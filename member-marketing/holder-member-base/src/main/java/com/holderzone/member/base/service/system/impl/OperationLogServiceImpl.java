package com.holderzone.member.base.service.system.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.base.entity.system.OperationLog;
import com.holderzone.member.base.mapper.system.OperationLogMapper;
import com.holderzone.member.base.service.system.OperationLogService;
import com.holderzone.member.base.transform.system.OperationLogTransform;
import com.holderzone.member.common.annotation.LogBusinessField;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.LogContentConstant;
import com.holderzone.member.common.dto.system.OperationLogDTO;
import com.holderzone.member.common.enums.exception.OperationLogExceptionEnum;
import com.holderzone.member.common.enums.log.LogBusinessEnum;
import com.holderzone.member.common.exception.OperationLogException;
import com.holderzone.member.common.qo.system.OperationLogQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.system.OperationLogVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @description 操作日志服务
 * @date 2021/8/25
 */
@Service
@Slf4j
@AllArgsConstructor
public class OperationLogServiceImpl extends HolderBaseServiceImpl<OperationLogMapper, OperationLog> implements OperationLogService {

    public Executor operationLogThreadExecutor;

    private final GuidGeneratorUtil guidGeneratorUtil;

    private static final String ENUM_SEPARATOR = ",";

    private static final String ENUM_VALUE_SEPARATOR = ":";

    @Override
    public List<OperationLogVO> listOperationLog(OperationLogQO request) {
        List<OperationLog> logList = this.list(new LambdaQueryWrapper<OperationLog>()
                .eq(OperationLog::getContentGuid, request.getContentGuid())
                .eq(OperationLog::getLogType, request.getLogType())
                .orderByDesc(OperationLog::getId));
        return OperationLogTransform.INSTANCE.OperationLogToVO(logList);
    }

    @Override
    public void dealBusinessLog(OperationLogDTO logDTO) {
        try {
            //校验参数
            verifyLogParams(logDTO);
            CompletableFuture.runAsync(() -> toDeal(logDTO),operationLogThreadExecutor);
        } catch (Exception e) {
            log.error("保存操作日志失败，错误信息：{}", e.getMessage(), e);
        }
    }

    private void toDeal(OperationLogDTO logDTO) {
        //多线程执行日志的保存
        Long guid = guidGeneratorUtil.getGuid(OperationLogServiceImpl.class.getSimpleName());
        try {
            List<OperationLog> operationLogList = new ArrayList<>();
            for (String contentGuid : logDTO.getContentGuidList()) {
                OperationLog operation = new OperationLog();
                operation.setOperationContent(mergeContent(logDTO.getExtraContent(),
                                buildOperationContent(logDTO.getOriginData(), logDTO.getChangeData(), logDTO.getField())))
                        .setContentGuid(contentGuid)
                        .setOperator(logDTO.getHeaderUserInfo() == null ? "" : logDTO.getHeaderUserInfo().getUserName())
                        .setPhoneNum(logDTO.getHeaderUserInfo() == null ? "" : logDTO.getHeaderUserInfo().getTel())
                        .setLogType(logDTO.getLogType().getCode())
                        .setOperationTime(LocalDateTime.now())
                        .setGuid(guid);
                //若操作日志为空则直接退出
                if (StringUtils.isEmpty(operation.getOperationContent())) {
                    break;
                }
                operationLogList.add(operation);
            }
            this.saveBatch(operationLogList);
        } catch (Exception e) {
            log.error("保存操作日志失败，错误信息：{}", e.getMessage());
        }
    }

    @Override
    public String compareData(Object originData, Object changeData, String field) {
        if (ObjectUtils.isEmpty(originData) && ObjectUtils.isEmpty(changeData)) {
            return null;
        }
        if (ObjectUtil.equal(originData, changeData)) {
            return null;
        }
        //删除
        if (ObjectUtils.isEmpty(changeData) && ObjectUtils.isNotEmpty(originData)) {
            return String.format(LogContentConstant.DELETE_CONTENT, field, originData);
        }
        //新增
        if (ObjectUtils.isEmpty(originData) && ObjectUtils.isNotEmpty(changeData)) {
            return String.format(LogContentConstant.ADD_CONTENT, field, changeData);
        }
        //修改
        return String.format(LogContentConstant.UPDATE_CONTENT, field, originData, changeData);
    }


    private String mergeContent(String extraContent, String operationContent) {
        if (StringUtils.isEmpty(extraContent) && StringUtils.isEmpty(operationContent)) {
            return null;
        }
        if (StringUtils.isEmpty(operationContent)) {
            return extraContent;
        }
        if (StringUtils.isEmpty(extraContent)) {
            return operationContent;
        }
        return operationContent +
                extraContent;
    }

    public String buildOperationContent(Object originData, Object changeData, String field) throws IllegalAccessException{
        //如果指定了记录的字段名称则直接比较数据
        if (StringUtils.isNotEmpty(field)) {
            return compareData(originData, changeData, field);
        }
        if (originData == null && changeData == null) {
            return Strings.EMPTY;
        }
        //若未指定字段则通过反射去获取字段信息
        StringBuilder content = new StringBuilder();
        if (originData == null) {
            return Strings.EMPTY;
        }
        Field[] originFields = originData.getClass().getDeclaredFields();
        Map<String, Object> originMap = new HashMap<>(originFields.length);
        forOriginData(originData, originFields, originMap);
        Field[] changeFields = changeData.getClass().getDeclaredFields();
        forChangeData(changeData, content, originMap, changeFields);
        originMap.clear();
        return content.toString();
    }

    private void forChangeData(Object changeData, StringBuilder content, Map<String, Object> originMap, Field[] changeFields) throws IllegalAccessException {
        for (Field f : changeFields) {
            LogBusinessField ann = f.getAnnotation(LogBusinessField.class);
            String fieldName = f.getName();
            f.setAccessible(true);
            //如果字段上面有日志统计的注解
            if (ann != null) {
                extracted(changeData, content, originMap, f, ann, fieldName);
            }

        }
    }

    private static void forOriginData(Object originData, Field[] originFields, Map<String, Object> originMap) throws IllegalAccessException {
        for (Field f : originFields) {
            String fieldName = f.getName();
            ReflectionUtils.makeAccessible(f);
            Object fieldValue = f.get(originData);
            originMap.put(fieldName, fieldValue);
        }
    }

    private void extracted(Object changeData, StringBuilder content, Map<String, Object> originMap, Field f, LogBusinessField ann, String fieldName) throws IllegalAccessException {
        Object origin = originMap.get(fieldName);
        Object change = f.get(changeData);
        //对象单独操作
        final String fieldType = ann.fieldType();
        if (StringUtils.isNotEmpty(fieldType)) {
            //递归生成日志
            final String s = childLog(origin, change, ann);
            content.append(s);
            return;
        }
        //若存在枚举类
        String enumName = ann.enumName();
        if (StringUtils.isNotEmpty(enumName)) {
            String[] enums = enumName.split(ENUM_SEPARATOR);
            for (String e : enums) {
                String[] split = e.split(ENUM_VALUE_SEPARATOR);
                if (isEqual(change, split)) {
                    change = split[1];
                }
                if (isEqual(origin, split)) {
                    origin = split[1];
                }

            }
        }
        //需要比较字段的名称
        String s = compareData(origin, change, ann.name());
        content.append(s == null ? "" : s);
    }

    private static boolean isEqual(Object change, String[] split) {
        return ObjectUtil.equal(change == null ? "" : change.toString(), split[0]);
    }

    /**
     * 递归
     *
     * @param originData
     * @param changeData
     */
    private String childLog(Object originData, Object changeData, LogBusinessField ann) throws IllegalAccessException {
        if (ObjectUtils.isEmpty(originData) && ObjectUtils.isEmpty(changeData)) {
            return "";
        }

        if (ObjectUtil.equal(originData, changeData)) {
            return "";
        }
        //新增
        if (ObjectUtils.isEmpty(originData) && ObjectUtils.isNotEmpty(changeData)) {
            final String fieldValues = getFieldValues(changeData, ann.fieldType(), ann.enumName());
            return String.format(LogContentConstant.ADD_CONTENT, ann.name(), fieldValues);
        }
        //删除
        if (ObjectUtils.isEmpty(changeData) && ObjectUtils.isNotEmpty(originData)) {
            final String fieldValues = getFieldValues(originData, ann.fieldType(), ann.enumName());
            return String.format(LogContentConstant.DELETE_CONTENT, ann.name(), fieldValues);
        }
        final String originFieldValues = getFieldValues(originData, ann.fieldType(), ann.enumName());
        final String changeFieldValues = getFieldValues(changeData, ann.fieldType(), ann.enumName());
        if (originFieldValues.equals(changeFieldValues)) {
            return "";
        }
        //需要比较字段的名称
        return String.format(LogContentConstant.UPDATE_CONTENT, ann.name(), originFieldValues, changeFieldValues);
    }

    private String getFieldValues(Object data, String fieldType, String parentEnumName) throws IllegalAccessException {
        //格式 1. "基础类型"  2. map和obj 3是 list(包含基础类型和map\obj)
        LogBusinessEnum logBusinessEnum = LogBusinessEnum.getEnumByDes(fieldType);
        switch (logBusinessEnum) {
            // list-base
            case LIST_BASE_TYPE:
                return JSONUtil.toJsonStr(data);

            // list
            case BUSINESS_LIST_TYPE:
                return getListOrMapContent(data, parentEnumName);

            // map
            case BUSINESS_MAP_TYPE:
                return getListOrMapContent(data, parentEnumName);

            // obj
            case BUSINESS_OBJ_TYPE:
                return getObjContent(data);

            // []
            case BUSINESS_ARRAY_TYPE:
                return getArrayContent((Object[]) data, parentEnumName);
            default:
                return data.toString();
        }
    }

    private String getObjContent(Object data) throws IllegalAccessException {
        StringBuilder content = new StringBuilder();
        Field[] originFields = data.getClass().getDeclaredFields();
        for (Field f : originFields) {
            f.setAccessible(true);
            LogBusinessField ann = f.getAnnotation(LogBusinessField.class);
            if (ann != null && ObjectUtil.isNotNull(f.get(data))) {
                String enumName = ann.enumName();
                if ("".equals(ann.fieldType())) {
                    Object o = f.get(data);
                    //若存在枚举类
                    Object nameInfo = getEnumName(enumName, o);
                    //todo 多个字段如何拼接？？ k:v , append(ENUM_SEPARATOR)
                    content.append(ann.name()).append(nameInfo).append(ENUM_SEPARATOR);
                } else {
                    //递归对象
                    content.append(getFieldValues(f.get(data), ann.fieldType(), enumName));
                }
            }
        }
        return delEndDot(content);
    }

    private String getListOrMapContent(Object data, String parentEnumName) throws IllegalAccessException {
        StringBuilder content = new StringBuilder();
        final List<Object> list = (List) data;
        for (Object o : list) {
            //todo 循环遍历对象
            content.append(getFieldValues(o, "obj", parentEnumName)).append(ENUM_SEPARATOR);
        }
        return delEndDot(content);
    }

    /**
     * 基础数据类型集合内容转换（例如：string[]、List<String>、List<基础数据类型包装类>）
     * 执行步骤：1.获取集合或者数组信息
     * 2.判断是否需要枚举转换?转换成枚举名称：使用集合或者数据原生数据
     *
     * @param '[]'           说明是String[]、或者List<基础数据类型包装类>
     * @param parentEnumName 枚举    code:name,code:name
     * @return 操作结果
     */
    private String getArrayContent(Object[] dataArray, String parentEnumName) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isEmpty(parentEnumName)) {
            for (Object data : dataArray) {
                sb.append(data).append(ENUM_SEPARATOR);
            }
            return delEndDot(sb);
        }
        for (Object data : dataArray) {
            Object enumName = getEnumName(parentEnumName, data);
            sb.append(enumName).append(ENUM_SEPARATOR);
        }
        return delEndDot(sb);
    }

    /**
     * 去掉末尾逗号
     * @param sb
     * @return
     */
    private String delEndDot(StringBuilder sb){
        final String s = sb.toString();
        if (s.endsWith(ENUM_SEPARATOR)) {
            return s.substring(0,s.length()-1);
        }
        return s;
    }

    /**
     * 获取日志枚举名称
     *
     * @param enumName 枚举信息
     * @param data     转换的数据对象
     * @return 枚举名称
     */
    private Object getEnumName(String enumName, Object data) {
        if (StringUtils.isEmpty(enumName)) {
            return data;
        }
        String[] enums = enumName.split(ENUM_SEPARATOR);
        for (String s : enums) {
            String[] split = s.split(ENUM_VALUE_SEPARATOR);
            if (isEqual(data, split)) {
                data = split[1];
                return data;
            }
        }
        return data;
    }

    private void verifyLogParams(OperationLogDTO logDTO) {
        if (ObjectUtil.isNull(logDTO)) {
            throw new OperationLogException(OperationLogExceptionEnum.LOG_PARAMS_NULL);
        }
        if (CollUtil.isEmpty(logDTO.getContentGuidList())) {
            throw new OperationLogException(OperationLogExceptionEnum.BUSINESS_GUID_NULL);
        }
        if (ObjectUtil.isNull(logDTO.getLogType())) {
            throw new OperationLogException(OperationLogExceptionEnum.LOG_TYPE_NULL);
        }
    }
}
