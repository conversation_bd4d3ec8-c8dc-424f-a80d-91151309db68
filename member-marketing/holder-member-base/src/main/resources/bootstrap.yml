server:
  port: 8083
spring:
  application:
    name: member-base
  profiles:
    active: dev
  messages:
    basename: i18n/messages
management:
  endpoints:
    web:
      exposure:
        include: 'health,info,metrics,prometheus'
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: ${spring.application.name}
      service: member-base
