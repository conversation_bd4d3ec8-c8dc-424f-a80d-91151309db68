package com.holderzone.member.common.module.marketing.coupon.use.qo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.member.common.dto.coupon.MemberCouponLinkDTO;
import com.holderzone.member.common.entity.coupon.HsaMemberCouponLink;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 核销标记使用
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CouponMarkUseQO implements Serializable {


    private static final long serialVersionUID = 725728891084387542L;
    /**
     * 主体
     */
    @JsonIgnore
    private String operSubjectGuid;

    /**
     * 核销门店
     * 需求：门店可为空
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 订单
     */
    private String orderNumber;

    /**
     * 券
     */
    @NotBlank(message = "优惠券不能为空")
    private String couponGuid;

    /**
     * 抵扣优惠金额
     * （优惠券抵扣金额）
     */
    @JsonIgnore
    private BigDecimal discountAmount;

    private String couponCode;

    private CouponActivityDTO couponActivityDTO;

    private MemberCouponLinkDTO memberCouponLinkDTO;

}
