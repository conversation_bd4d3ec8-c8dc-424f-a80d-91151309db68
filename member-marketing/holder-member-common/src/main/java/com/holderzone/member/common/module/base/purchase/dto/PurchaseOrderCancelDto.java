package com.holderzone.member.common.module.base.purchase.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 消费订单取消
 *
 * <AUTHOR>
 * @date 2023/12/4
 * @since 1.8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PurchaseOrderCancelDto implements Serializable {

    private static final long serialVersionUID = 4432117346679859924L;
    /**
     * 运营主体guid
     */
    @ApiModelProperty(value = "运营主体guid")
    private String operSubjectGuid;

    /**
     * 订单编号（外部订单编号）
     */
    @ApiModelProperty(value = "订单编号（外部订单编号）")
    private String orderNumber;
}
