package com.holderzone.member.common.module.marketing.purchase.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 限量抢购活动状态
 *
 * <AUTHOR>
 * @date 2023/11/27
 **/
@Getter
@AllArgsConstructor
public enum PurchaseStateEnum {

    NONE(0, "无"),

    /**
     * 未发布：创建、编辑后
     */
    UN_PUBLISHED(1, "未发布"),

    /**
     * 不展示 : 活动正常状态
     */
    PUBLISHED(2, "已发布/已开启"),

    /**
     * 发布后：时间未到
     */
    UN_START(3, "未开始"),

    /**
     * 发布后：未结束
     */
    UNDER_WAY(4, "进行中"),

    /**
     * 手动暂停，且未结束
     */
    STOP(5, "已暂停"),

    /**
     * 发布后：结束
     */
    OVER(6, "已结束");

    private final int code;

    private final String des;

    public static PurchaseStateEnum get(Integer code) {
        if (Objects.isNull(code)) {
            return NONE;
        }
        for (PurchaseStateEnum anEnum : PurchaseStateEnum.values()) {
            if (Objects.equals(anEnum.code, code)) {
                return anEnum;
            }
        }
        return NONE;
    }

    /**
     * 实际状态
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param state     状态
     * @return 实际状态
     */
    public static Integer getState(LocalDateTime startTime, LocalDateTime endTime, Integer state) {
        if (Objects.isNull(state)) {
            return PurchaseStateEnum.NONE.getCode();
        }
        LocalDateTime now = LocalDateTime.now();
        //已发布
        if (state == PUBLISHED.code) {
            //未开始 or 进行中
            final int code = startTime.isBefore(now)
                    ? UNDER_WAY.code
                    : UN_START.code;
            //进行中 or 已结束
            return code == UNDER_WAY.code && endTime.isBefore(now)
                    ? OVER.code
                    : code;
        }
        if (state == STOP.code) {
            //暂停到已结束
            return endTime.isBefore(now) ? OVER.code : state;
        }
        return state;
    }

    /**
     * 可暂停状态
     *
     * @param code 状态编码
     * @return 是否
     */
    public static boolean isStop(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        return code.equals(PUBLISHED.code) || code.equals(UN_START.code) || code.equals(UNDER_WAY.code);
    }
}
