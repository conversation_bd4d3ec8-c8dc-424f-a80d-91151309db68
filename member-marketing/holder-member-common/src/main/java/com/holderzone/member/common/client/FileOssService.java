package com.holderzone.member.common.client;

import cn.hutool.core.io.FileUtil;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.framework.security.Encodes;
import com.holderzone.member.common.dto.excel.FileDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

/**
 * 阿里云文件
 *
 * <AUTHOR>
 * @date 2021/08/17 14:53
 */
@Slf4j
@Component
//@Profile("file-oss-save")
public class FileOssService {
    @Autowired
    @Lazy
    OssClient ossClient;

    public String upload(FileDto fileDto) {
        String content = fileDto.getFileContent();
        byte[] bytes = Encodes.decodeBase64(content);
        return ossClient.upload(fileDto.getFileName(), bytes);
    }

    public void delete(String fileUrl) {
        ossClient.delete(getFileName(fileUrl));
    }

    public File getFileByName(String fileUrl) {
        String fileName = getFileName(fileUrl);
        try {
            URL url = new URL(fileUrl);
            InputStream inputStream = url.openStream();
            if (inputStream != null) {
                int index = fileName.lastIndexOf('.');
                String suffix = fileName.substring(index);
                fileName = fileName.replace("." + suffix, "");
                final File tempFile = File.createTempFile(fileName + File.separator, suffix);
                return FileUtil.writeFromStream(inputStream, tempFile);
            }
        } catch (IOException e) {
            log.error("fileUrl transfer file error, e:", e);
        }
        return null;
    }

    /**
     * 获取文件名称
     */
    private String getFileName(String fileUrl) {
        String url = fileUrl.split("//")[1];
        int position = url.indexOf("/");
        return url.substring(position + 1);
    }

}
