package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.vo.permission.PerMerchantLogoVO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/3/23 下午4:56
 * @description 好搭档平台端远程调用
 */
@Component
@FeignClient(name = FilterConstant.MEMBER_PARTNER_APPLET, fallbackFactory = PartnerAppletFeign.ServiceFallBack.class)
public interface PartnerAppletFeign {


    @GetMapping("/applet/merchant/un_auth_phone/guids")
    List<String> getUnAuthGuidsByMerchantPhone(@RequestParam("merchantPhone") String merchantPhone);

    @PostMapping("/applet/reserve/auto/cancel")
    void autoCancelReserve(@RequestParam("cancelTime") LocalDateTime cancelTime);

    @PostMapping("/applet/merchant/medium/get_merchant_status")
    List<PerMerchantLogoVO> getMerchantByStatus(@RequestBody List<String> merchantGuid);

    /**
     * 预定核销超时自动取消
     */
    @PostMapping("/applet/reserve/verify_timeout_auto_cancel")
    void verifyTimeoutAutoCancel();

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<PartnerAppletFeign> {

        @Override
        public PartnerAppletFeign create(Throwable throwable) {
            return new PartnerAppletFeign() {

                @Override
                public List<String> getUnAuthGuidsByMerchantPhone(String merchantPhone) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getUnAuthGuidsByMerchantPhone", merchantPhone,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void autoCancelReserve(LocalDateTime cancelTime) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "autoCancelReserve", cancelTime,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PerMerchantLogoVO> getMerchantByStatus(List<String> merchantGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getMerchantByStatus", merchantGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void verifyTimeoutAutoCancel() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "verifyTimeoutAutoCancel", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
