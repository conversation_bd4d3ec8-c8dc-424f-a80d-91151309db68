package com.holderzone.member.common.module.settlement.rule.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountItemEnum;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 结算规则已选优惠项
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementRuleDiscountVO implements Serializable {


    private static final long serialVersionUID = -4454540761399563042L;

    /**
     * 优惠项guid
     */
    private String guid;

    /**
     * 子项数量
     */
    private Integer childNum;

    /**
     * 优惠类型：0 单品级优惠 1订单级优惠 2资产优惠
     *
     * @see SettlementDiscountTypeEnum
     */
    private Integer discountType;

    /**
     * 优惠项
     *
     * @see SettlementDiscountItemEnum
     */
    private Integer discountItem;

    /**
     * 优惠项
     *
     * @see SettlementDiscountOptionEnum
     */
    private Integer discountOption;

    /**
     * 优惠guid,界面显示为：优惠id
     */
    private String discountGuid;

    /**
     * 优惠名称
     */
    private String discountName;

    /**
     * 优惠力度
     */
    private String discountDynamic;

    /**
     * 排序
     */
    @JsonIgnore
    private Integer rank;
    /**
     * 数量
     */
    private Integer discountNum;


    /**
     * 是否可叠加
     */
    @JsonIgnore
    private Integer  isAppend;

    public void nullDiscountGuid(List<Integer> options) {
        if (options.contains(discountOption)) {
            //不返回
            this.discountGuid = null;
        }
    }
}
