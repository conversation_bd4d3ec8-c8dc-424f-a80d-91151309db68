package com.holderzone.member.common.module.settlement.rule.vo;

import com.holderzone.member.common.module.settlement.rule.dto.SettlementRuleBaseDTO;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementRuleStoreDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 结算规则已选优惠项
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Accessors(chain = true)
public class SettlementRuleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 基础规则
     */
    private SettlementRuleBaseDTO baseRule;

    /**
     * 应用门店
     */
    private List<SettlementRuleStoreDTO> applicableStore;

    /**
     * 可叠加优惠项
     */
    private List<SettlementRuleDiscountTreeVO> appendDiscounts;

}
