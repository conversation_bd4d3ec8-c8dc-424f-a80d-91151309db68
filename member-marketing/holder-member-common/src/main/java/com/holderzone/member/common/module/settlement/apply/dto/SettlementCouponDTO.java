package com.holderzone.member.common.module.settlement.apply.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 优惠券
 *
 * <AUTHOR>
 * @date 2023/10/28
 * @since 1.8
 */
@Data
@AllArgsConstructor
public class SettlementCouponDTO {

    /**
     * 券码codes
     */
    private List<String> couponCodes;


    /**
     * 可用数量
     */
    private int couponLimitNum;


    /**
     * 数量-1
     */
    public void couponLimitNumSubtract1() {
        this.couponLimitNum = couponLimitNum - 1;
    }
}
