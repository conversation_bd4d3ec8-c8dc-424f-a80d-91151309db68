package com.holderzone.member.common.module.marketing.purchase.qo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 限购活动列表查询
 *
 * <AUTHOR>
 * @date 2023/12/01
 **/
@Data
@Accessors(chain = true)
public class PurchaseApplyOrderQO implements Serializable {

    private static final long serialVersionUID = 6480851653375152230L;
    /**
     * purchaseGuid
     */
    private List<String> purchaseGuid;

}
