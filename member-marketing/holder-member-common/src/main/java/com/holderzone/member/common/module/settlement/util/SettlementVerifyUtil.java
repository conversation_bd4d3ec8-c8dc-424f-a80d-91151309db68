package com.holderzone.member.common.module.settlement.util;


import com.holderzone.member.common.exception.MemberSettlementException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collection;

/**
 * 参数校验
 *
 * <AUTHOR>
 */
public class SettlementVerifyUtil {

    private SettlementVerifyUtil(){
        //default
    }

    public static void isNull(Object object, String message) {
        if (object == null) {
            throw new MemberSettlementException(message);
        }
    }

    public static void isBlank(String str, String message) {
        if (StringUtils.isBlank(str)) {
            throw new MemberSettlementException(message);
        }
    }

    public static void isTrue(boolean error, String message) {
        if (error) {
            throw new MemberSettlementException(message);
        }
    }

    public static void isEmpty(Collection<?> list, String message) {
        if (CollectionUtils.isEmpty(list)) {
            throw new MemberSettlementException(message);
        }
    }
}
