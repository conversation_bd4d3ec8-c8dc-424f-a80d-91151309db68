package com.holderzone.member.common.support;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.feign.MemberSettlementFeign;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementDiscountDTO;
import lombok.AllArgsConstructor;
import lombok.extern.java.Log;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 结算台支撑类
 *
 * <AUTHOR>
 * @date 2023/9/7
 * @since 1.8
 */
@Component
@AllArgsConstructor
@Slf4j
public class SettlementSupport {

    private final MemberSettlementFeign settlementFeign;

    /**
     * 推送优惠项
     *
     * @param dto 入参
     */
    public void syncSettlementDiscount(SettlementDiscountDTO dto) {
        HeaderUserInfo userInfo = new HeaderUserInfo().setOperSubjectGuid(dto.getOperSubjectGuid());
        ThreadLocalCache.put(JacksonUtils.writeValueAsString(userInfo));
        log.info("推送到结算台数据,{}",JacksonUtils.writeValueAsString(dto));
        settlementFeign.syncSettlementDiscount(dto);
    }
}
