package com.holderzone.member.common.module.settlement.rule.qo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountItemEnum;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 结算规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class SettlementDiscountQO extends PageDTO implements Serializable {


    private static final long serialVersionUID = -9190266794032720049L;

    /**
     * 主体
     */
    @JsonIgnore
    private String operSubjectGuid;

    /**
     * 关键字
     * 优惠券ID、名称模糊查询
     */
    private String keywords;

    /**
     * 优惠项
     *
     * @see SettlementDiscountItemEnum
     */
    private Integer discountItem;

    /**
     * 部分类型关键字不用查询discountGuid
     */
    @JsonIgnore
    private List<Integer> notShowGuidOptions;

    public void buildKeywordsQuery() {
        if (StringUtils.isNoneBlank(keywords)) {
            //部分类型关键字不用查询discountGuid
            this.notShowGuidOptions = SettlementDiscountOptionEnum.noShowDiscountGuid();
        }
    }
}
