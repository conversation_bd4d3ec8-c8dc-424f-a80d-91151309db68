package com.holderzone.member.common.assembler;

import com.holderzone.member.common.dto.activity.VolumeInfoUpdateDTO;
import com.holderzone.member.common.dto.certificate.GiftInfoDTO;
import com.holderzone.member.common.vo.activity.IslandCouponDTO;
import com.holderzone.member.common.vo.activity.IslandPupilCouponDTO;
import com.holderzone.member.common.vo.base.ResponseOperationLabel;
import com.holderzone.member.common.vo.base.ResponseOperationLabelList;
import com.holderzone.member.common.vo.certificate.VolumeInfoVO;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/28
 * @description
 */
public class CertifiedAssembler {

    /**
     * 背景图
     */
    private static final String BACKGROUND_PICTURE = "https://cydl1000.holderzone.com/20231206183423.png";

    /**
     * 按钮色
     */
    private static final String BUTTON_COLOR = "#FF4D48";

    private CertifiedAssembler() {
    }

    public static List<IslandCouponDTO> toIslandCouponDTO(List<VolumeInfoVO> infoVOList) {
        if (CollectionUtils.isEmpty(infoVOList)) {
            return Collections.emptyList();
        }
        List<IslandCouponDTO> list = new ArrayList<>();
        for (VolumeInfoVO infoVO : infoVOList) {
            IslandCouponDTO islandCouponDTO = new IslandCouponDTO();
            BeanUtils.copyProperties(infoVO, islandCouponDTO);
            islandCouponDTO.setId(infoVO.getGuid());
            islandCouponDTO.setName(infoVO.getVolumeName());
            islandCouponDTO.setGuid(infoVO.getGuid());
            list.add(islandCouponDTO);
        }
        return list;
    }

    public static List<IslandPupilCouponDTO> toIslandPupilCouponDTO(List<VolumeInfoUpdateDTO> infoVOList, List<GiftInfoDTO> giftInfoList) {
        List<IslandPupilCouponDTO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(infoVOList)) {
            return Collections.emptyList();
        }
        Map<String, VolumeInfoUpdateDTO> giftInfoDTOMap = infoVOList.stream().collect(Collectors.toMap(VolumeInfoUpdateDTO::getGuid, Function.identity(), (v1, v2) -> v1));

        for (GiftInfoDTO giftInfoDTO : giftInfoList) {
            VolumeInfoUpdateDTO volumeInfoUpdateDTO = giftInfoDTOMap.get(giftInfoDTO.getGiveawayGuid());

            if (Objects.nonNull(volumeInfoUpdateDTO)) {
                IslandPupilCouponDTO islandCouponDTO = new IslandPupilCouponDTO();
                BeanUtils.copyProperties(volumeInfoUpdateDTO, islandCouponDTO);
                islandCouponDTO.setId(volumeInfoUpdateDTO.getGuid());
                islandCouponDTO.setName(volumeInfoUpdateDTO.getVolumeName());
                islandCouponDTO.setCouponNumber(giftInfoDTO.getGiveawayNum());
                list.add(islandCouponDTO);
            }
        }
        return list;
    }

    public static List<ResponseOperationLabel> toResponseOperationLabelList(List<ResponseOperationLabelList> labelList) {
        List<ResponseOperationLabel> responseList = new ArrayList<>();
        labelList.forEach(label -> {
            ResponseOperationLabel responseLabel = new ResponseOperationLabel();
            responseLabel.setGuid(label.getGuid());
            responseLabel.setLabelName(label.getLabelName());
            responseLabel.setLabelType(label.getLabelType());
            responseLabel.setMemberNum(label.getMemberNum());
            responseLabel.setGmtCreate(label.getGmtCreate());
            responseList.add(responseLabel);
        });
        return responseList;
    }

    public static List<IslandPupilCouponDTO> responseVolumeInfoList2IslandPupilCouponDTOList(List<VolumeInfoUpdateDTO> responseVolumeInfoList) {
        List<IslandPupilCouponDTO> islandPupilCouponDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(responseVolumeInfoList)) {
            return islandPupilCouponDTOList;
        }
        for (VolumeInfoUpdateDTO responseVolume : responseVolumeInfoList) {
            IslandPupilCouponDTO couponDTO = new IslandPupilCouponDTO();
            couponDTO.setId(responseVolume.getGuid());
            couponDTO.setName(responseVolume.getVolumeName());
            couponDTO.setDenomination(responseVolume.getDenomination());
            couponDTO.setUseThreshold(responseVolume.getUseThreshold());
            couponDTO.setUseThresholdFull(responseVolume.getUseThresholdFull());
            couponDTO.setVolumeType(responseVolume.getVolumeType());
            couponDTO.setVolumeTypeName(responseVolume.getVolumeTypeName());
            couponDTO.setValidityPeriodType(0);
            couponDTO.setAtferEffectiveDay(responseVolume.getAtferEffectiveDay());
            couponDTO.setValidityPeriodDay(responseVolume.getValidityPeriodDay());
            couponDTO.setStartValidityPeriod(responseVolume.getStartValidityPeriod());
            couponDTO.setEndValidityPeriod(responseVolume.getEndValidityPeriod());
            couponDTO.setCouponNumber(responseVolume.getCouponNumber());
            couponDTO.setAtferEffectiveUnit(responseVolume.getAtferEffectiveUnit());
            couponDTO.setValidityUnit(responseVolume.getValidityUnit());
            islandPupilCouponDTOList.add(couponDTO);
        }

        return islandPupilCouponDTOList;
    }

}
