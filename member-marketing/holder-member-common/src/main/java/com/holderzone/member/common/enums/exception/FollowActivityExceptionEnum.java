package com.holderzone.member.common.enums.exception;

import com.holderzone.member.common.enums.ResponseBase;

/**
 * <AUTHOR>
 */

public enum FollowActivityExceptionEnum implements ResponseBase {

    /**
     * 随行红包活动未查询到
     */
    FOLLOW_ACTIVITY_NOT_FOUND(1, "随行红包活动未查询到"),

    /**
     * 随行红包活动未发布
     */
    FOLLOW_ACTIVITY_NOT_PUBLISH(2, "随行红包活动未发布,不能更改状态"),

    NOT_FOUND_STORE_INFO(3, "未查询到门店信息"),

    QUERY_STORE_ERROR(4, "查询门店报错"),

    QUERY_TABLE_ERROR(5, "查询桌台报错"),

    ;

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误描述
     */
    private final String des;

    FollowActivityExceptionEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getDes() {
        return this.des;
    }
}
