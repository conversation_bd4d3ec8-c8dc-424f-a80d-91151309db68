package com.holderzone.member.common.constant;

import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.SystemEnum;

import java.util.Optional;

/**
 * <AUTHOR>
 * @description 线程缓存
 * @date 2021/8/12
 */
public final class ThreadLocalCache {
    private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<>();

    public static void put(String str) {
        THREAD_LOCAL.set(str);
    }

    public static String get() {
        return THREAD_LOCAL.get();
    }

    public static HeaderUserInfo getHeaderUserInfo() {
        return Optional.ofNullable(THREAD_LOCAL.get()).map(json -> JSON.parseObject(json, HeaderUserInfo.class)).orElse(
                new HeaderUserInfo());
    }

    /**
     * 获取运营主体或联盟GUID
     *
     * @return 运营主体或联盟GUID
     */
    public static String getOperSubjectGuid() {
        return getHeaderUserInfo().getOperSubjectGuid();
    }

    /**
     * 获取会员平台的企业Guid
     *
     * @return 会员平台的企业Guid
     */
    public static String getEnterpriseGuid() {

        return getHeaderUserInfo().getEnterpriseGuid();
    }

    public static String getUserGuid() {
        return getHeaderUserInfo().getUserGuid();
    }

    public static String getUserName() {
        return getHeaderUserInfo().getUserName();
    }

    public static void remove() {
        THREAD_LOCAL.remove();
    }

    public static String getOperatorTelName() {
        return getHeaderUserInfo().getUserName() + StringConstant.STR_BIAS + getHeaderUserInfo().getTel();
    }

    public static String getOperatorName() {
        return getHeaderUserInfo().getUserName();
    }

    public static Integer getSource(){
        return getHeaderUserInfo().getSource();
    }

    public static Integer getSystem(){
        return getHeaderUserInfo().getSystem();
    }

    /**
     * 好搭档
     *
     * @return
     */
    public static boolean isPartner() {
        return SystemEnum.PARTNER.getCode() == getHeaderUserInfo().getSystem();
    }


}
