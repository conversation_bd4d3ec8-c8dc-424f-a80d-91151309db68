package com.holderzone.member.common.module.settlement.apply.dto;

import com.holderzone.member.common.entity.activity.ApplyRecordCommodity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * 订单优惠锁定
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementOrderLockDTO implements Serializable {

    private static final long serialVersionUID = 1319038445331907520L;
    /**
     * 订单入参
     */
    @ApiModelProperty("锁定订单相关入参")
    @NotNull(message = "订单入参必填！")
    private SettlementLockedOrderInfoDTO orderInfo;
    /**
     * 已选优惠： 计算使用
     * 更新时 可为空
     * todo 要按使用顺序传递过来
     */
    @ApiModelProperty("已选优惠")
    private List<SettlementLockedDiscountReqDTO> checkDiscountList;

    /**
     * 参与的商品列表
     */
    private List<ApplyRecordCommodity> limitSpecialsRecordCommodities;

    private Integer codeType;

    /**
     * 校验选中参数
     */
    public void validateCheck() {
        orderInfo.validate();
        
    }


}
