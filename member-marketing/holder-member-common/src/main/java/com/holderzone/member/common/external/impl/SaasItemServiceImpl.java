package com.holderzone.member.common.external.impl;

import cn.hutool.core.collection.CollUtil;
import com.aimilin.bean.ExcelResult;
import com.aimilin.bean.ExcelType;
import com.aimilin.utils.ExcelWriteUtils;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.common.assembler.SaasItemAssembler;
import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.dto.crm.CrmCommodityReqDTO;
import com.holderzone.member.common.dto.excel.*;
import com.holderzone.member.common.dto.grade.CommodityInfoDTO;
import com.holderzone.member.common.dto.grade.StrategyInfoDTO;
import com.holderzone.member.common.dto.member.*;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.base.ChannelEnum;
import com.holderzone.member.common.enums.base.ItemTypeEnum;
import com.holderzone.member.common.enums.growth.CommodityComboTypeEnum;
import com.holderzone.member.common.enums.growth.CommodityStatusEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.specials.SpecialsTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalItemService;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.feign.SaasStoreFeign;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.qo.base.CommodityBasePageQO;
import com.holderzone.member.common.qo.grade.CommodityInfoQO;
import com.holderzone.member.common.qo.grade.GradeCommodityBasePageQO;
import com.holderzone.member.common.qo.mall.QueryProductCategoryCrmQO;
import com.holderzone.member.common.qo.tool.CommodityDetailsQO;
import com.holderzone.member.common.util.excel.ItemUploadExcelUtil;
import com.holderzone.member.common.util.verify.BigDecimalUtil;
import com.holderzone.member.common.vo.base.CommodityBaseTypeVO;
import com.holderzone.member.common.vo.base.CommodityBaseVO;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.feign.MerchantModel;
import com.holderzone.member.common.vo.grade.StrategyInfoVO;
import com.holderzone.member.common.vo.mall.ProductCrmCategoryVO;
import com.holderzone.member.common.vo.tool.CommodityDetailsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/20
 * @description 餐饮云
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SaasItemServiceImpl implements ExternalItemService {

    public static final String CRLF = "\r\n";
    public static final String ONE = "^-?\\d+(\\.\\d{1})?$";
    public static final String TWO = "^(([1-9]\\d*)|(0))(\\.\\d{0,2})?$";
    public static final String DISCOUNT_AMOUNT_ERROR = "【折扣力度】填写错误";
    public static final String LIMIT_ERROR = "【优惠限购】填写错误";

    private final SaasStoreFeign saasStoreFeign;

    private final FileOssService fileOssService;

    private final CrmFeign crmFeign;

    @Override
    public List<ResArrayStrategyBase> getOperatingStrategy(QueryArrayShopBase queryStoreBasePage) {
        return Collections.emptyList();
    }

    @Override
    public List<ResCategoryBase> queryCategoryByStrategy(QueryArrayShopBase queryStoreBasePage) {
        return Collections.emptyList();
    }

    @Override
    public Pair<Integer, List<ResCommodityBase>> pageStrategyCommodity(QueryCommodityBasePage commodityBasePage) {
        return null;
    }

    @Override
    public List<ResCommodityBase> listCommodityBase(QueryArrayShopBase queryStoreBasePage) {

        return Collections.emptyList();
    }

    /**
     * 根据商品id查询商品信息
     *
     * @param query 查询条件
     * @return 商品列表
     */
    @Override
    public List<ResCommodityBase> listCommodityByDetail(QueryArrayShopBase query) {
        if (Objects.isNull(query) || CollectionUtils.isEmpty(query.getCommodityIdList())) {
            log.warn("参数为空");
            return Lists.newArrayList();
        }
        ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setDataList(query.getCommodityIdList());
        log.info("[saas][根据商品id查询商品信息]listDTO={}", JacksonUtils.writeValueAsString(listDTO));
        MerchantModel<List<ItemWebRespDTO>> itemModel = saasStoreFeign.queryItemByGuid(listDTO);
        log.info("[saas][根据商品id查询商品信息]webRespDTOList={}", JacksonUtils.writeValueAsString(itemModel));
        if (ObjectUtils.isEmpty(itemModel)) {
            log.warn("返回结果为空");
            return Lists.newArrayList();
        }
        return SaasItemAssembler.webRespDTOList2ResCommodityBaseList(itemModel.getTData());
    }

    @Override
    public Pair<Integer, List<ResGradeCommodityBase>> listStoreCommodityPage(GradeCommodityBasePageQO pageQO) {
        CommodityBasePageQO query = new CommodityBasePageQO();
        query.setCurrentPage(NumberConstant.NUMBER_1);
        query.setPageSize(NumberConstant.NUMBER_9999);
        query.setCommityIds(pageQO.getCommityIds());
        query.setCommodityType(Objects.nonNull(pageQO.getType()) ? Integer.valueOf(pageQO.getType()) : null);
        query.setCategoryId(pageQO.getCategoryId());
        Page<CommodityBaseVO> commodityBaseVOPage = pageCommodityBase(query);

        // 转换结果
        return Optional.ofNullable(commodityBaseVOPage)
                .map(page -> {
                    List<ResGradeCommodityBase> commodityList = page.getData().stream()
                            .map(vo -> {
                                ResGradeCommodityBase base = new ResGradeCommodityBase();
                                base.setCommodityId(vo.getCommodityId());
                                base.setName(vo.getCommodityName());
                                base.setPrice(vo.getCommodityPrice());
                                base.setState(CommodityStatusEnum.COMMODITY_UP.getDes());
                                base.setType(CommodityComboTypeEnum.getNameByCode(vo.getCommodityType()));
                                base.setCode(vo.getCommodityCode());
                                base.setSystem(SystemEnum.REPAST.name());
                                return base;
                            })
                            .collect(Collectors.toList());
                    return Pair.of(page.getData().size(), commodityList);
                })
                .orElse(Pair.of(NumberConstant.NUMBER_0, Collections.emptyList()));
    }

    /**
     * 门店系统获取当前运营主体对应门店的品牌库商品分类数据
     */
    @Override
    public Page<CommodityBaseVO> pageCommodityBase(CommodityBasePageQO pageQO) {
        if (Objects.isNull(pageQO)) {
            log.warn("请求参数为空");
            return new Page<>();
        }
        // 通过运营主体获取所有品牌
        List<ResponseBrandInfo> data = getBrandInfoList();
        if (CollectionUtils.isEmpty(data)) {
            log.warn("品牌数据为空");
            return new Page<>();
        }
        List<String> brandGuidList = data.stream()
                .map(ResponseBrandInfo::getGuid)
                .distinct()
                .collect(Collectors.toList());

        // 分页查询商品数据
        ItemQueryReqDTO queryReqDTO = getItemQueryReqDTO(pageQO, brandGuidList);
        log.info("[查询品牌下所有商品]入参={}", JacksonUtils.writeValueAsString(queryReqDTO));
        Page<ItemWebRespDTO> itemWebRespDTOPage = saasStoreFeign.queryItemByBrand(queryReqDTO);
        log.info("[查询品牌下所有商品]返回={}", JacksonUtils.writeValueAsString(itemWebRespDTOPage));
        Page<CommodityBaseVO> commodityBaseDTOPage = SaasItemAssembler.itemWebRespDTOPage2CommodityBaseDTOPage(itemWebRespDTOPage);

        return getCommodityBaseVOPage(commodityBaseDTOPage);
    }

    @Override
    public List<CommodityBaseTypeVO> getStoreGoodsComboType() {
        log.info("[获取商品类型基础数据]");
        return null;
    }

    private Page<CommodityBaseVO> getCommodityBaseVOPage(Page<CommodityBaseVO> pageCommodityBase) {
        List<CommodityBaseVO> commodityBaseList = pageCommodityBase.getData();
        Page<CommodityBaseVO> baseVOPage = new Page<>();
        if (CollUtil.isEmpty(commodityBaseList)) {
            return baseVOPage;
        }

        baseVOPage.setCurrentPage(pageCommodityBase.getCurrentPage());
        baseVOPage.setPageSize(pageCommodityBase.getPageSize());
        baseVOPage.setTotalCount(pageCommodityBase.getTotalCount());
        List<CommodityBaseVO> commodityBaseVOList = getCommodityBaseVOList(commodityBaseList);
        baseVOPage.setData(commodityBaseVOList);
        return baseVOPage;
    }

    private List<CommodityBaseVO> getCommodityBaseVOList(List<CommodityBaseVO> commodityBaseList) {
        List<CommodityBaseVO> commodityBaseVOList = Lists.newArrayList();
        commodityBaseList.forEach(commodityBaseDTO -> {
            CommodityBaseVO commodityBaseVO = new CommodityBaseVO();
            commodityBaseVO.setCommodityId(commodityBaseDTO.getCommodityId());
            commodityBaseVO.setCommodityName(commodityBaseDTO.getCommodityName());
            commodityBaseVO.setCommodityCode(commodityBaseDTO.getCommodityCode());
            commodityBaseVO.setCommodityType(commodityBaseDTO.getCommodityType());
            commodityBaseVO.setCommodityPrice(commodityBaseDTO.getCommodityPrice());
            commodityBaseVO.setChannel(SystemEnum.REPAST.getDes());
            commodityBaseVO.setSystem(SystemEnum.REPAST.name());
            commodityBaseVO.setBusinessType(SystemEnum.REPAST.getCode());
            commodityBaseVOList.add(commodityBaseVO);
        });
        return commodityBaseVOList;
    }

    private ItemQueryReqDTO getItemQueryReqDTO(CommodityBasePageQO pageQO, List<String> brandGuidList) {
        ItemQueryReqDTO queryReqDTO = new ItemQueryReqDTO();
        queryReqDTO.setBrandGuidList(brandGuidList);
        queryReqDTO.setTypeGuid(pageQO.getCategoryId());
        if (Objects.nonNull(pageQO.getCommodityType())) {
            queryReqDTO.setItemTypeList(Collections.singletonList(pageQO.getCommodityType()));
        }
        queryReqDTO.setSearchKey(pageQO.getSearchKey());
        queryReqDTO.setPageSize(pageQO.getPageSize());
        queryReqDTO.setCurrentPage(pageQO.getCurrentPage());
        return queryReqDTO;
    }

    /**
     * itemWebRespDTOPage
     */
    private List<ResponseBrandInfo> getBrandInfoList() {
        FeignModel<List<ResponseBrandInfo>> feignModel = saasStoreFeign.queryBrandsBySubject(null, false);
        log.info("[listStoreCommodityPage][通过运营主体查询品牌列表]feignModel={}", JacksonUtils.writeValueAsString(feignModel));
        return feignModel.getData();
    }

    @Override
    public List<CommodityInfoDTO> listCommodity(CommodityInfoQO commodityInfoQO) {
        return Collections.emptyList();
    }

    @Override
    public CrmFeignModel<CommodityInfoDTO> listCommodityHasCount(CommodityInfoQO commodityInfoQO) {
        return null;
    }

    @Override
    public List<StrategyInfoVO> listStrategyInfo(StrategyInfoDTO strategyInfoDTO) {
        return Collections.emptyList();
    }

    @Override
    public String getCommodityUrl(CrmCommodityReqDTO reqDTO) {
        return null;
    }

    @Override
    public List<CommodityDetailsVO> getCommodityDetails(CommodityDetailsQO detailsQO) {
        return Collections.emptyList();
    }

    @Override
    public List<CommodityDetailsVO> getNewCommodityDetails(CommodityDetailsQO detailsQO) {
        return crmFeign.getNewCommodityDetails(detailsQO).getData();
    }

    /**
     * 门店系统获取当前运营主体对应门店的品牌库商品分类数据
     */
    @Override
    public List<ProductCrmCategoryVO> getCommodityCategory(QueryProductCategoryCrmQO categoryCrmQO) {
        if (Objects.isNull(categoryCrmQO)) {
            log.warn("请求参数为空");
            return Lists.newArrayList();
        }
        // 通过运营主体获取所有品牌
        List<ResponseBrandInfo> data = getBrandInfoList();
        if (CollectionUtils.isEmpty(data)) {
            log.warn("品牌数据为空");
            return Lists.newArrayList();
        }
        List<String> brandGuidList = data.stream()
                .map(ResponseBrandInfo::getGuid)
                .distinct()
                .collect(Collectors.toList());
        ItemStringListDTO dto = new ItemStringListDTO();
        dto.setDataList(brandGuidList);
        List<TypeWebRespDTO> typeWebRespDTOList = saasStoreFeign.queryTypeByBrand(dto);
        log.info("[getCommodityCategory][查询品牌列表下的所有分类]typeWebRespDTOList={}", JacksonUtils.writeValueAsString(typeWebRespDTOList));
        return getCategoryVOList(typeWebRespDTOList);
    }

    private List<ProductCrmCategoryVO> getCategoryVOList(List<TypeWebRespDTO> typeWebRespDTOList) {
        List<ProductCrmCategoryVO> productCategoryVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(typeWebRespDTOList)) {
            log.warn("分类数据为空");
            return Lists.newArrayList();
        }
        typeWebRespDTOList.forEach(typeWebRespDTO -> {
            ProductCrmCategoryVO categoryVO = new ProductCrmCategoryVO();
            categoryVO.setCategory_name(typeWebRespDTO.getName());
            categoryVO.setCategory_id(typeWebRespDTO.getTypeGuid());
            productCategoryVOList.add(categoryVO);
        });
        return productCategoryVOList;
    }

    @Override
    public ItemUploadVO itemUploadExcelUrl(String fileUrl, Integer activityType) {
        List<ItemUploadExcel> itemUploadExcelReadList = ItemUploadExcelUtil.read(fileUrl);
        log.info("itemUploadExcelReadList={}", JacksonUtils.writeValueAsString(itemUploadExcelReadList));

        // 条件验证(默认不读取前三条模板数据)
        if (CollUtil.isEmpty(itemUploadExcelReadList)) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_EMPTY_UPLOAD_EXCEL);
        }

        // 数量校验
        if (itemUploadExcelReadList.size() > NumberConstant.NUMBER_20000) {
            throw new MemberBaseException(MemberAccountExceptionEnum.ERROR_MEMBER_MAX_EXPORT_EXCEL);
        }
        List<String> commodityCodeList = itemUploadExcelReadList.stream()
                .map(ItemUploadExcel::getCommodityCode)
                .distinct()
                .collect(Collectors.toList());

        // 返回成功数据
        List<ItemUploadExcelVO> itemUploadExcelSuccessList = Lists.newArrayList();
        // 返回失败数据
        List<ItemUploadErrorExcel> itemUploadExcelErrorList = Lists.newArrayList();

        // 商品查询
        QueryArrayShopBase shopBase = new QueryArrayShopBase();
        shopBase.setCommodityIdList(commodityCodeList);
        List<ResCommodityBase> commodityBaseList = this.listCommodityByDetail(shopBase);
        if (CollectionUtils.isEmpty(commodityBaseList)) {
            // 全部商品都是失败
            log.warn("商品查询为空");
            itemUploadExcelReadList.forEach(itemUploadExcel -> {
                ItemUploadErrorExcel error = getItemUploadErrorExcel(itemUploadExcel, "【商品编码】填写错误");
                itemUploadExcelErrorList.add(error);
            });
            return getItemUploadExcelVO(itemUploadExcelErrorList, itemUploadExcelSuccessList);
        }
        buildItemUploadExcelResult(commodityBaseList, itemUploadExcelReadList, itemUploadExcelSuccessList, itemUploadExcelErrorList, activityType);

        return getItemUploadExcelVO(itemUploadExcelErrorList, itemUploadExcelSuccessList);
    }

    private void buildItemUploadExcelResult(List<ResCommodityBase> commodityBaseList,
                                            List<ItemUploadExcel> itemUploadExcelReadList,
                                            List<ItemUploadExcelVO> itemUploadExcelSuccessList,
                                            List<ItemUploadErrorExcel> itemUploadExcelErrorList,
                                            Integer activityType) {
        Map<String, ResCommodityBase> commodityBaseMap = commodityBaseList.stream()
                .collect(Collectors.toMap(ResCommodityBase::getCommodityCode, Function.identity(), (entity1, entity2) -> entity1));

        // 商品重复校验
        Set<String> commodityCodeSet = new HashSet<>();
        for (ItemUploadExcel uploadExcel : itemUploadExcelReadList) {
            StringBuilder errorSb = new StringBuilder();
            String channel = uploadExcel.getChannel();
            if (StringUtils.isEmpty(channel)) {
                errorSb.append("【商品销售渠道】未填写" + CRLF);
            }
            // 暂时只有这个一个
            if (!ChannelEnum.POS.getType().equals(channel)) {
                errorSb.append("【商品销售渠道】填写错误" + CRLF);
            }

            checkItemInfo(uploadExcel, errorSb, commodityBaseMap, commodityCodeSet, activityType);
            if (Objects.nonNull(activityType) && activityType != SettlementDiscountOptionEnum.FULL_OFF.getCode()) {
                checkActivityNeed(uploadExcel, errorSb);
            }
            if (StringUtils.isEmpty(errorSb.toString())) {
                ItemUploadExcelVO excelVO = getItemUploadExcelVO(uploadExcel);
                itemUploadExcelSuccessList.add(excelVO);
            } else {
                ItemUploadErrorExcel error = getItemUploadErrorExcel(uploadExcel, errorSb.toString());
                itemUploadExcelErrorList.add(error);
            }
        }
    }

    private ItemUploadExcelVO getItemUploadExcelVO(ItemUploadExcel itemUploadExcel) {
        ItemUploadExcelVO excelVO = new ItemUploadExcelVO();
        excelVO.setChannel(itemUploadExcel.getChannel());
        excelVO.setCommodityId(itemUploadExcel.getCommodityCode());
        excelVO.setCommodityCode(itemUploadExcel.getCommodityCode());
        excelVO.setCommodityName(itemUploadExcel.getCommodityName());
        int commodityType = ItemTypeEnum.getCode(itemUploadExcel.getCommodityType());
        excelVO.setCommodityType(commodityType);
        int specialsType = SpecialsTypeEnum.getCode(itemUploadExcel.getSpecialsType());
        excelVO.setSpecialsType(specialsType);
        excelVO.setSpecialsNumber(new BigDecimal(itemUploadExcel.getSpecialsNumber()));
        if (!StringUtils.isEmpty(itemUploadExcel.getLimitNumber())) {
            excelVO.setLimitNumber(Integer.valueOf(itemUploadExcel.getLimitNumber()));
        }
        return excelVO;
    }

    private void checkActivityNeed(ItemUploadExcel itemUploadExcel, StringBuilder errorSb) {
        String limitNumber = itemUploadExcel.getLimitNumber();
        try {
            if (!StringUtils.isEmpty(limitNumber) && Integer.parseInt(limitNumber) > 9999) {
                errorSb.append(LIMIT_ERROR + CRLF);
            }
        } catch (Exception e) {
            log.warn("[优惠限购类型转换错误]e=", e);
            errorSb.append(LIMIT_ERROR + CRLF);
            return;
        }

        String specialsType = itemUploadExcel.getSpecialsType();
        if (StringUtils.isEmpty(specialsType)) {
            errorSb.append("【折扣方式】未选择" + CRLF);
            return;
        }
        String specialsNumber = itemUploadExcel.getSpecialsNumber();
        if (StringUtils.isEmpty(specialsNumber)) {
            errorSb.append("【折扣力度】未填写" + CRLF);
            return;
        }
        checkSpecialsNum(errorSb, specialsNumber, specialsType);
    }

    private void checkSpecialsNum(StringBuilder errorSb, String specialsNumber, String specialsType) {
        BigDecimal specialsNum;
        try {
            specialsNum = new BigDecimal(specialsNumber);
        } catch (Exception e) {
            log.warn("[金额类型转换错误]e=", e);
            errorSb.append(DISCOUNT_AMOUNT_ERROR + CRLF);
            return;
        }
        switch (SpecialsTypeEnum.getEnum(specialsType)) {
            // 打折：0.1~9.9，最多1位小数
            case DISCOUNT:
                if (BigDecimalUtil.lessThan(specialsNum, new BigDecimal("0.1"))
                        || BigDecimalUtil.greaterThan(specialsNum, new BigDecimal("9.9"))
                        || !specialsNumber.matches(ONE)) {
                    errorSb.append(DISCOUNT_AMOUNT_ERROR + CRLF);
                }
                break;
            // 减价：正数输入框，最多2位小数，最大999999.99
            case SALE:
                if (BigDecimalUtil.lessEqual(specialsNum, new BigDecimal("0"))
                        || BigDecimalUtil.greaterThan(specialsNum, new BigDecimal("999999.99"))
                        || !specialsNumber.matches(TWO)) {
                    errorSb.append(DISCOUNT_AMOUNT_ERROR + CRLF);
                }
                break;
            // 指定价格：，可输入0，最大999999.99，最多2位小数
            case SPECIFY_PRICE:
                if (BigDecimalUtil.lessThan(specialsNum, new BigDecimal("0"))
                        || BigDecimalUtil.greaterThan(specialsNum, new BigDecimal("999999.99"))
                        || !specialsNumber.matches(TWO)) {
                    errorSb.append(DISCOUNT_AMOUNT_ERROR + CRLF);
                }
                break;
            default:
                errorSb.append("【折扣方式】填写错误" + CRLF);
                break;
        }
    }

    private void checkItemInfo(ItemUploadExcel itemUploadExcel,
                               StringBuilder errorSb,
                               Map<String, ResCommodityBase> commodityBaseMap,
                               Set<String> commodityCodeSet,
                               Integer activityType) {
        String commodityCode = itemUploadExcel.getCommodityCode();
        if (StringUtils.isEmpty(commodityCode)) {
            errorSb.append("【商品编码】未填写" + CRLF);
        }
        ResCommodityBase commodityBase = commodityBaseMap.get(commodityCode);
        if (commodityCodeSet.contains(commodityCode) || ObjectUtils.isEmpty(commodityBase)) {
            errorSb.append("【商品编码】填写错误" + CRLF);
            return;
        }
        itemUploadExcel.setCommodityName(commodityBase.getName());
        commodityCodeSet.add(commodityCode);
        String commodityType = itemUploadExcel.getCommodityType();

        if (Objects.nonNull(activityType) && activityType != SettlementDiscountOptionEnum.FULL_OFF.getCode()) {
            if (StringUtils.isEmpty(commodityType)) {
                errorSb.append("【商品类型】未填写" + CRLF);
            }
            if (!Objects.equals(ItemTypeEnum.getName(Integer.parseInt(commodityBase.getCommodityComboType())), commodityType)) {
                errorSb.append("【商品类型】填写错误" + CRLF);
            }
        }
    }

    private ItemUploadErrorExcel getItemUploadErrorExcel(ItemUploadExcel itemUploadExcel, String failReason) {
        ItemUploadErrorExcel error = new ItemUploadErrorExcel();
        error.setChannel(itemUploadExcel.getChannel());
        error.setCommodityCode(itemUploadExcel.getCommodityCode());
        error.setCommodityName(itemUploadExcel.getCommodityName());
        error.setCommodityType(itemUploadExcel.getCommodityType());
        error.setSpecialsType(itemUploadExcel.getSpecialsType());
        error.setSpecialsNumber(itemUploadExcel.getSpecialsNumber());
        error.setLimitNumber(itemUploadExcel.getLimitNumber());
        error.setFailReason(failReason);
        return error;
    }

    private ItemUploadVO getItemUploadExcelVO(List<ItemUploadErrorExcel> itemUploadExcelErrorList,
                                              List<ItemUploadExcelVO> itemUploadExceSuccesslList) {
        // 上传至阿里oos
        ItemUploadVO excelVO = new ItemUploadVO();
        excelVO.setFail(itemUploadExcelErrorList.size());
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formatDateTime = now.format(formatter);
        if (!CollectionUtils.isEmpty(itemUploadExcelErrorList)) {
            ExcelResult excelResult = com.aimilin.utils.BeanUtils.toResult(itemUploadExcelErrorList);
            byte[] write = ExcelWriteUtils.write(excelResult, ExcelType.XLSX);
            try {
                FileDto fileDto = new FileDto();
                fileDto.setFileContent(com.holderzone.framework.security.SecurityManager.entryptBase64(write));
                fileDto.setFileName(String.format("限时特价批量导入商品%s-失败数据", formatDateTime) + "." + ExcelType.XLSX);
                String upload = fileOssService.upload(fileDto);
                String newUpload = upload.replace("http", "https");
                log.info("[限时特价批量导入商品]错误信息文件下载路径={}", newUpload);
                excelVO.setFailUrl(newUpload);
                excelVO.setFail(itemUploadExcelErrorList.size());
            } catch (Exception e) {
                log.error("上传文件失败", e);
                excelVO.setFailUrl("上传错误信息失败");
            }
        }
        excelVO.setSuccess(itemUploadExceSuccesslList.size());
        excelVO.setItemUploadExcelList(itemUploadExceSuccesslList);
        return excelVO;
    }

}
