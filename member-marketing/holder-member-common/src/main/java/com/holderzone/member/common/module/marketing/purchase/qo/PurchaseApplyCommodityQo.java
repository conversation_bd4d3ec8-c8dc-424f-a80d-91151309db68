package com.holderzone.member.common.module.marketing.purchase.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.enums.growth.ApplyBusinessEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 限购活动商品查询
 *
 * <AUTHOR>
 * @date 2023/12/01
 **/
@Data
public class PurchaseApplyCommodityQo implements Serializable {

    private static final long serialVersionUID = -222336256340727278L;
    /**
     * 主体
     */
    @ApiModelProperty("主体")
    private String operSubjectGuid;

    /**
     * 业务（订单类型） :见同步表
     *
     * @see com.holderzone.member.common.enums.growth.ApplyBusinessEnum
     */
    @ApiModelProperty("业务类型")
    @NotBlank(message = "业务必传！")
    private String business;

    /**
     * 消费门店GUID
     */
    @ApiModelProperty(value = "消费门店GUID")
    @NotBlank(message = "消费门店GUID必传！")
    private String storeGuid;

    /**
     * 下单用户
     */
    @ApiModelProperty(value = "下单用户")
    private String memberGuid;

    /**
     * 商品渠道
     */
    @ApiModelProperty(value = "渠道")
    @NotBlank(message = "渠道必传！")
    private String channel;

    /**
     * 开始时间 （预定业务）
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime startTime;

    /**
     * 结束时间 （预定业务）
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime endTime;

    /**
     * 指定商品编码: 为空则返回所有
     */
    @ApiModelProperty(value = "指定商品编码")
    private List<String> commodityCodes;

    /**
     * 预定业务校验
     */
    public void checkBusiness() {
        if (business.equals(ApplyBusinessEnum.RESERVE_MEAL.getCode() + "")
                && (startTime == null || endTime == null)) {
                throw new MemberBaseException("预定业务，开始时间、结束时间、商品编码不能为空！");

        }
    }
}
