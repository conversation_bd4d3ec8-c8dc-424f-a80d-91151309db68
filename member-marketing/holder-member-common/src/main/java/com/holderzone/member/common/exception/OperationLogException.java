package com.holderzone.member.common.exception;

import com.holderzone.member.common.enums.exception.OperationLogExceptionEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 操作日志服务异常
 * @date 2021/8/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OperationLogException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    protected int code;
    /**
     * 错误信息
     */
    protected String des;

    public OperationLogException() {
        super();
    }

    public OperationLogException(OperationLogExceptionEnum exceptionEnums) {
        super(exceptionEnums.getDes());
        this.code = exceptionEnums.getCode();
        this.des = exceptionEnums.getDes();
    }



}
