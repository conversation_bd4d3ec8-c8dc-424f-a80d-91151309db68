package com.holderzone.member.common.constant;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.enums.CommonEnum;
import com.holderzone.member.common.enums.ResponseBase;
import com.holderzone.member.common.enums.exception.ExceptionEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 服务返回结果
 * @date 2021/8/9
 */
@Data
public final class Result<T> implements Serializable {

    /**返回码*/
    private int code;

    /**返回消息*/
    private String message;

    /**返回数据*/
    private T data;

    public Result() {
    }

    public Result(ExceptionEnum exceptionEnum) {
        this.code = exceptionEnum.getCode();
        this.message = exceptionEnum.getDes();
    }

    /**
     * 成功
     *
     * @return null
     */
    public static <T> Result <T> success() {
        return success(null);
    }

    /**
     * 成功
     * @param data 数据
     * @return 返回数据
     */
    public static <T> Result<T> success(T data) {
        Result<T> rb = new Result<>();
        rb.setCode(CommonEnum.SUCCESS.getCode());
        rb.setMessage(CommonEnum.SUCCESS.getDes());
        rb.setData(data);
        return rb;
    }

    /**
     * 失败
     */
    public static <T>Result<T> error(ResponseBase errorInfo) {
        Result<T> rb = new Result<>();
        rb.setCode(errorInfo.getCode());
        rb.setMessage(errorInfo.getDes());
        rb.setData(null);
        return rb;
    }

    /**
     * 失败
     */
    public static <T>Result<T> error(T data) {
        Result<T> rb = new Result<>();
        rb.setCode(CommonEnum.FAILED.getCode());
        rb.setMessage(CommonEnum.FAILED.getDes());
        rb.setData(data);
        return rb;
    }

    /**
     * 失败
     */
    public static <T>Result<T> error(int code, String message) {
        Result<T> rb = new Result<>();
        rb.setCode(code);
        rb.setMessage(message);
        rb.setData(null);
        return rb;
    }

    /**
     * 失败
     */
    public static <T> Result<T> error(int code, String message,T data) {
        Result<T> rb = new Result<>();
        rb.setCode(code);
        rb.setMessage(message);
        rb.setData(data);
        return rb;
    }

    /**
     * 失败
     */
    public static <T>Result<T> error(String message) {
        Result<T> rb = new Result<>();
        rb.setCode(CommonEnum.FAILED.getCode());
        rb.setMessage(message);
        rb.setData(null);
        return rb;
    }

    /**
     * 响应操作结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    public static <T>Result<T> isSuccess(int rows){
        return rows > 0 ? success() : error("操作失败");
    }

    public static <T>Result<T> isSuccess(boolean flag){
        return flag ? success() : error("操作失败");
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }

}
