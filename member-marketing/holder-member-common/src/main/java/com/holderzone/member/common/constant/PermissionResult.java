package com.holderzone.member.common.constant;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.enums.CommonEnum;
import com.holderzone.member.common.enums.ResponseBase;
import com.holderzone.member.common.enums.exception.ExceptionEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public final class PermissionResult implements Serializable {

    /**返回码*/
    private int returnCode;

    /**返回消息*/
    private String returnMessage;

    /**返回数据*/
    private Object dataList;

    public PermissionResult() {
    }

    public PermissionResult(ExceptionEnum exceptionEnum) {
        this.returnCode = exceptionEnum.getCode();
        this.returnMessage = exceptionEnum.getDes();
    }

    /**
     * 成功
     *
     * @return null
     */
    public static PermissionResult success() {
        return success(null);
    }

    /**
     * 成功
     * @param data 数据
     * @return 返回数据
     */
    public static PermissionResult success(Object data) {
        PermissionResult rb = new PermissionResult();
        rb.setReturnCode(CommonEnum.SUCCESS.getCode());
        rb.setReturnMessage(CommonEnum.SUCCESS.getDes());
        rb.setDataList(data);
        return rb;
    }

    /**
     * 失败
     */
    public static PermissionResult error(ResponseBase errorInfo) {
        PermissionResult rb = new PermissionResult();
        rb.setReturnCode(errorInfo.getCode());
        rb.setReturnMessage(errorInfo.getDes());
        rb.setDataList(null);
        return rb;
    }

    /**
     * 失败
     */
    public static PermissionResult error(int code, String message) {
        PermissionResult rb = new PermissionResult();
        rb.setReturnCode(code);
        rb.setReturnMessage(message);
        rb.setDataList(null);
        return rb;
    }

    /**
     * 失败
     */
    public static PermissionResult error(int code, String message, Object data) {
        PermissionResult rb = new PermissionResult();
        rb.setReturnCode(code);
        rb.setReturnMessage(message);
        rb.setDataList(data);
        return rb;
    }

    /**
     * 失败
     */
    public static PermissionResult error(String message) {
        PermissionResult rb = new PermissionResult();
        rb.setReturnCode(CommonEnum.FAILED.getCode());
        rb.setReturnMessage(message);
        rb.setDataList(null);
        return rb;
    }

    /**
     * 响应操作结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    public static PermissionResult isSuccess(int rows){
        return rows > 0 ? success() : error("操作失败");
    }

    public static PermissionResult isSuccess(boolean flag){
        return flag ? success() : error("操作失败");
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }

}
