package com.holderzone.member.common.module.settlement.rule.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountItemEnum;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 结算优惠项
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementDiscountVO implements Serializable {

    private static final long serialVersionUID = -2115083201325155686L;

    /**
     * 唯一主键
     */
    private String guid;

    /**
     * 父级
     */
    private String parentGuid;

    /**
     * 父级名称
     */
    private String parentDiscountName;

    /***
     * 父级数量
     */
    private Integer parentDiscountNum;

    @ApiModelProperty(value = "类型：0 单品级优惠 1订单级优惠 2资产优惠")
    private Integer discountType;

    /**
     * @see SettlementDiscountItemEnum
     */
    @ApiModelProperty(value = "优惠项： SettlementDiscountItemEnum")
    private Integer discountItem;

    /**
     * 优惠项 具体小类
     *
     * @see SettlementDiscountOptionEnum
     */
    @ApiModelProperty(value = "结算台优惠具体项： SettlementDiscountOptionEnum")
    private Integer discountOption;

    @ApiModelProperty(value = "优惠guid")
    private String discountGuid;

    @ApiModelProperty(value = "优惠名称")
    private String discountName;

    @ApiModelProperty(value = "优惠力度")
    private String discountDynamic;

    @ApiModelProperty(value = "数量")
    private Integer discountNum;
}
