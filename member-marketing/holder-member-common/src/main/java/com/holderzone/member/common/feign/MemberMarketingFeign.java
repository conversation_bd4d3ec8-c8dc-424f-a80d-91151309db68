package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.unite.UniteActivityOrderPayDTO;
import com.holderzone.member.common.dto.unite.UniteActivityOrderRefundDTO;
import com.holderzone.member.common.module.marketing.purchase.vo.PurchaseActivityOrderVO;
import com.holderzone.member.common.module.marketing.purchase.vo.PurchasePartDetailVo;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementSynMarketingDTO;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityRecordQO;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityRunQO;
import com.holderzone.member.common.qo.activity.SynOrderDiscountQO;
import com.holderzone.member.common.qo.card.BarkOrderDiscountCallbackQO;
import com.holderzone.member.common.qo.coupon.CouponPageQO;
import com.holderzone.member.common.qo.evaluation.OrderEvaluationListQO;
import com.holderzone.member.common.qo.gift.RechargeGiftActivityAppletQO;
import com.holderzone.member.common.qo.gift.TerRechargeGiftActivityQO;
import com.holderzone.member.common.qo.mall.order.MemberSynOrderQO;
import com.holderzone.member.common.qo.nth.NthActivityApplyRecordQO;
import com.holderzone.member.common.qo.permission.HsaDeleteOperSubjectLabelTypeQO;
import com.holderzone.member.common.qo.redeem.RequestCheckRedeemApplyQO;
import com.holderzone.member.common.qo.redeem.RequestUpdateDtlQO;
import com.holderzone.member.common.qo.redeem.RespondCheckRedeemApplyVO;
import com.holderzone.member.common.qo.redeem.RespondEditActiveVO;
import com.holderzone.member.common.qo.specials.LimitSpecialsActivityRunQO;
import com.holderzone.member.common.qo.specials.SpecialsActivityRecordQO;
import com.holderzone.member.common.qo.nth.NthActivityApplyRunQO;
import com.holderzone.member.common.qo.nth.NthActivityApplyRunVO;
import com.holderzone.member.common.qo.unite.UniteActivityQO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityRecordVO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityRunVO;
import com.holderzone.member.common.vo.certificate.CertifiedPupilActivityVO;
import com.holderzone.member.common.vo.certificate.CertifiedStudentVerifiedVO;
import com.holderzone.member.common.vo.coupon.CouponPackageSelfDetailsVO;
import com.holderzone.member.common.vo.coupon.EditCouponPackageActivityVO;
import com.holderzone.member.common.vo.coupon.ResponseCouponActivityVO;
import com.holderzone.member.common.vo.coupon.SellableCouponPackageDetailsVO;
import com.holderzone.member.common.vo.coupon.SellableCouponPackageListVO;
import com.holderzone.member.common.vo.gift.RechargeGiftActivityRunVO;
import com.holderzone.member.common.vo.gift.RechargeGiftActivityVO;
import com.holderzone.member.common.vo.gift.TerRechargeGiftActivityVO;
import com.holderzone.member.common.vo.member.MemberSystemPermissionVO;
import com.holderzone.member.common.vo.portrayal.MemberPortrayalDetailsVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityRecordVO;
import com.holderzone.member.common.vo.specials.LimitSpecialsActivityRunVO;
import com.holderzone.member.common.vo.unite.UniteActivityVO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-03-23 16:03
 */
@Component
@FeignClient(name = FilterConstant.FEIGN_MARKETING, fallbackFactory = MemberMarketingFeign.ServiceFallBack.class)
public interface MemberMarketingFeign {

    @ApiOperation("更新限时特价优惠记录")
    @PostMapping("/limit_specials_activity/update_discount_record")
    void updateRecord(@RequestBody BarkOrderDiscountCallbackQO barkOrderDiscountCallbackQO);

    @ApiOperation("更新满减满折优惠记录")
    @PostMapping("/full/reduction/fold/record/update_discount_record")
    void updateFullReductionRecord(@RequestBody BarkOrderDiscountCallbackQO callbackQO);

    @ApiOperation("同步限时特价优惠记录")
    @PostMapping("/limit_specials_activity/syn_discount_record")
    void saveRecord(@RequestBody List<SynOrderDiscountQO> recordQO);

    @ApiOperation("同步满减满折优惠记录")
    @PostMapping("/full/reduction/fold/record/syn_discount_record")
    void saveFullReductionRecord(@RequestBody List<SynOrderDiscountQO> recordQO);

    @PostMapping("/full/reduction/fold/query_run_info")
    List<FullReductionFoldActivityRunVO> queryRunInfo(@RequestBody FullReductionFoldActivityRunQO query);

    @ApiOperation("结算台查询新会员能参与的活动的相关商品及活动信息")
    @PostMapping("/limit_specials_activity/query_settle_commodity_run")
    List<LimitSpecialsActivityRunVO> querySettleCommodityByRun(@RequestBody LimitSpecialsActivityRunQO query);

    @ApiOperation("结算台查询满减满折活动")
    @PostMapping("/full/reduction/fold/query_settle_commodity_run")
    List<FullReductionFoldActivityRunVO> querySettleCommodityByRun(@RequestBody FullReductionFoldActivityRunQO query);

    @ApiOperation("查询进行中的活动信息")
    @PostMapping("/limit_specials_activity/query_run_info")
    List<LimitSpecialsActivityRunVO> queryRunInfo(@RequestBody LimitSpecialsActivityRunQO query);

    @ApiOperation("自动同步限时特价状态")
    @PostMapping("/limit_specials_activity/syn_state")
    void limitSpecialsSynState();

    @ApiOperation("结算台同步活动共享互斥项")
    @PostMapping(value = "/callback/syn_activity_relation_rule")
    Result<Void> synActivityRelationRule(@RequestBody SettlementSynMarketingDTO synMarketingDTO);

    @ApiOperation("结算台同步优惠券共享互斥项")
    @PostMapping(value = "/coupon_activity/syn_coupon_relation_rule")
    Result<Void> synCouponRelationRule(@RequestBody SettlementSynMarketingDTO synMarketingDTO);

    @PostMapping(value = "/callback/initialize_subject_data")
    Result<Void> initializeSubjectData(@RequestBody List<String> operSubjectGuids);

    @PostMapping(value = "/subsidy_record/find_all_soon_overdue_subsidy")
    Result<BigDecimal> findAllSoonOverdueSubsidy(@RequestBody List<String> detailRecordGuids);

    @ApiOperation("会员信息同步")
    @PostMapping(value = "/order_valuation/synMemberInfo", produces = "application/json;charset=utf-8")
    void memberSynOrder(@RequestBody MemberSynOrderQO memberSynOrderQO);

    @ApiOperation("当前holder用户操作权限")
    @PostMapping("/member_permission/marketing_account_permission")
    MemberSystemPermissionVO getHolderAccountPermission(@RequestParam(value = "identification") String identification);

    /**
     * 同步删除标签
     */
    @ApiOperation("修改当前账户中运营主体权限")
    @PostMapping("/hsa_oper_subject_permission_label/delete_label")
    void deleteOperSubjectLabel(@RequestBody HsaDeleteOperSubjectLabelTypeQO qo);

    @ApiOperation("获取活动详情")
    @GetMapping(value = "/coupon_package/details")
    Result<EditCouponPackageActivityVO> queryActivity(@RequestParam(value = "guid") String guid);

    @ApiOperation("获取发放中活动")
    @PostMapping(value = "/coupon_package/query_run_activity", produces = "application/json;charset=utf-8")
    List<EditCouponPackageActivityVO> queryRunActivity(@RequestBody List<String> activityGuid);

    @ApiOperation("查询自助领券活动详情")
    @GetMapping(value = "/coupon_package/self_details")
    Result<CouponPackageSelfDetailsVO> getCouponPackageSelfDetails(@RequestParam("guid") String guid,
                                                                   @RequestParam(value = "memberGuid", required = false) String memberGuid);

    /**
     * 测试地址
     */
    @PostMapping("/activity_job/test")
    void test();

    /**
     * 补贴活动状态更新
     */
    @PostMapping("/activity_job/update_status")
    void updateActivityStatus();

    /**
     * 补贴活动回收权益
     */
    @PostMapping("/subsidy_job/back_rights")
    void subsidyBackRights();

    /**
     * 发放补贴活动权益
     */
    @PostMapping("/send_job/subsidy_rights")
    void sendSubsidyRights();

    @PostMapping("/order_valuation/queryEvaluationByOrderNum")
    @ApiOperation("过滤可评价订单")
    Result queryEvaluationByOrderNum(@RequestBody OrderEvaluationListQO orderNum);


    /**
     * 一体机查询会员满足的所有活动
     *
     * @param rechargeGiftActivityQO 查询条件
     */
    @PostMapping("/recharge-gift-activity/list_suitable_ter")
    List<TerRechargeGiftActivityVO> listSuitableNew(@RequestBody TerRechargeGiftActivityQO rechargeGiftActivityQO);

    @PostMapping("/recharge-gift-activity/list_suitable")
    List<RechargeGiftActivityVO> listSuitableRechargeGiftActivity(@RequestBody RechargeGiftActivityAppletQO activityAppletQO);

    @PostMapping("/purchase/activity/listByGuid")
    List<PurchaseActivityOrderVO> listPurchaseByGuids(@RequestBody List<String> listGuid);


    @ApiOperation("定时过期任务")
    @GetMapping(value = "/coupon_activity/xxl_job_state", produces = "application/json;charset=utf-8")
    void updateActivityState();

    @ApiOperation("定时过期任务")
    @GetMapping(value = "/redeem/code/active/xxl_job_state", produces = "application/json;charset=utf-8")
    void updateRedeemActivityState();

    /**
     * 查询运行中可触发活动（未过滤）
     */
    @GetMapping("/recharge-gift-activity/get_activity_by_run")
    List<RechargeGiftActivityRunVO> getActivityByRun(@RequestParam(value = "operSubjectGuid") String operSubjectGuid);

    /**
     * 根据活动GUID查询充值赠送活动详情
     */
    @GetMapping("/recharge-gift-activity/query_by_guid")
    RechargeGiftActivityVO queryRechargeGiftActivityByGuid(@RequestParam(value = "activityGuid") String activityGuid);

    /**
     * 根据活动GUID列表批量查询充值赠送活动详情
     */
    @PostMapping("/recharge-gift-activity/query_by_guid_list")
    List<RechargeGiftActivityVO> queryRechargeGiftActivityByGuidList(@RequestBody List<String> activityGuidList);

    /**
     * 初始化默认主题
     */
    @ApiOperation(value = "初始化默认主题")
    @PostMapping("/certified_activity/init_certified_theme")
    void initCertifiedTheme(List<String> subjectInfoList);

    /**
     * 查询是否参加学生认证活动
     */
    @GetMapping("/certified_activity/check_join_student_activity")
    Boolean checkJoinStudentActivity(@RequestParam("userId") String userId);

    /**
     * 学生认证通过
     */
    @PostMapping("/certified_activity/student_verified")
    CertifiedPupilActivityVO studentVerified(@RequestBody CertifiedStudentVerifiedVO request);

    @PostMapping("/purchase/activity/listByIds")
    List<PurchasePartDetailVo> listPurchaseByIds(@RequestBody List<String> ids);

    @ApiOperation("条件查询活动记录")
    @PostMapping("/limit_specials_activity_statics/query_record")
    List<LimitSpecialsActivityRecordVO> queryRecord(@RequestBody SpecialsActivityRecordQO qo);

    @ApiOperation("条件查询满减满折活动记录")
    @PostMapping("/full/reduction/fold/record/query_record")
    List<FullReductionFoldActivityRecordVO> queryRecord(@RequestBody FullReductionFoldActivityRecordQO qo);

    /**
     * 查询是否参加学生认证活动
     */
    @GetMapping("/redeem/code/active/queryByRedeemCode")
    Result<RespondEditActiveVO> queryByRedeemCode(@RequestBody RequestCheckRedeemApplyQO request);

    @ApiOperation("查询会员画像配置")
    @GetMapping("/member_portrayal/query_apply_setting")
    MemberPortrayalDetailsVO queryApplySetting(@RequestParam("operSubjectGuid") String operSubjectGuid);

    /**
     * 兑换码兑换
     */
    @PostMapping("/redeem/code/active/redeem")
    Result<RespondCheckRedeemApplyVO> redeem(@RequestBody RequestCheckRedeemApplyQO request);

    /**
     * 批量修改兑换码会员信息
     */
    @PostMapping("/redeem/code/dtl/batchRedeemCodeMemberInfo")
    Result<Void> batchRedeemCodeMemberInfo(@RequestBody RequestUpdateDtlQO request);

    /**
     * 查询优惠券列表
     */
    @PostMapping("/coupon_activity/page")
    Result<PageResult<ResponseCouponActivityVO>> couponPageQuery(@RequestBody CouponPageQO qo);

    /**
     * 查询适用的第N份活动
     */
    @ApiOperation("查询适用的第N份活动")
    @PostMapping("/nth_activity/apply/query_run_activities")
    List<NthActivityApplyRunVO> queryApplyRunActivities(@RequestBody NthActivityApplyRunQO qo);

    @ApiOperation("保存第N份活动记录")
    @PostMapping("/nth_activity/apply/save_nth_activity_apply_record")
    void saveNthActivityApplyRecord(@RequestBody NthActivityApplyRecordQO qo);

    /**
     * 营销活动统一查询
     */
    @PostMapping("/unite/activity/query")
    Result<UniteActivityVO> queryUniteActivity(@RequestBody UniteActivityQO query);

    /**
     * 营销活动统一下单
     */
    @PostMapping("/unite/activity/pay")
    void payUniteActivity(@RequestBody UniteActivityOrderPayDTO payDTO);

    /**
     * 营销活动统一退款
     */
    @PostMapping("/unite/activity/refund")
    void refundUniteActivity(@RequestBody UniteActivityOrderRefundDTO refundDTO);

    /**
     * 获取可售券包列表（应用端）
     */
    @ApiOperation("获取可售券包列表")
    @GetMapping("/sellable_coupon_package/applets/list")
    Result<PageResult<SellableCouponPackageListVO>> getSellableCouponPackageList(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize);

    /**
     * 获取可售券包详情（应用端）
     */
    @ApiOperation("获取可售券包详情")
    @GetMapping("/sellable_coupon_package/applets/details")
    Result<SellableCouponPackageDetailsVO> getSellableCouponPackageDetails(
            @RequestParam("guid") String guid,
            @RequestParam(value = "memberGuid", required = false) String memberGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MemberMarketingFeign> {

        @Override
        public MemberMarketingFeign create(Throwable throwable) {
            return new MemberMarketingFeign() {


                @Override
                public void updateRecord(BarkOrderDiscountCallbackQO barkOrderDiscountCallbackQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateRecord", barkOrderDiscountCallbackQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateFullReductionRecord(BarkOrderDiscountCallbackQO callbackQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateFullReductionRecord", callbackQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void saveRecord(List<SynOrderDiscountQO> recordQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveRecord", recordQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void saveFullReductionRecord(List<SynOrderDiscountQO> recordQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveFullReductionRecord", recordQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<FullReductionFoldActivityRunVO> queryRunInfo(FullReductionFoldActivityRunQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryRunInfo", query, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<LimitSpecialsActivityRunVO> querySettleCommodityByRun(LimitSpecialsActivityRunQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "querySettleCommodityByRun", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<FullReductionFoldActivityRunVO> querySettleCommodityByRun(FullReductionFoldActivityRunQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "querySettleCommodityByRun", query, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<LimitSpecialsActivityRunVO> queryRunInfo(LimitSpecialsActivityRunQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryRunInfo", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void limitSpecialsSynState() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "limitSpecialsSynState", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> synActivityRelationRule(SettlementSynMarketingDTO synMarketingDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "synActivityRelationRule", synMarketingDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> synCouponRelationRule(SettlementSynMarketingDTO synMarketingDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "synCouponRelationRule", JacksonUtils.writeValueAsString(synMarketingDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> initializeSubjectData(List<String> operSubjectGuids) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "initializeSubjectData", operSubjectGuids, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<BigDecimal> findAllSoonOverdueSubsidy(List<String> detailRecordGuids) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findAllSoonOverdueSubsidy", detailRecordGuids, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void memberSynOrder(MemberSynOrderQO memberSynOrderQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "memberSynOrder", memberSynOrderQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberSystemPermissionVO getHolderAccountPermission(String identification) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getHolderAccountPermission", identification, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void deleteOperSubjectLabel(HsaDeleteOperSubjectLabelTypeQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "deleteOperSubjectLabel", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<EditCouponPackageActivityVO> queryActivity(String activityGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryActivity", activityGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<EditCouponPackageActivityVO> queryRunActivity(@RequestBody List<String> activityGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryRunActivity", activityGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<CouponPackageSelfDetailsVO> getCouponPackageSelfDetails(String guid, String memberGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCouponPackageSelfDetails", guid + "-" + memberGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void test() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "test", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateActivityStatus() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateActivityStatus", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void subsidyBackRights() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "subsidyBackRights", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void sendSubsidyRights() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "sendSubsidyRights", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result queryEvaluationByOrderNum(OrderEvaluationListQO orderNum) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryEvaluationByOrderNum", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<TerRechargeGiftActivityVO> listSuitableNew(TerRechargeGiftActivityQO rechargeGiftActivityQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listSuitableNew",
                            JacksonUtils.writeValueAsString(rechargeGiftActivityQO), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<RechargeGiftActivityVO> listSuitableRechargeGiftActivity(RechargeGiftActivityAppletQO activityAppletQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listSuitableRechargeGiftActivity",
                            JacksonUtils.writeValueAsString(activityAppletQO), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PurchaseActivityOrderVO> listPurchaseByGuids(List<String> listGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listPurchaseByGuids",
                            JacksonUtils.writeValueAsString(listGuid), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }


                @Override
                public void updateActivityState() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listSuitableRechargeGiftActivity", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateRedeemActivityState() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateRedeemActivityState", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<RechargeGiftActivityRunVO> getActivityByRun(String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getActivityByRun",
                            JacksonUtils.writeValueAsString(operSubjectGuid), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public RechargeGiftActivityVO queryRechargeGiftActivityByGuid(String activityGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryRechargeGiftActivityByGuid",
                            activityGuid, ThrowableUtils.asString(throwable));
                    return null; // 返回null而不是抛异常，避免影响主流程
                }

                @Override
                public List<RechargeGiftActivityVO> queryRechargeGiftActivityByGuidList(List<String> activityGuidList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryRechargeGiftActivityByGuidList",
                            JacksonUtils.writeValueAsString(activityGuidList), ThrowableUtils.asString(throwable));
                    return Collections.emptyList(); // 返回空列表而不是抛异常，避免影响主流程
                }

                @Override
                public List<PurchasePartDetailVo> listPurchaseByIds(List<String> ids) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listPurchaseByIds",
                            JacksonUtils.writeValueAsString(ids), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<LimitSpecialsActivityRecordVO> queryRecord(SpecialsActivityRecordQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryRecord",
                            JacksonUtils.writeValueAsString(qo), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<FullReductionFoldActivityRecordVO> queryRecord(FullReductionFoldActivityRecordQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryRecord",
                            JacksonUtils.writeValueAsString(qo), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<RespondEditActiveVO> queryByRedeemCode(RequestCheckRedeemApplyQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryByRedeemCode",
                            JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberPortrayalDetailsVO queryApplySetting(String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryApplySetting",
                            operSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<RespondCheckRedeemApplyVO> redeem(RequestCheckRedeemApplyQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "redeem",
                            request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> batchRedeemCodeMemberInfo(RequestUpdateDtlQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "batchRedeemCodeMemberInfo",
                            request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult<ResponseCouponActivityVO>> couponPageQuery(CouponPageQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "couponPageQuery",
                            qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult<SellableCouponPackageListVO>> getSellableCouponPackageList(Integer pageNum, Integer pageSize) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getSellableCouponPackageList",
                            "pageNum=" + pageNum + ", pageSize=" + pageSize, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<SellableCouponPackageDetailsVO> getSellableCouponPackageDetails(String guid, String memberGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getSellableCouponPackageDetails",
                            "guid=" + guid + ", memberGuid=" + memberGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void initCertifiedTheme(List<String> subjectInfoList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "initCertifiedTheme",
                            subjectInfoList, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean checkJoinStudentActivity(String userId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "checkJoinStudentActivity",
                            userId, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CertifiedPupilActivityVO studentVerified(CertifiedStudentVerifiedVO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "studentVerified",
                            JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<NthActivityApplyRunVO> queryApplyRunActivities(NthActivityApplyRunQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryApplyRunActivities",
                            JacksonUtils.writeValueAsString(qo), ThrowableUtils.asString(throwable));
                    return Collections.emptyList(); // 返回空列表而不是抛异常，避免影响主流程
                }

                @Override
                public void saveNthActivityApplyRecord(NthActivityApplyRecordQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveNthActivityApplyRecord",
                            JacksonUtils.writeValueAsString(qo), ThrowableUtils.asString(throwable));
                }

                @Override
                public Result<UniteActivityVO> queryUniteActivity(UniteActivityQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryUniteActivity",
                            JacksonUtils.writeValueAsString(query), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void payUniteActivity(UniteActivityOrderPayDTO payDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "payUniteActivity",
                            JacksonUtils.writeValueAsString(payDTO), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void refundUniteActivity(UniteActivityOrderRefundDTO refundDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "refundUniteActivity",
                            JacksonUtils.writeValueAsString(refundDTO), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
