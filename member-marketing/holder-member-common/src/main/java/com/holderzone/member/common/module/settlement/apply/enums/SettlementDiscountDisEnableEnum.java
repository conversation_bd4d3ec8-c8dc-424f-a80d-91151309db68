package com.holderzone.member.common.module.settlement.apply.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 优惠项不可用原因,方便定位问题
 *
 * <AUTHOR>
 * @date 2023/10/25
 * @since 1.8
 */
@AllArgsConstructor
@Getter
public enum SettlementDiscountDisEnableEnum {

    FEE_0(0,"优惠为0"),

    ;

    /**
     * 编码
     */
    private final int code;

    /**
     * 信息
     */
    private final String des;
}
