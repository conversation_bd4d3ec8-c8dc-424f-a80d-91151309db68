package com.holderzone.member.common.module.settlement.apply.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyCommodityDTO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品优惠明细
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementApplyCommodityVO implements Serializable {

    private static final long serialVersionUID = 3559064153837638379L;

    /**
     * 唯一id
     * 商品id相同时，这个需要保持唯一
     */
    @ApiModelProperty("唯一id")
    private String rid;

    /**
     * 商品id
     */
    @ApiModelProperty("商品id")
    private String commodityId;

    /**
     * 商品skuId
     */
    private String skuId;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String commodityName;

    /**
     * 商品 优惠总额金额
     */
    @ApiModelProperty("商品优惠总额")
    private BigDecimal discountFee = BigDecimal.ZERO;

    /**
     * 单个商品优惠金额
     */
    @ApiModelProperty("单个商品优惠金额")
    private BigDecimal singleDiscountFee = BigDecimal.ZERO;

    /**
     * 参与优惠的商品数量
     */
    @ApiModelProperty("参与优惠的商品数量")
    private BigDecimal discountNum = BigDecimal.ZERO;

    /**
     * 优惠后应付总额
     */
    @ApiModelProperty("优惠后应付总额")
    private BigDecimal discountTotalFee = BigDecimal.ZERO;

    /**
     * 优惠分摊金额
     */
    private BigDecimal shareDiscountFee;

    /**
     * 不为空 表示这个商品有改价
     */
    private String discountPriceInShopCar;

    /**
     * 商品类型  只计算销售中心且无改价的商品
     * 2无码加购  3 快速结账商品  1 来自于销售中心的商品  11 扫码加购的商品
     */
    private Integer goodsType;

    /**
     * 商品 优惠总数量
     */
    @ApiModelProperty("优惠总数量")
    private BigDecimal exchangeDiscountNum =new BigDecimal(BigDecimal.ROUND_UP);

    /**
     * 限时特价后的单价
     */
    private BigDecimal timeLimitPrice;

    /**
     * 限时特价的商品数量
     */
    private BigDecimal timeLimitNum = new BigDecimal(BigDecimal.ROUND_UP);

    /**
     * 构造商品
     *
     * @param rid         唯一id
     * @param commodityId 商品id
     * @param discountFee 优惠
     * @return 商品对象
     */
    public static SettlementApplyCommodityVO build(String rid,
                                                   String commodityId,
                                                   BigDecimal discountFee) {
        return new SettlementApplyCommodityVO()
                .setRid(rid)
                .setCommodityId(commodityId)
                .setDiscountFee(discountFee);
    }


    /**
     * 构造商品
     *
     * @return 商品对象
     */
    public static SettlementApplyCommodityVO build(SettlementApplyCommodityDTO dto) {
        return new SettlementApplyCommodityVO()
                .setRid(dto.getRid())
                .setCommodityId(dto.getCommodityId())
                .setShareDiscountFee(dto.getDiscountFee())
                .setCommodityName(dto.getCommodityName())
                .setSkuId(dto.getSkuId())
                .setGoodsType(dto.getGoodsType())
                .setDiscountFee(dto.getDiscountFee());
    }

    /**
     * 唯一 主键
     *
     * @return 主键
     */
    @JsonIgnore
    public String key() {
        return rid + commodityId;
    }
}
