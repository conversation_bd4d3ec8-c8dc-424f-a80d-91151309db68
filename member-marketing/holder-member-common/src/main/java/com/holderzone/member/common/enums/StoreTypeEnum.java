package com.holderzone.member.common.enums;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-03-11 11:04
 */
public enum StoreTypeEnum {

    CREDIT(0, "挂账账户"),

    GROWTH_VALUE(1, "会员成长值"),

    INTEGRAL_VALUE(2, "会员积分"),
    ;

    private int code;

    /**
     * 信息
     */
    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    StoreTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }
}
