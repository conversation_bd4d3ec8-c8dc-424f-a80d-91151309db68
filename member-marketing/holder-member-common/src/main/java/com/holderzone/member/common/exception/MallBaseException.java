package com.holderzone.member.common.exception;

import com.holderzone.member.common.enums.ResponseBase;
import com.holderzone.member.common.enums.mall.MemberMallExceptionEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.MemberTerminalExceptionEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @program: member-marketing
 * @description: 会员商城异常
 * @author: pan tao
 * @create: 2022-08-09 16:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MallBaseException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    protected int code;
    /**
     * 错误信息
     */
    protected String des;

    public MallBaseException() {
        super();
    }


    public MallBaseException(ResponseBase responseBase) {
        super(String.valueOf(responseBase.getCode()));
        this.code = responseBase.getCode();
        this.des = responseBase.getDes();
    }

    public MallBaseException(ResponseBase responseBase, Throwable cause) {
        super(String.valueOf(responseBase.getCode()), cause);
        this.code = responseBase.getCode();
        this.des = responseBase.getDes();
    }

    public MallBaseException(String des) {
        super(des);
        this.des = des;
    }

    public MallBaseException(MemberAccountExceptionEnum exceptionEnums) {
        super(exceptionEnums.getDes());
        this.code = exceptionEnums.getCode();
        this.des = exceptionEnums.getDes();
    }

    public MallBaseException(MemberTerminalExceptionEnum exceptionEnums) {
        super(exceptionEnums.getDes());
        this.code = exceptionEnums.getCode();
        this.des = exceptionEnums.getDes();
    }

    public MallBaseException(MemberMallExceptionEnum exceptionEnums) {
        super(exceptionEnums.getDes());
        this.code = exceptionEnums.getCode();
        this.des = exceptionEnums.getDes();
    }


    public MallBaseException(int code, String des) {
        super(String.valueOf(code));
        this.code = code;
        this.des = des;
    }

    public MallBaseException(int code, String des, Throwable cause) {
        super(String.valueOf(code), cause);
        this.code = code;
        this.des = des;
    }
}
