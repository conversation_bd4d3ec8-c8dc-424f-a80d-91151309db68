package com.holderzone.member.common.module.rights;


import com.google.common.collect.Lists;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.equities.EquitiesRuleTypeEnum;
import com.holderzone.member.common.enums.equities.EquitiesSetEnum;
import com.holderzone.member.common.enums.equities.EquitiesStatusEnum;
import com.holderzone.member.common.enums.equities.EquitiesTypeEnum;
import com.holderzone.member.common.enums.member.EquitiesSubjectTypeEnum;
import com.holderzone.member.common.qo.equities.ADDEquitiesInfoQO;
import com.holderzone.member.common.qo.equities.EquitiesRuleQO;
import com.holderzone.member.common.qo.equities.HsaEquitiesInfoQO;

import java.util.Objects;

/**
 * 权益中心初始化类型
 *
 * <AUTHOR>
 * @date 2024/1/10
 * @since 1.8
 */
public enum EquitiesInitEnum {

    /**
     * 会员折扣
     */
    MEMBER_PRICE(0, "会员折扣", initMemberDiscountQo()),

    //    
//    /**
//     * 满减
//     */
//    ENOUGH_REDUCE(1, "满减"),
//
//    /**
//     * 直减
//     */
//    DIRECT_REDUCE(2, "直减"),
//
//
//
//
    /**
     * 商品会员价
     */
    GOODS_MEMBER(3, "商品会员价", initMemberPriceQo()),


    /**
     * 赠送成长值
     */
    GIVE_GROWTH_VALUE(4, "赠送成长值", initGiveGrowthValueQo()),

    /**
     * 翻倍成长值
     */
    DOUBLE_GROWTH_VALUE(5, "翻倍成长值", initDoubleGrowthValueQo()),


    /**
     * 线下权益
     */
    OFFLINE_EQUITIES(6, "线下权益", initOfflineEquitiesQo()),

    /**
     * 赠送积分
     */
    GIVE_INTEGRAL(7, "赠送积分", initGiveIntegralQo()),

    /**
     * 翻倍积分
     */
    DOUBLE_INTEGRAL(8, "翻倍积分", initDoubleIntegralQo()),

    /**
     * 免运费
     */
    FREE_SHIPPING(9, "免运费", initConsumptionEquitiesQo()),

    /**
     * 优先发货
     */
    PRIORITY_SHIPPING(10, "优先发货", initPriorityShippingQo()),
    /**
     * 极速售后
     */
    FAST_AFTER_SALES(11, "极速售后", initFastAfterSalesQo()),
    /**
     * 专属客服
     */
    DEDICATED_CUSTOMER_SERVICE(12, "专属客服", initDedicatedCustomerServiceQo()),


    ;


    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     * 初始化名称
     */
    private final String des;

    /**
     * 初始化入参
     */
    private final ADDEquitiesInfoQO equitiesInfoQo;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    /**
     * 获取初始化入参
     *
     * @return 入参
     */
    public ADDEquitiesInfoQO getEquitiesInfoQo() {
        return equitiesInfoQo;
    }

    EquitiesInitEnum(int code, String des, ADDEquitiesInfoQO equitiesInfoQo) {
        this.code = code;
        this.des = des;
        //名称 
        equitiesInfoQo.getHsaEquitiesInfo().setEquitiesName(des);
        this.equitiesInfoQo = equitiesInfoQo;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 编号
     * @return 枚举
     */
    public static EquitiesInitEnum getEnumByCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (EquitiesInitEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }


    /**
     * 所有周期类型
     */
    public static final String PERIOD_TYPE = "-1,0,1,2,3";

    /**
     * 会员折扣 （前端入参）
     *
     * @return 权益
     */
    private static ADDEquitiesInfoQO initMemberDiscountQo() {
        ADDEquitiesInfoQO request = new ADDEquitiesInfoQO();

        //权益信息
        HsaEquitiesInfoQO hsaEquitiesInfo = new HsaEquitiesInfoQO();
        //hsaEquitiesInfo.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());

        hsaEquitiesInfo.setEquitiesImg("https://saleoss.holderzone.com/member-marketing/right-icon/指定商品@2x.png,https://saleoss.holderzone.com/member-marketing/right-icon/指定商品-灰色@2x.png");
        hsaEquitiesInfo.setEquitiesExplain("<p>享受会员折扣权益</p>");
        hsaEquitiesInfo.setEquitiesType(EquitiesSubjectTypeEnum.EQUITIES_ALL.getCode());
        hsaEquitiesInfo.setOperSubjectGuidList(Lists.newArrayList());
        hsaEquitiesInfo.setType(EquitiesTypeEnum.DISCOUNT_EQUITIES.getCode());
        hsaEquitiesInfo.setIsStatus(EquitiesStatusEnum.ENABLE.getCode());
        hsaEquitiesInfo.setIsDefault(BooleanEnum.TRUE.getCode());

        request.setHsaEquitiesInfo(hsaEquitiesInfo);

        //权益规则
        EquitiesRuleQO equitiesRule = new EquitiesRuleQO();
        equitiesRule.setType(EquitiesRuleTypeEnum.MEMBER_PRICE.getCode());
        equitiesRule.setGoodsDiscountSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setEnoughReduceSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setMultipleEnoughReduce(1);
        equitiesRule.setDirectReduceSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setMemberDiscountSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setAmountSatisfyScopeType("0,1");
        equitiesRule.setPartValuationType("0,1,2");
        equitiesRule.setApplyCommodity("0,1");
//        equitiesRule.setNoApplyCommodity("0,1");
        equitiesRule.setMostLimitedType("1,2,3,4");
        equitiesRule.setApplyTerminalType("0");
        equitiesRule.setApplyBusinessType("0");
        equitiesRule.setApplyChannelLimited("0");
        equitiesRule.setBusinessType("1");
        equitiesRule.setTerminalType("pos");
        equitiesRule.setApplyChannelType("pos,mall");
        equitiesRule.setSingleDiscountsLimitedRequired(0);
        equitiesRule.setSingleDiscountsLimitedSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setSingleDiscountsLimitedType("0,1");
        equitiesRule.setPeriodDiscountRequired(0);
        equitiesRule.setPeriodDiscountLimitedSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setPeriodDiscountType(PERIOD_TYPE);
        equitiesRule.setLimitedTimeType(PERIOD_TYPE);
        equitiesRule.setTotalMostLimitedTypeRequired(0);
        equitiesRule.setTotalMostLimitedTypeSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setTotalMostLimitedType("0,1");
        equitiesRule.setPeriodUseCountRequired(0);
        equitiesRule.setPeriodUseCountLimitedSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setPeriodUseCountType(PERIOD_TYPE);
        equitiesRule.setDiscountsSuperpositionRequired(0);
        equitiesRule.setDiscountsSuperpositionSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setDiscountsSuperpositionType("0,1");
        equitiesRule.setEquitiesTimeRequired(0);
        equitiesRule.setEquitiesTimeSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setEquitiesTimeLimitedType("0,1");
        equitiesRule.setApplyCommodityRequired(0);
        equitiesRule.setApplyCommoditySet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setApplyCommodityType("-1,0,1");
        equitiesRule.setApplyTerminalRequired(0);
        equitiesRule.setApplyTerminalSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setApplyBusinessRequired(0);
        equitiesRule.setApplyBusinessSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setApplyChannelRequired(0);
        equitiesRule.setApplyChannelSet(EquitiesSetEnum.UN_SET.getCode());

        equitiesRule.setStoreRequired(0);
        equitiesRule.setApplyStoreSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setApplyStoreType("0,1");

        equitiesRule.setRelationRuleSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setRelationRuleType("0,1");
        equitiesRule.setRelationRuleRequired(BooleanEnum.FALSE.getCode());

        request.setEquitiesRule(equitiesRule);

        return request;
    }

    /**
     * 会员价 （前端入参）
     *
     * @return 权益
     */
    private static ADDEquitiesInfoQO initMemberPriceQo() {
        ADDEquitiesInfoQO request = new ADDEquitiesInfoQO();

        //权益信息
        HsaEquitiesInfoQO hsaEquitiesInfo = new HsaEquitiesInfoQO();
        //hsaEquitiesInfo.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());

        hsaEquitiesInfo.setEquitiesImg("https://saleoss.holderzone.com/member-marketing/right-icon/指定商品@2x.png,https://saleoss.holderzone.com/member-marketing/right-icon/指定商品-灰色@2x.png");
        hsaEquitiesInfo.setEquitiesExplain("<p>享受会员价权益</p>");
        hsaEquitiesInfo.setEquitiesType(EquitiesSubjectTypeEnum.EQUITIES_ALL.getCode());
        hsaEquitiesInfo.setOperSubjectGuidList(Lists.newArrayList());
        hsaEquitiesInfo.setType(EquitiesTypeEnum.DISCOUNT_EQUITIES.getCode());
        hsaEquitiesInfo.setIsStatus(EquitiesStatusEnum.ENABLE.getCode());
        hsaEquitiesInfo.setIsDefault(BooleanEnum.TRUE.getCode());

        request.setHsaEquitiesInfo(hsaEquitiesInfo);

        //权益规则
        EquitiesRuleQO equitiesRule = new EquitiesRuleQO();
        equitiesRule.setType(EquitiesRuleTypeEnum.GOODS_MEMBER.getCode());
        equitiesRule.setGoodsDiscountSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setEnoughReduceSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setMultipleEnoughReduce(1);
        equitiesRule.setDirectReduceSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setMemberDiscountSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setAmountSatisfyScopeType("0,1");
        equitiesRule.setPartValuationType("0,1,2");
        equitiesRule.setApplyCommodity("0,1");
//        equitiesRule.setNoApplyCommodity("0,1");
        equitiesRule.setMostLimitedType("1,2,3,4");
        equitiesRule.setApplyTerminalType("0");
        equitiesRule.setApplyBusinessType("0,1");
        equitiesRule.setApplyChannelLimited("0");
        equitiesRule.setBusinessType("1");
        equitiesRule.setTerminalType("pos");
        equitiesRule.setApplyChannelType("pos,mall");
        equitiesRule.setSingleDiscountsLimitedRequired(0);
        equitiesRule.setSingleDiscountsLimitedSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setSingleDiscountsLimitedType("0,1");
        equitiesRule.setPeriodDiscountRequired(0);
        equitiesRule.setPeriodDiscountLimitedSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setPeriodDiscountType(PERIOD_TYPE);
        equitiesRule.setLimitedTimeType(PERIOD_TYPE);
        equitiesRule.setTotalMostLimitedTypeRequired(0);
        equitiesRule.setTotalMostLimitedTypeSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setTotalMostLimitedType("0,1");
        equitiesRule.setPeriodUseCountRequired(0);
        equitiesRule.setPeriodUseCountLimitedSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setPeriodUseCountType(PERIOD_TYPE);
        equitiesRule.setDiscountsSuperpositionRequired(0);
        equitiesRule.setDiscountsSuperpositionSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setDiscountsSuperpositionType("0,1");
        equitiesRule.setEquitiesTimeRequired(0);
        equitiesRule.setEquitiesTimeSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setEquitiesTimeLimitedType("0,1");
        equitiesRule.setApplyCommodityRequired(0);
        equitiesRule.setApplyCommoditySet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setApplyCommodityType("-1,0,1");
        equitiesRule.setApplyTerminalRequired(0);
        equitiesRule.setApplyTerminalSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setApplyBusinessRequired(0);
        equitiesRule.setApplyBusinessSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setApplyChannelRequired(0);
        equitiesRule.setApplyChannelSet(EquitiesSetEnum.UN_SET.getCode());

        equitiesRule.setStoreRequired(0);
        equitiesRule.setApplyStoreSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setApplyStoreType("0,1");

        equitiesRule.setRelationRuleSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setRelationRuleType("0,1");
        equitiesRule.setRelationRuleRequired(BooleanEnum.FALSE.getCode());

        request.setEquitiesRule(equitiesRule);

        return request;
    }

    /**
     * 赠送成长值 （前端入参）
     *
     * @return 权益
     */
    private static ADDEquitiesInfoQO initGiveGrowthValueQo() {
        ADDEquitiesInfoQO request = new ADDEquitiesInfoQO();

        //权益信息
        HsaEquitiesInfoQO hsaEquitiesInfo = new HsaEquitiesInfoQO();
        //hsaEquitiesInfo.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        //hsaEquitiesInfo.setEquitiesName(GIVE_GROWTH_VALUE.getDes());
        hsaEquitiesInfo.setEquitiesImg("https://saleoss.holderzone.com/member-marketing/right-icon/赠送成长值@2x.png,https://saleoss.holderzone.com/member-marketing/right-icon/赠送成长值-灰色@2x.png");
        hsaEquitiesInfo.setEquitiesExplain("<p>享受赠送成长值权益</p>");
        hsaEquitiesInfo.setEquitiesType(EquitiesSubjectTypeEnum.EQUITIES_ALL.getCode());
        hsaEquitiesInfo.setOperSubjectGuidList(Lists.newArrayList());
        hsaEquitiesInfo.setType(EquitiesTypeEnum.GROWTH_VALUE_EQUITIES.getCode());
        hsaEquitiesInfo.setIsStatus(EquitiesStatusEnum.ENABLE.getCode());
        hsaEquitiesInfo.setIsDefault(BooleanEnum.TRUE.getCode());

        request.setHsaEquitiesInfo(hsaEquitiesInfo);

        //权益规则
        EquitiesRuleQO equitiesRule = new EquitiesRuleQO();
        equitiesRule.setType(EquitiesRuleTypeEnum.GIVE_GROWTH_VALUE.getCode());
        equitiesRule.setSingleGiveNumberSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setGiveWaySet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setPeriodType(PERIOD_TYPE);
        equitiesRule.setDoubleMultipleSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setTotalGiveType("0,1");
        equitiesRule.setSingleDoubleLimitedType("0,1");
        equitiesRule.setDoubleCountType("0,1");
        equitiesRule.setTotalDoubleLimitedType("0,1");
        equitiesRule.setDoubleCountSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setSingleDoubleLimitedSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setTotalDoubleLimitedSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setDoubleCountRequired(0);
        equitiesRule.setSingleDoubleLimitedRequired(0);
        equitiesRule.setTotalDoubleLimitedRequired(0);

        request.setEquitiesRule(equitiesRule);

        return request;
    }

    /**
     * 翻倍成长值 （前端入参）
     *
     * @return 权益
     */
    private static ADDEquitiesInfoQO initDoubleGrowthValueQo() {
        ADDEquitiesInfoQO request = new ADDEquitiesInfoQO();

        //权益信息
        HsaEquitiesInfoQO hsaEquitiesInfo = new HsaEquitiesInfoQO();
        //hsaEquitiesInfo.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        //hsaEquitiesInfo.setEquitiesName(DOUBLE_GROWTH_VALUE.getDes());
        hsaEquitiesInfo.setEquitiesImg("https://saleoss.holderzone.com/member-marketing/right-icon/翻倍成长值@2x .png,https://saleoss.holderzone.com/member-marketing/right-icon/翻倍成长值-灰色@2x.png");
        hsaEquitiesInfo.setEquitiesExplain("<p>享受成长值翻倍权益</p>");
        hsaEquitiesInfo.setEquitiesType(EquitiesSubjectTypeEnum.EQUITIES_ALL.getCode());
        hsaEquitiesInfo.setOperSubjectGuidList(Lists.newArrayList());
        hsaEquitiesInfo.setType(EquitiesTypeEnum.GROWTH_VALUE_EQUITIES.getCode());
        hsaEquitiesInfo.setIsStatus(EquitiesStatusEnum.ENABLE.getCode());
        hsaEquitiesInfo.setIsDefault(BooleanEnum.TRUE.getCode());

        request.setHsaEquitiesInfo(hsaEquitiesInfo);

        //权益规则
        EquitiesRuleQO equitiesRule = new EquitiesRuleQO();
        equitiesRule.setType(EquitiesRuleTypeEnum.DOUBLE_GROWTH_VALUE.getCode());
        equitiesRule.setSingleGiveNumberSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setGiveWaySet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setPeriodType(PERIOD_TYPE);
        equitiesRule.setDoubleMultipleSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setTotalGiveType("0,1");
        equitiesRule.setSingleDoubleLimitedType("0,1");
        equitiesRule.setDoubleCountType("0,1");
        equitiesRule.setTotalDoubleLimitedType("0,1");
        equitiesRule.setDoubleCountSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setSingleDoubleLimitedSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setTotalDoubleLimitedSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setDoubleCountRequired(0);
        equitiesRule.setSingleDoubleLimitedRequired(0);
        equitiesRule.setTotalDoubleLimitedRequired(0);

        request.setEquitiesRule(equitiesRule);

        return request;
    }

    /**
     * 线下权益 （前端入参）
     *
     * @return 权益
     */
    private static ADDEquitiesInfoQO initOfflineEquitiesQo() {
        ADDEquitiesInfoQO request = new ADDEquitiesInfoQO();

        //权益信息
        HsaEquitiesInfoQO hsaEquitiesInfo = new HsaEquitiesInfoQO();
        //hsaEquitiesInfo.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        //hsaEquitiesInfo.setEquitiesName(OFFLINE_EQUITIES.getDes());
        hsaEquitiesInfo.setEquitiesImg("https://saleoss.holderzone.com/member-marketing/right-icon/线下权益@2x.png,https://saleoss.holderzone.com/member-marketing/right-icon/线下权益-灰色@2x.png");
        hsaEquitiesInfo.setEquitiesExplain("");
        hsaEquitiesInfo.setEquitiesType(EquitiesSubjectTypeEnum.EQUITIES_ALL.getCode());
        hsaEquitiesInfo.setOperSubjectGuidList(Lists.newArrayList());
        hsaEquitiesInfo.setType(EquitiesTypeEnum.OFFLINE_EQUITIES.getCode());
        hsaEquitiesInfo.setIsStatus(EquitiesStatusEnum.ENABLE.getCode());

        request.setHsaEquitiesInfo(hsaEquitiesInfo);
        return request;
    }


    /**
     * 免运费 （前端入参）
     *
     * @return 权益
     */
    private static ADDEquitiesInfoQO initConsumptionEquitiesQo() {
        ADDEquitiesInfoQO request = new ADDEquitiesInfoQO();

        //权益信息
        HsaEquitiesInfoQO hsaEquitiesInfo = new HsaEquitiesInfoQO();
        hsaEquitiesInfo.setEquitiesImg("https://saleoss.holderzone.com/member-marketing/right-icon/物流优惠1.png,https://saleoss.holderzone.com/member-marketing/right-icon/物流优惠.png");
        hsaEquitiesInfo.setEquitiesExplain("<p>享受快递配送费优惠</p>");
        hsaEquitiesInfo.setEquitiesType(EquitiesSubjectTypeEnum.EQUITIES_ALL.getCode());
        hsaEquitiesInfo.setOperSubjectGuidList(Lists.newArrayList());
        hsaEquitiesInfo.setType(EquitiesTypeEnum.CONSUMPTION_EQUITIES.getCode());
        hsaEquitiesInfo.setIsStatus(EquitiesStatusEnum.ENABLE.getCode());
        hsaEquitiesInfo.setIsDefault(BooleanEnum.TRUE.getCode());
        hsaEquitiesInfo.setEquitiesName("免运费");

        request.setHsaEquitiesInfo(hsaEquitiesInfo);

        //权益规则
        EquitiesRuleQO equitiesRule = new EquitiesRuleQO();
        equitiesRule.setType(EquitiesRuleTypeEnum.FREE_SHIPPING.getCode());

        request.setEquitiesRule(equitiesRule);

        return request;
    }

    /**
     * 优先发货
     *
     * @return
     */
    private static ADDEquitiesInfoQO initPriorityShippingQo() {
        ADDEquitiesInfoQO request = new ADDEquitiesInfoQO();

        //权益信息
        HsaEquitiesInfoQO hsaEquitiesInfo = new HsaEquitiesInfoQO();
        hsaEquitiesInfo.setEquitiesImg("https://saleoss.holderzone.com/member-marketing/right-icon/优先发货1.png,https://saleoss.holderzone.com/member-marketing/right-icon/优先发货.png");
        hsaEquitiesInfo.setEquitiesExplain("<p>享受商城订单优先发货权益</p>");
        hsaEquitiesInfo.setEquitiesType(EquitiesSubjectTypeEnum.EQUITIES_ALL.getCode());
        hsaEquitiesInfo.setOperSubjectGuidList(Lists.newArrayList());
        hsaEquitiesInfo.setType(EquitiesTypeEnum.SHOPPING_EQUITIES.getCode());
        hsaEquitiesInfo.setIsStatus(EquitiesStatusEnum.ENABLE.getCode());
        hsaEquitiesInfo.setIsDefault(BooleanEnum.TRUE.getCode());
        hsaEquitiesInfo.setEquitiesName("优先发货");

        request.setHsaEquitiesInfo(hsaEquitiesInfo);

        EquitiesRuleQO equitiesRule = new EquitiesRuleQO();
        equitiesRule.setType(EquitiesRuleTypeEnum.PRIORITY_SHIPPING.getCode());
        request.setEquitiesRule(equitiesRule);

        return request;
    }


    /**
     * 极速售后
     *
     * @return
     */
    private static ADDEquitiesInfoQO initFastAfterSalesQo() {
        ADDEquitiesInfoQO request = new ADDEquitiesInfoQO();

        //权益信息
        HsaEquitiesInfoQO hsaEquitiesInfo = new HsaEquitiesInfoQO();
        hsaEquitiesInfo.setEquitiesImg("https://saleoss.holderzone.com/member-marketing/right-icon/极速售后1.png,https://saleoss.holderzone.com/member-marketing/right-icon/极速售后.png");
        hsaEquitiesInfo.setEquitiesExplain("<p>享受商城订单极速售后权益</p>");
        hsaEquitiesInfo.setEquitiesType(EquitiesSubjectTypeEnum.EQUITIES_ALL.getCode());
        hsaEquitiesInfo.setOperSubjectGuidList(Lists.newArrayList());
        hsaEquitiesInfo.setType(EquitiesTypeEnum.SHOPPING_EQUITIES.getCode());
        hsaEquitiesInfo.setIsStatus(EquitiesStatusEnum.ENABLE.getCode());
        hsaEquitiesInfo.setIsDefault(BooleanEnum.TRUE.getCode());
        hsaEquitiesInfo.setEquitiesName("极速售后");

        request.setHsaEquitiesInfo(hsaEquitiesInfo);

        EquitiesRuleQO equitiesRule = new EquitiesRuleQO();
        equitiesRule.setType(EquitiesRuleTypeEnum.FAST_AFTER_SALES.getCode());
        request.setEquitiesRule(equitiesRule);

        return request;
    }


    /**
     * 专属客服
     *
     * @return
     */
    private static ADDEquitiesInfoQO initDedicatedCustomerServiceQo() {
        ADDEquitiesInfoQO request = new ADDEquitiesInfoQO();

        //权益信息
        HsaEquitiesInfoQO hsaEquitiesInfo = new HsaEquitiesInfoQO();
        hsaEquitiesInfo.setEquitiesImg("https://saleoss.holderzone.com/member-marketing/right-icon/专属客服1.png,https://saleoss.holderzone.com/member-marketing/right-icon/专属客服.png");
        hsaEquitiesInfo.setEquitiesExplain("<p>享受商城订单专属客服权益</p>");
        hsaEquitiesInfo.setEquitiesType(EquitiesSubjectTypeEnum.EQUITIES_ALL.getCode());
        hsaEquitiesInfo.setOperSubjectGuidList(Lists.newArrayList());
        hsaEquitiesInfo.setType(EquitiesTypeEnum.SHOPPING_EQUITIES.getCode());
        hsaEquitiesInfo.setIsStatus(EquitiesStatusEnum.ENABLE.getCode());
        hsaEquitiesInfo.setIsDefault(BooleanEnum.TRUE.getCode());
        hsaEquitiesInfo.setEquitiesName("专属客服");

        request.setHsaEquitiesInfo(hsaEquitiesInfo);

        EquitiesRuleQO equitiesRule = new EquitiesRuleQO();
        equitiesRule.setType(EquitiesRuleTypeEnum.DEDICATED_CUSTOMER_SERVICE.getCode());
        request.setEquitiesRule(equitiesRule);

        return request;
    }

    /**
     * 赠送积分 （前端入参）
     *
     * @return 权益
     */
    private static ADDEquitiesInfoQO initGiveIntegralQo() {
        ADDEquitiesInfoQO request = new ADDEquitiesInfoQO();

        //权益信息
        HsaEquitiesInfoQO hsaEquitiesInfo = new HsaEquitiesInfoQO();
        //hsaEquitiesInfo.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        //hsaEquitiesInfo.setEquitiesName(GIVE_INTEGRAL.getDes());
        hsaEquitiesInfo.setEquitiesImg("https://saleoss.holderzone.com/member-marketing/right-icon/赠送积分@2x.png,https://saleoss.holderzone.com/member-marketing/right-icon/赠送积分-灰色@2x.png");
        hsaEquitiesInfo.setEquitiesExplain("<p>享受赠送积分权益</p>");
        hsaEquitiesInfo.setEquitiesType(EquitiesSubjectTypeEnum.EQUITIES_ALL.getCode());
        hsaEquitiesInfo.setOperSubjectGuidList(Lists.newArrayList());
        hsaEquitiesInfo.setType(EquitiesTypeEnum.INTEGRAL_EQUITIES.getCode());
        hsaEquitiesInfo.setIsStatus(EquitiesStatusEnum.ENABLE.getCode());
        hsaEquitiesInfo.setIsDefault(BooleanEnum.TRUE.getCode());

        request.setHsaEquitiesInfo(hsaEquitiesInfo);

        //权益规则
        EquitiesRuleQO equitiesRule = new EquitiesRuleQO();
        equitiesRule.setType(EquitiesRuleTypeEnum.GIVE_INTEGRAL.getCode());
        equitiesRule.setSingleGiveNumberSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setGiveWaySet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setPeriodType(PERIOD_TYPE);
        equitiesRule.setDoubleMultipleSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setTotalGiveType("0,1");
        equitiesRule.setSingleDoubleLimitedType("0,1");
        equitiesRule.setDoubleCountType("0,1");
        equitiesRule.setTotalDoubleLimitedType("0,1");
        equitiesRule.setDoubleCountSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setSingleDoubleLimitedSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setTotalDoubleLimitedSet(EquitiesSetEnum.UN_SET.getCode());
        equitiesRule.setDoubleCountRequired(0);
        equitiesRule.setSingleDoubleLimitedRequired(0);
        equitiesRule.setTotalDoubleLimitedRequired(0);

        request.setEquitiesRule(equitiesRule);
        return request;
    }

    /**
     * 翻倍积分 （前端入参）
     *
     * @return 权益
     */
    private static ADDEquitiesInfoQO initDoubleIntegralQo() {
        ADDEquitiesInfoQO request = new ADDEquitiesInfoQO();

        //权益信息
        HsaEquitiesInfoQO hsaEquitiesInfo = new HsaEquitiesInfoQO();
        //hsaEquitiesInfo.setEnterpriseGuid(ThreadLocalCache.getEnterpriseGuid());
        //hsaEquitiesInfo.setEquitiesName(DOUBLE_INTEGRAL.getDes());
        hsaEquitiesInfo.setEquitiesImg("https://saleoss.holderzone.com/member-marketing/right-icon/翻倍积分@2xn.png,https://saleoss.holderzone.com/member-marketing/right-icon/翻倍积分-灰色@2xn.png");
        hsaEquitiesInfo.setEquitiesExplain("<p>享受积分翻倍权益</p>");
        hsaEquitiesInfo.setEquitiesType(EquitiesSubjectTypeEnum.EQUITIES_ALL.getCode());
        hsaEquitiesInfo.setOperSubjectGuidList(Lists.newArrayList());
        hsaEquitiesInfo.setType(EquitiesTypeEnum.INTEGRAL_EQUITIES.getCode());
        hsaEquitiesInfo.setIsStatus(EquitiesStatusEnum.ENABLE.getCode());
        hsaEquitiesInfo.setIsDefault(BooleanEnum.TRUE.getCode());

        request.setHsaEquitiesInfo(hsaEquitiesInfo);

        //权益规则
        EquitiesRuleQO equitiesRule = new EquitiesRuleQO();
        equitiesRule.setType(EquitiesRuleTypeEnum.DOUBLE_INTEGRAL.getCode());
        equitiesRule.setSingleGiveNumberSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setGiveWaySet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setPeriodType(PERIOD_TYPE);
        equitiesRule.setDoubleMultipleSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setTotalGiveType("0,1");
        equitiesRule.setSingleDoubleLimitedType("0,1");
        equitiesRule.setDoubleCountType("0,1");
        equitiesRule.setTotalDoubleLimitedType("0,1");
        equitiesRule.setDoubleCountSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setSingleDoubleLimitedSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setTotalDoubleLimitedSet(EquitiesSetEnum.SET.getCode());
        equitiesRule.setDoubleCountRequired(0);
        equitiesRule.setSingleDoubleLimitedRequired(0);
        equitiesRule.setTotalDoubleLimitedRequired(0);

        request.setEquitiesRule(equitiesRule);
        return request;
    }
}
