package com.holderzone.member.common.module.marketing.purchase.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 限购提醒
 *
 * <AUTHOR>
 */
@Data
public class PurchaseStateNoticeVO implements Serializable {


    private static final long serialVersionUID = 794444978906778L;
//    /**
//     * 是否临近时间：3天内
//     */
//    private Boolean approachingTime;

    /**
     * 重复活动的商品
     */
    private List<PurchaseCommodityVO> duplicateCommodities;
}


