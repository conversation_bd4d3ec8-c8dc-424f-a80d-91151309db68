package com.holderzone.member.common.enums.integral;

import java.util.Objects;

/**
 * 积分过期提醒方式
 * <AUTHOR>
 */

public enum RemindTheWayEnum {

    /**
     * 短信通知
     */
    SHORT_MESSAGE_NOTICE(1,"短信通知"),

    /**
     * 微信公众号通知
     */
    NOTICE_WECHAT_OFFICIAL_ACCOUNT(2,"微信公众号通知"),

    /**
     * 微信小程序通知
     */
    MINI_PROGRAM_NOTIFICATION(3,"微信小程序通知"),

    /**
     * 未知消息
     */
    UNKNOWN(-1,"未知消息"),
    ;
    private final int code;

    private final String des;

    RemindTheWayEnum(int code, String des){
        this.code = code;
        this.des = des;
    }

    public int getCode(){
        return code;
    }

    public String getDes(){
        return des;
    }

    public int getCodeByDes(String des){
        if (Objects.isNull(des)) {
            return UNKNOWN.getCode();
        }
        RemindTheWayEnum[] wayEnums = RemindTheWayEnum.values();
        for (RemindTheWayEnum wayEnum : wayEnums) {
            if (wayEnum.getDes().equals(des)) {
                return wayEnum.getCode();
            }
        }
        return UNKNOWN.getCode();
    }

    public String getDesByCode(Integer code){
        if (Objects.isNull(code)) {
            return UNKNOWN.getDes();
        }
        RemindTheWayEnum[] wayEnums = RemindTheWayEnum.values();
        for (RemindTheWayEnum wayEnum : wayEnums) {
            if (code.equals(wayEnum.getCode())) {
                return wayEnum.getDes();
            }
        }
        return UNKNOWN.getDes();
    }


}
