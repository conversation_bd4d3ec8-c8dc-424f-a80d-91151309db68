package com.holderzone.member.common.module.settlement.rule.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 结算规则已选优惠项
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SettlementRuleDiscountTreeVO extends SettlementRuleDiscountVO {

    /**
     * 父级
     */
    private String parentGuid;

    /**
     * 子级
     */
    private List<SettlementRuleDiscountTreeVO> children;
}
