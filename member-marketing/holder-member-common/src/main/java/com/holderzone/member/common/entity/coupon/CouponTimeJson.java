package com.holderzone.member.common.entity.coupon;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 优惠券应用时间
 *
 * <AUTHOR>
 * @date 2023/10/25
 * @since 1.8
 */
@NoArgsConstructor
@Data
public class CouponTimeJson {

    /**
     * 周期：
     */
    @JsonProperty("type")
    private List<Integer> type;

    /**
     * 时间段
     */
    @JsonProperty("value")
    private String[] value;
}
