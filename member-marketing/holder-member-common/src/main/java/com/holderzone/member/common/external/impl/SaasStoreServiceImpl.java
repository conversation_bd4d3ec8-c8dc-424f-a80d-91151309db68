package com.holderzone.member.common.external.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.util.Page;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.dto.crm.*;
import com.holderzone.member.common.dto.grade.StoreInfoDTO;
import com.holderzone.member.common.dto.member.ResponseStoreInfo;
import com.holderzone.member.common.dto.order.CrmOrderDTO;
import com.holderzone.member.common.dto.order.CrmOrderStockDTO;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.exception.FollowActivityExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalStoreService;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.feign.SaasStoreFeign;
import com.holderzone.member.common.qo.crm.CrmOrderDetailQo;
import com.holderzone.member.common.qo.crm.CrmOrderDetailVo;
import com.holderzone.member.common.qo.growth.AppletGrowthStoreQO;
import com.holderzone.member.common.qo.tool.StoreByIdQO;
import com.holderzone.member.common.util.number.LimitUtil;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import com.holderzone.member.common.vo.tool.OperSubjectVO;
import com.holderzone.member.common.vo.wechat.PaySettingVO;
import com.holderzone.member.common.vo.wechat.WeChatConfigInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description 餐饮云
 */
@Slf4j
@Service("saasStoreServiceImpl")
@RequiredArgsConstructor
public class SaasStoreServiceImpl implements ExternalStoreService {

    private final SaasStoreFeign saasStoreFeign;

    private final MemberMallToolFeign memberMallToolFeign;

    @Override
    public List<StoreBaseInfo> listStore(QueryStoreBasePage query) {
        log.info("餐饮云获取门店==============>" + JSON.toJSONString(query));
        List<StoreBaseInfo> storeBaseInfos = queryStore(Strings.EMPTY);
        if (CollectionUtils.isNotEmpty(query.getStoreIds())) {
            storeBaseInfos = storeBaseInfos.stream()
                    .filter(e -> query.getStoreIds().contains(e.getStoreGuid()))
                    .collect(Collectors.toList());
        }
        return storeBaseInfos;
    }

    @Override
    public Page<StoreBaseInfo> storePage(QueryStoreBasePage query) {
        List<StoreBaseInfo> storeBaseInfos = queryStore(query.getName());
        List<StoreBaseInfo> limitList = LimitUtil.getLimitList(storeBaseInfos, query.getPage(), query.getPageSize());
        return new Page<>(query.getPage(), query.getPageSize(), storeBaseInfos.size(), limitList);
    }

    @Override
    public List<StoreBaseInfo> listStoreAndStall(QueryStoreBasePage query) {
        log.info("餐饮云获取所有门店==============>" + JSON.toJSONString(query));
        return queryStore(query.getName());
    }

    @Override
    public AppIdRespDTO getAppId(CrmAppIdQueryDTO crmAppIdReqDTO) {
        log.info("餐饮云获取appid==============>" + JSON.toJSONString(crmAppIdReqDTO));

        AppIdRespDTO appIdRespDTO = new AppIdRespDTO();
        Result<WeChatConfigInfoVO> weChatConfigInfoVOResult = memberMallToolFeign.queryByOperSubjectGuid();
        log.info("getAppId:{}", JSON.toJSONString(weChatConfigInfoVOResult));

        if (Objects.isNull(weChatConfigInfoVOResult) || Objects.isNull(weChatConfigInfoVOResult.getData())) {
            return null;
        }
        WeChatConfigInfoVO weChatConfigInfoVO = weChatConfigInfoVOResult.getData();
        appIdRespDTO.setAppId(weChatConfigInfoVO.getAppId());
        appIdRespDTO.setAppsecret(weChatConfigInfoVO.getApplyPrivateKey());
        appIdRespDTO.setAppName(weChatConfigInfoVO.getAppName());
        appIdRespDTO.setAppLogo(weChatConfigInfoVO.getAppLogo());
        return appIdRespDTO;
    }

    @Override
    public ResAppletOrderCallBack appletOrderCallBack(AppletOrderCallBack query) {
        log.info("餐饮云订单回调==============>" + JSON.toJSONString(query));
        return null;
    }

    @Override
    public List<StoreBaseInfo> getStoreByStrategyOrCommodity(AppletGrowthStoreQO query) {
        log.info("餐饮云获取门店==============>" + JSON.toJSONString(query));
        return Collections.emptyList();
    }

    @Override
    public PaySettingBaseRes getPaySetting(PaySettingDTO paySettingDTO) {
        log.info("餐饮云获取支付配置==============>" + JSON.toJSONString(paySettingDTO));
        PaySettingBaseRes paySettingBaseRes = new PaySettingBaseRes();
        PaySettingVO paySettingVO = memberMallToolFeign.getPaySetting(paySettingDTO);
        log.info("getPaySetting:{}", JSON.toJSONString(paySettingVO));

        if (Objects.isNull(paySettingVO)) {
            return null;
        }
        paySettingBaseRes.setAppId(paySettingVO.getAppId());
        paySettingBaseRes.setAppSecret(paySettingVO.getApplyPrivateKey());
        paySettingBaseRes.setPayMerchantNum(paySettingVO.getPayMerchantNum());
        paySettingBaseRes.setPayMerchantKey(paySettingVO.getPayMerchantKey());
        return paySettingBaseRes;
    }

    @Override
    public int refundAggPayOrder(CrmRefundPayDTO crmRefundPayDTO) {
        log.info("餐饮云退款==============>" + JSON.toJSONString(crmRefundPayDTO));
        return 0;
    }

    @Override
    public void getReceiptPrinting(ReceiptPrintingBaseDTO receiptPrintingBaseDTO) {
        log.info("餐饮云打印小票==============>" + JSON.toJSONString(receiptPrintingBaseDTO));
    }

    @Override
    public List<StoreInfoVO> listStoreStall(StoreInfoDTO storeInfoDTO) {
        log.info("餐饮云查询店铺和档口==============>" + JSON.toJSONString(storeInfoDTO));
        return Collections.emptyList();
    }

    @Override
    public CrmOperatingSubjectRespDTO getOperatingSubject(CrmOperatingSubjectQueryDTO subjectReqDTO) {
        log.info("餐饮云查询主体==============>" + JSON.toJSONString(subjectReqDTO));
        return null;
    }

    @Override
    public CrmOrderDetailVo getOrderDetail(CrmOrderDetailQo crmOrderDetailQo) {
        log.info("餐饮云查询订单详情==============>" + JSON.toJSONString(crmOrderDetailQo));
        return null;
    }

    @Override
    public List<ResOrderCommodity> queryOrderCommodity(QueryOrderCommodity queryOrderCommodity) {
        log.info("餐饮云查询订单商品==============>" + JSON.toJSONString(queryOrderCommodity));
        return Collections.emptyList();
    }

    /**
     * @description: 查询主体下的店铺和档口
     * @author: li ao
     * @date: 2024/3/18 14:48
     * @param: query
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.vo.grade.StoreInfoVO>
     **/
    @Override
    public List<StoreInfoVO> getStoreStall(StoreInfoDTO query) {
        log.info("餐饮云查询主体下的店铺和档口==============>" + JSON.toJSONString(query));
        return Collections.emptyList();
    }

    /**
     * @description: 查询门店基础信息
     * @author: li ao
     * @date: 2024/3/18 14:56
     * @param: storageByIdQuery
     * @return: java.lang.Object
     **/
    @Override
    public Object getStoreById(StoreByIdQO storageByIdQuery) {
        log.info("餐饮云查询主体下的门店==============>" + JSON.toJSONString(storageByIdQuery));
        return null;
    }


    /**
     * @description: 查询主体下所有门店
     * @author: li ao
     * @date: 2024/3/18 15:15
     * @param: query
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.vo.grade.StoreInfoVO>
     **/
    @Override
    public List<StoreInfoVO> queryStoreV2(QueryStoreBasePage query) {
        log.info("餐饮云查询主体下所有门店==============>" + JSON.toJSONString(query));
        return Collections.emptyList();
    }

    /**
     * @description: 查询门店
     * @author: li ao
     * @date: 2024/3/18 15:24
     * @param: queryStoreBasePage
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.dto.base.StoreBaseInfo>
     **/
    @Override
    public List<StoreBaseInfo> queryStore(QueryStoreBasePage queryStoreBasePage) {
        log.info("餐饮云查询门店==============>" + JSON.toJSONString(queryStoreBasePage));
        return Collections.emptyList();
    }

    /**
     * @description: 创建订单
     * @author: li ao
     * @date: 2024/3/18 16:19
     * @param: crmOrderDTO
     * @return: com.alibaba.fastjson.JSONObject
     **/
    @Override
    public JSONObject pushOrder(CrmOrderDTO crmOrderDTO) {
        log.info("餐饮云创建订单==============>" + JSON.toJSONString(crmOrderDTO));
        return new JSONObject();
    }


    /**
     * @description: 同步状态 | 同步库存
     * @author: li ao
     * @date: 2024/3/18 16:20
     * @param: crmOrderStockDTO
     * @return: com.alibaba.fastjson.JSONObject
     **/
    @Override
    public JSONObject pushOrderStock(CrmOrderStockDTO crmOrderStockDTO) {
        log.info("餐饮云同步状态 | 同步库存==============>" + JSON.toJSONString(crmOrderStockDTO));
        return new JSONObject();
    }

    @Override
    public List<StoreBaseInfo> queryStore(String name) {
        List<StoreBaseInfo> storeBaseInfos = new ArrayList<>();
        try {
            List<ResponseStoreInfo> responseStoreInfos = saasStoreFeign.queryStoresBySubject(StringUtils.EMPTY, false).getData();
            log.info("查询餐饮云返回结果==============>" + JSON.toJSONString(responseStoreInfos));
            if (CollUtil.isEmpty(responseStoreInfos)) {
                return storeBaseInfos;
            }
            // 筛选
            if (StringUtils.isNotEmpty(name)) {
                responseStoreInfos = responseStoreInfos.stream()
                        .filter(e -> e.getName().contains(name) || e.getStoreNumber().contains(name))
                        .collect(Collectors.toList());
            }
            for (ResponseStoreInfo responseStoreInfo : responseStoreInfos) {
                storeBaseInfos.add(toStoreBaseInfo(responseStoreInfo));
            }
            return storeBaseInfos;
        } catch (Exception e) {
            log.error("查询餐饮云报错", e);
            throw new MemberBaseException(FollowActivityExceptionEnum.QUERY_STORE_ERROR);
        }
    }

    private StoreBaseInfo toStoreBaseInfo(ResponseStoreInfo storeDTO) {
        StoreBaseInfo storeBaseInfo = new StoreBaseInfo();
        storeBaseInfo.setId(storeDTO.getGuid());
        storeBaseInfo.setStoreGuid(storeDTO.getGuid());
        storeBaseInfo.setStoreNumber(storeDTO.getStoreNumber());
        storeBaseInfo.setStoreName(storeDTO.getName());
        storeBaseInfo.setAddress(storeDTO.getAddressDetail());
        storeBaseInfo.setStoreGuid(storeDTO.getGuid());
        storeBaseInfo.setSystem(SystemEnum.REPAST.name());
        storeBaseInfo.setChannel(SystemEnum.REPAST.getDes());
        if (Objects.nonNull(storeDTO.getLongitude()) && Objects.nonNull(storeDTO.getLatitude())) {
            storeBaseInfo.setAddressPoint(storeDTO.getLongitude() + StringConstant.COMMA + storeDTO.getLatitude());
        }
        return storeBaseInfo;
    }

    @Override
    public List<OperSubjectVO> listOperSubjectAndApplet() {
        return Collections.emptyList();
    }

    @Override
    public List<QueryOrderDTO> queryOrder(QueryOrderCommodity queryOrderCommodity) {
        return Collections.emptyList();
    }

    @Override
    public StoreCountDTO countStores() {
        List<StoreBaseInfo> stores = listStoreAndStall(new QueryStoreBasePage());
        int count = CollUtil.isEmpty(stores) ? 0 : stores.size();
        return new StoreCountDTO()
                .setRetailStoreCount(0)
                .setMallStoreCount(0)
                .setRepastStoreCount(count)
                .setTotalCount(count);
    }
}
