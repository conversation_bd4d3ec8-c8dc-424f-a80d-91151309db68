package com.holderzone.member.common.constant;

/**
 * <AUTHOR>
 * @description 成长值常量
 * @date 2021/11/24
 */
public final class GrowthValueConstant {

    public final static String GROWTH_VALUE_DEFAULT_NAME = "成长值";

    public final static String GROWTH_DESCRIPTION_REGISTER = "完成注册，获得%s%s";

    public final static String GROWTH_DESCRIPTION_TOTAL_RECHARGE = "再充值%s元，获得%s%s";

    public final static String GROWTH_DESCRIPTION_SINGLE_RECHARGE = "每充值%s元，获得%s%s";

    public final static String GROWTH_DESCRIPTION_CONSUMPTION_COUNT = "再消费%s笔，获得%s%s";

    public final static String GROWTH_DESCRIPTION_CONSUMPTION_AMOUNT = "再消费%s元，获得%s%s";

    public final static String GROWTH_DESCRIPTION_SPECIFIED_GOODS = "消费指定商品获得大量%s";

    public final static String GROWTH_DESCRIPTION_SINGLE_CONSUMPTION = "每消费%s元，获得%s%s";

    public final static String GROWTH_DESCRIPTION_PERFECT_PERSONAL = "完善%s信息，获得%s%s";

    public final static String GROWTH_TASK_VALIDITY_PERMANENT = "永久有效";

    public final static String GROWTH_TASK_VALIDITY_FIXED = "%s结束";

    public final static String GROWTH_TASK_SURPLUS_DAY = "剩余%s天";

    public final static String VALIDITY_FIXED = "%s小时";

    public final static String SURPLUS_DAY = "%s天";

    public final static String GROWTH_TASK_SURPLUS_HOUR = "剩余%sh";

    public final static String GROWTH_TASK_SURPLUS_IMMEDIATELY = "即将结束";

    public final static String GROWTH_VALUE_RULE_REGISTER = "新人注册成功赠送%s%s";

    /**
     * 每日首次分享获得
     */
    public static final String GROWTH_VALUE_RULE_SHARE = "每日首次分享获得%s%s";

    /**
     * 每日首次登录获得
     */
    public static final String GROWTH_VALUE_RULE_LOGIN = "每日首次登录获得%s%s";

    /**
     * 第n天连续登录额外获赠n积分
     */
    public static final String GROWTH_VALUE_RULE_CONTINUOUS_LOGIN = "第%s天连续登录额外获赠%s%s";

    /**
     * 每日首次签到获得
     */
    public static final String GROWTH_VALUE_RULE_SIGN_IN = "每日首次签到获得%s%s";

    /**
     * 第n天连续签到额外获赠n积分
     */
    public static final String GROWTH_VALUE_RULE_CONTINUOUS_SIGN_IN = "第%s天连续签到额外获赠%s%s";


    public final static String GROWTH_VALUE_RULE_PERFECT_PERSONAL = "完善个人信息赠送%s%s（限1次）";

    public final static String GROWTH_VALUE_RULE_SINGLE_CONSUMPTION = "每消费%s元，赠送%s%s（消费实付金额计算，金额不足不计算）";

    public final static String GROWTH_VALUE_RULE_SINGLE_RECHARGE = "每充值%s元，赠送%s%s（充值实付金额计算，金额不足不计算）";

    public final static String GROWTH_VALUE_RULE_SINGLE_CONSUMPTION_EXTRA = "一次性消费%s元，额外赠送%s%s";

    public final static String GROWTH_VALUE_RULE_SINGLE_RECHARGE_EXTRA = "一次性充值%s元，额外赠送%s%s";


    public final static String GROWTH_VALUE_RULE_SINGLE_CONSUMPTION_UNLIMITED = "不限制";

    public final static String GROWTH_VALUE_RULE_SINGLE_CONSUMPTION_FIXED_LIMITED = "每人可完成%s次";

    public final static String GROWTH_VALUE_RULE_SINGLE_CONSUMPTION_PERIOD_LIMITED = "每人每%s限定次数%s次";


    public final static String GOODS_BUY_UNLIMITED = "不限";

    public final static String GROWTH_VALUE_RULE_SPECIFIED_GOODS_BUY_COUNT = "购买次数：每购买%s次，赠送%s%s，最多完成%s次";

    public final static String GROWTH_VALUE_RULE_SPECIFIED_GOODS_BUY_NUMBER = "购买数量：每购买%s件，赠送%s%s，最多完成%s次";

    public final static String GROWTH_VALUE_RULE_SPECIFIED_GOODS_BUY_PERIOD = "购买周期：连续购买%s%s赠送%s%s，最多完成%s次";


    public final static String GROWTH_VALUE_RULE_TOTAL_CONSUMPTION_AMOUNT = "累计消费%s元，赠送%s%s";

    public final static String GROWTH_VALUE_RULE_TOTAL_CONSUMPTION_COUNT = "累计消费%s笔，赠送%s%s";

    public final static String GROWTH_VALUE_RULE_TOTAL_RECHARGE_AMOUNT = "累计充值%s元，赠送%s%s";

    public final static String TOTAL_CONSUMPTION_PERIOD_ALWAYS = "始终累计";

    public final static String TOTAL_CONSUMPTION_PERIOD_YEAR = "每年%s 00:00重新累计";

    public final static String TOTAL_CONSUMPTION_PERIOD_MONTH = "每月%s日00:00重新累计";

    public final static String TOTAL_CONSUMPTION_PERIOD_WEEK = "每周%s00:00重新累计";

    public final static String TOTAL_CONSUMPTION_PERIOD_DAY = "每天%s重新累计";

    public final static String GROWTH_VALUE_LIMIT_TOTAL_CONSUMPTION_COUNT = "单笔消费金额不足%s元，不计入消费笔数奖励规则";

    public final static String GROWTH_VALUE_LIMIT_SINGLE_SPECIFIED_STORE = "指定%s家门店";

    public static final String GROWTH_VALUE_LIMIT_DESIGNATED_GOODS = "指定%S个商品";

    public final static String GROWTH_VALUE_LIMIT_SINGLE_ALL_STORE = "所有门店";


}
