package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.certificate.IslandUserDTO;
import com.holderzone.member.common.dto.certificate.SendCertifiedStampsDTO;
import com.holderzone.member.common.dto.zhuancan.AppDTO;
import com.holderzone.member.common.vo.activity.IslandCouponDTO;
import com.holderzone.member.common.vo.base.PlatFormUserDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 新门店管理（食堂）
 *
 * <AUTHOR>
 * @date 2022/2/11
 **/
@Component
@FeignClient(name = FilterConstant.ZHUAN_CAN, fallbackFactory = ZhuanCanFeign.ServiceFallBack.class, url = "${feign.zhuancan}")
public interface ZhuanCanFeign {
    /**
     * 校验赚餐会员性别和生日
     * 如果有一个为空则返回false
     *
     * @param userId 用户id
     * @return 是否存在
     */
    @ApiOperation(value = "校验赚餐会员性别和生日")
    @GetMapping("/island/api/check_island_user_info")
    Boolean checkIslandUserInfo(@RequestParam("userId") Long userId);

    @GetMapping("/platform/api/get-platform-thirdNo")
    @ApiOperation("通过guid获取平台")
    ResponseEntity<PlatFormUserDTO> getPlatFormThirdNo(@RequestParam("enterpriseGuid") String enterpriseGuid,
                                                       @RequestParam("operSubjectGuid") String operSubjectGuid);

    @ApiOperation("获取营销中心-优惠券列表信息 带分页")
    @GetMapping("/island/api/get-island-coupons-list")
    ResponseEntity<List<IslandCouponDTO>> getIslandCouponsList(
            @RequestParam("size") Long size,
            @ApiParam(value = "平台id", required = true) @RequestParam("platFormId") Long platFormId,
            @ApiParam("券类型") @RequestParam("useType") String useType,
            @ApiParam("券名称/券id") @RequestParam("keyword") String keyword,
            @ApiParam("是否过期 true:过期, false:未过期") @RequestParam("expired") Boolean expired);

    /**
     * 批量获取优惠券
     *
     * @param id
     * @return
     */
    @ApiOperation("根据优惠券id获取优惠券信息(new)")
    @GetMapping("/island/api/get-island-coupon-details-new")
    ResponseEntity<List<IslandCouponDTO>> getIslandCouponDetailsNew(@RequestParam("id") List<Long> id);

    /**
     * 认证活动发券
     *
     * @param sendCertifiedStampsDTO
     */
    @PostMapping("/island/api/certified-activity-coupon")
    @ApiOperation("认证活动发券")
    void certifiedActivityCouponSend(@RequestBody SendCertifiedStampsDTO sendCertifiedStampsDTO);

    /**
     * 根据平台id和用户手机号码查询
     *
     * @param platformId
     * @param phone
     * @return
     */
    @ApiOperation("根据平台id和用户手机号码查询")
    @GetMapping("/island/api/island-user-phone/{platformId}/{phone}")
    ResponseEntity<List<IslandUserDTO>> getIslandUserByPlatformIdAndLikephone(@PathVariable("platformId") Long platformId, @PathVariable("phone") String phone);

    @ApiOperation("通过运营主体查询关联小程序")
    @GetMapping("/platform/api/plat-form-user/query_app")
    AppDTO getPlatFormUserApp(@RequestParam("operSubjectGuid") String operSubjectGuid);

    /**
     * 根据平台查询小程序token
     * @param platformId 平台id
     * @return token
     */
    @GetMapping("/sys/api/token")
    String getTokenByPlatform(@RequestParam("platformId") Long platformId);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ZhuanCanFeign> {

        @Override
        public ZhuanCanFeign create(Throwable throwable) {
            return new ZhuanCanFeign() {
                @Override
                public Boolean checkIslandUserInfo(Long userId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "checkIslandUserInfo", userId, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ResponseEntity<PlatFormUserDTO> getPlatFormThirdNo(String enterpriseGuid, String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getPlatFormThirdNo", operSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ResponseEntity<List<IslandCouponDTO>> getIslandCouponsList(Long size, Long platFormId, String useType, String keyword, Boolean expired) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getIslandCouponsList", platFormId, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ResponseEntity<List<IslandCouponDTO>> getIslandCouponDetailsNew(List<Long> id) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getIslandCouponDetailsNew", id, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void certifiedActivityCouponSend(SendCertifiedStampsDTO sendCertifiedStampsDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "certifiedActivityCouponSend", sendCertifiedStampsDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ResponseEntity<List<IslandUserDTO>> getIslandUserByPlatformIdAndLikephone(Long platformId, String phone) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getIslandUserByPlatformIdAndLikephone", phone, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public AppDTO getPlatFormUserApp(String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getPlatFormUserOne", operSubjectGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String getTokenByPlatform(Long platformId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getTokenByPlatform", platformId,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
