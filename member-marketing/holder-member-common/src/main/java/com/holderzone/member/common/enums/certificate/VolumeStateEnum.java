package com.holderzone.member.common.enums.certificate;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/11/27
 * @description 优惠券状态枚举
 */
public enum VolumeStateEnum {

    /**
     * 未过期(发送中)
     */
    NOT_EXPIRED(0, "未过期"),

    /**
     * 已过期(保留状态)
     */
    EXPIRED(1, "已过期"),

    STOP_DISPENSING(2, "停止发放"),

    VOID(3, "作废"),
    ;

    private final int code;

    private final String des;

    VolumeStateEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return this.code;
    }

    public String getDes() {
        return this.des;
    }

    public int getCodeByDes(String des) {
        if (StringUtils.isEmpty(des)) {
            return -1;
        }
        VolumeStateEnum[] values = VolumeStateEnum.values();
        for (VolumeStateEnum value : values) {
            if (value.des.equals(des)) {
                return value.getCode();
            }
        }
        return -1;
    }

    public String getDesByCode(int code) {
        VolumeStateEnum[] values = VolumeStateEnum.values();
        for (VolumeStateEnum value : values) {
            if (value.getCode() == code) {
                return value.getDes();
            }
        }
        return "";
    }
}
