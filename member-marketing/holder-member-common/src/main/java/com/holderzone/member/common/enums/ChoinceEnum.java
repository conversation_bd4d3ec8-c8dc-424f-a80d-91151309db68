package com.holderzone.member.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用选择
 *
 * <AUTHOR>
 * @create 2023-11-27
 */
@AllArgsConstructor
@Getter
public enum ChoinceEnum {

    /**
     * 全部
     */
    ALL(0, "全部"),
    /**
     * 部分
     */
    PART(1, "部分"),
    /**
     * 无：无数据
     */
    NONE(2, "无");

    /**
     * 编码
     */
    private final int code;

    /**
     * 信息
     */
    private final String des;

    /**
     * 根据code获取name
     *
     * @param code code
     * @return 操作结果
     */

    public static String getDesByCode(int code) {
        for (ChoinceEnum anEnum : ChoinceEnum.values()) {
            if (anEnum.getCode() == code) {
                return anEnum.getDes();
            }
        }
        return null;
    }
}
