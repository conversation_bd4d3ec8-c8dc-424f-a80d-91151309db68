package com.holderzone.member.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @className SwaggerConfig
 * @date 2018/07/10 下午7:39
 * @description Swagger相关
 * @program holder-member-merchant
 */
@EnableSwagger2
@SuppressWarnings("Duplicates")
@Configuration
public class SwaggerConfig{

    @Bean
    public Docket testApi() {
        List<Parameter> params = new ArrayList<>();
        params.add(new ParameterBuilder().name("Content-Type").description("内容类型")
                .modelRef(new ModelRef("string"))
                .defaultValue("application/json;charset=utf-8")
                .parameterType("header").required(true).build());

        params.add(new ParameterBuilder().name("userInfo").description("用户信息")
                .modelRef(new ModelRef("string"))
                .defaultValue("%7B%0A%09%22userGuid%22%3A%20%226480756476603191298%22%2C%0A%09%22" +
                        "enterpriseGuid%22%3A%20%226506431195651982337%22%2C%0A%09%22" +
                        "enterpriseName%22%3A%20%22%E6%89%93%E5%8D%B0%E4%BC%81%E4%B8%9A1218%22%2C%0A%09%22" +
                        "name%22%3A%20%22tcw%22%2C%0A%09%22" +
                        "tel%22%3A%20%2217302856437%22%2C%0A%09%22" +
                        "storeGuid%22%3A%20%226506453252643487745%22%2C%0A%09%22" +
                        "storeName%22%3A%20%22%E9%97%A8%E5%BA%970227_3%22%2C%0A%09%22" +
                        "merchant%22%3A%20%22100001%22%2C%0A%09%22" +
                        "allianceid%22%3A%20%221fb529b8da78459ca64187f94dc3ae3e%22%2C%0A%09%22" +
                        "storeNo%22%3A%20%224478046%22%0A%7D")
                .parameterType("header").required(true).build());
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("Holder")
                .globalOperationParameters(params)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.holderzone.member.marketing.controller"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("运营主体或联盟商户会员服务")
                .description("运营主体或联盟商户会员服务API")
                .version("1.0")
                .build();
    }
}
