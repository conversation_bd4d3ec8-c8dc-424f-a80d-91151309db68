package com.holderzone.member.common.external;

import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.CommonEnum;
import com.holderzone.member.common.enums.PlatformEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.util.repast.RepastUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description 外部服务支撑
 */
@Component
public class ExternalSupport {

    private final ExternalBaseFactory externalBaseFactory;

    private final ExternalStoreFactory externalStoreFactory;

    private final ExternalItemFactory externalItemFactory;

    private final ExternalMemberFactory externalMemberFactory;

    public ExternalSupport(ExternalBaseFactory externalBaseFactory, ExternalStoreFactory externalStoreFactory, ExternalItemFactory externalItemFactory, ExternalMemberFactory externalMemberFactory) {
        this.externalBaseFactory = externalBaseFactory;
        this.externalStoreFactory = externalStoreFactory;
        this.externalItemFactory = externalItemFactory;
        this.externalMemberFactory = externalMemberFactory;
    }

    public ExternalBaseService baseServer(int systemCode) {
        switch (SystemEnum.transferByCode(systemCode)) {
            case SALE:
                return externalBaseFactory.build(PlatformEnum.HOLDER_CANTEEN);
            case RETAIL:
            case MALL:
                return externalBaseFactory.build(PlatformEnum.PASS_RETAIL);
            case REPAST:
                return externalBaseFactory.build(PlatformEnum.SAAS);
            default:
                throw new MemberBaseException(CommonEnum.SYSTEM_ILLEGAL);
        }
    }

    public ExternalBaseService baseServer(int systemCode, String operSubjectGuid) {
        if (Objects.equals(SystemEnum.REPAST.getCode(), systemCode)
                && RepastUtils.isHasRepastAuth(operSubjectGuid)) {
            systemCode = SystemEnum.RETAIL.getCode();
        }
        switch (SystemEnum.transferByCode(systemCode)) {
            case SALE:
                return externalBaseFactory.build(PlatformEnum.HOLDER_CANTEEN);
            case RETAIL:
            case MALL:
                return externalBaseFactory.build(PlatformEnum.PASS_RETAIL);
            case REPAST:
                return externalBaseFactory.build(PlatformEnum.SAAS);
            default:
                throw new MemberBaseException(CommonEnum.SYSTEM_ILLEGAL);
        }
    }

    public ExternalStoreService storeServer(int systemCode) {
        switch (SystemEnum.transferByCode(systemCode)) {
            case SALE:
                return externalStoreFactory.build(PlatformEnum.HOLDER_CANTEEN);
            case REPAST:
                return externalStoreFactory.build(PlatformEnum.SAAS);
            case RETAIL:
                return externalStoreFactory.build(PlatformEnum.PASS_RETAIL);
            case MALL:
                return externalStoreFactory.build(PlatformEnum.MALL);
            default:
                throw new MemberBaseException(CommonEnum.SYSTEM_ILLEGAL);
        }
    }

    public ExternalItemService itemServer(int systemCode) {
        switch (SystemEnum.transferByCode(systemCode)) {
            case SALE:
                return externalItemFactory.build(PlatformEnum.HOLDER_CANTEEN);
            case REPAST:
                return externalItemFactory.build(PlatformEnum.SAAS);
            case RETAIL:
                return externalItemFactory.build(PlatformEnum.PASS_RETAIL);
            case MALL:
                return externalItemFactory.build(PlatformEnum.MALL);
            default:
                throw new MemberBaseException(CommonEnum.SYSTEM_ILLEGAL);
        }
    }


    public ExternalMemberService memberServer(int systemCode) {
        switch (SystemEnum.transferByCode(systemCode)) {
            case REPAST:
                return externalMemberFactory.build(PlatformEnum.SAAS);
            case RETAIL:
                return externalMemberFactory.build(PlatformEnum.PASS_RETAIL);
            case MALL:
                return externalMemberFactory.build(PlatformEnum.MALL);
            default:
                throw new MemberBaseException(CommonEnum.SYSTEM_ILLEGAL);
        }
    }


    public ExternalMemberService memberServer(String operSubjectGuid) {
        if (RepastUtils.isHasRepastAuth(operSubjectGuid)) {
            return externalMemberFactory.build(PlatformEnum.PASS_RETAIL);
        }
        return externalMemberFactory.build(PlatformEnum.SAAS);
    }
}
