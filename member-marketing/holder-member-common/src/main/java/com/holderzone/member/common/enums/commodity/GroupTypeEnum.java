package com.holderzone.member.common.enums.commodity;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/10 下午6:13
 * @description 组合类型 1：单品 2：固定套餐 3 ：可选套餐
 */
public enum GroupTypeEnum {

    SINGLE(1,"单品"),

    STATIONARY(2,"固定套餐"),

    SELECT(3,"可选套餐"),
    ;

    private final int code;

    private final String des;

    GroupTypeEnum(int code,String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return this.code;
    }

    public String getDes() {
        return this.des;
    }

    public static GroupTypeEnum getByCode(Integer code) {

        if (Objects.isNull(code)) {
            return null;
        }
        for (GroupTypeEnum type : GroupTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}
