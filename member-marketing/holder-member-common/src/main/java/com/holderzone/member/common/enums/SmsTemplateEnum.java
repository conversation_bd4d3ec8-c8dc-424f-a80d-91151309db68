package com.holderzone.member.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

@Getter
@AllArgsConstructor
public enum SmsTemplateEnum {
    /**
     * 开卡成功短信
     */
    OPEN_CARD_SUCCESS_SMS(0, "开卡成功通知"),

    /**
     * 余额调整短信
     */
    AMOUNT_ADJUST_SMS(1, "资金变动提醒"),

    /**
     * 优惠券到账通知
     */
    DISCOUNT_COUPON_ARRIVAL_NOTICE_SMS(2, "优惠券到账提醒"),

    /**
     * 优惠券过期提醒
     */
    DISCOUNT_COUPON_EXPIRE_NOTICE_SMS(3, "优惠券过期提醒")
    ;

    /**
     * 消费短信
     */

    private int code;

    /**
     * 信息
     */
    private String des;

    public String getMessageByType(int code){
        SmsTemplateEnum[] permissionEnums = SmsTemplateEnum.values();
        for (SmsTemplateEnum permissionEnum : permissionEnums) {
            if(permissionEnum.getCode() == code){
                return permissionEnum.getDes();
            }
        }
        return "";
    }

    public int getTypeByMessage(String message){
        if(StringUtils.isEmpty(message)){
            return -1;
        }
        SmsTemplateEnum[] permissionEnums = SmsTemplateEnum.values();
        for (SmsTemplateEnum permissionEnum : permissionEnums) {
            if(permissionEnum.getDes().equals(message)){
                return permissionEnum.getCode();
            }
        }
        return -1;
    }
}
