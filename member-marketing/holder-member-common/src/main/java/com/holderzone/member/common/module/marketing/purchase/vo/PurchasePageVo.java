package com.holderzone.member.common.module.marketing.purchase.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseRuleTypeEnum;
import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseStateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 限量抢购活动
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Data
@Accessors(chain = true)
public class PurchasePageVo implements Serializable {


    private static final long serialVersionUID = -8164589024984794259L;
    /**
     * 活动ID
     */
    private Long id;

    /**
     * guid
     */
    @ApiModelProperty(value = "guid")
    private String guid;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String name;

    /**
     * 活动状态
     *
     * @see PurchaseStateEnum
     */
    @ApiModelProperty(value = "活动状态")
    private Integer state;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityStartTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityEndTime;
    
    /**
     * 限购规则
     *
     * @see PurchaseRuleTypeEnum
     */
    @ApiModelProperty(value = "限购规则:0每人限购 1每单限购 2周期限购")
    private Integer purchaseRuleType;

    /**
     * 限购场景
     */
    @ApiModelProperty(value = "限购场景")
    private String applyBusinessJson;

    /**
     * 活动商品数量
     */
    private int applyCommodityNum;

    /**
     * 活动订单数量
     */
    private int applyOrderNum;


}
