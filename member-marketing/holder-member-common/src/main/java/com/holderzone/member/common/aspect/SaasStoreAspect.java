package com.holderzone.member.common.aspect;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * SaasStore service aspect for handling pre/post processing
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class SaasStoreAspect {

    private final MemberMallToolFeign memberMallToolFeign;

    @Pointcut("execution(* com.holderzone.member.common.external.impl.SaasStoreServiceImpl.*(..)) && " +
            "!execution(* com.holderzone.member.common.external.impl.SaasStoreServiceImpl.getAppId(..)) || " +
            "execution(* com.holderzone.member.common.external.impl.SaasItemServiceImpl.*(..))  || " +
            "execution(* com.holderzone.member.common.external.impl.SaasBaseServiceImpl.*(..))")
    public void saasStorePointcut() {
    }

    @Around("saasStorePointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        String methodName = point.getSignature().getName();
        Object[] args = point.getArgs();

        // Store original values
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        String originalOperSubjectGuid = headerUserInfo.getOperSubjectGuid();
        String originalEnterpriseGuid = headerUserInfo.getEnterpriseGuid();

        try {
            log.info("SaasStore method [{}] pre-processing, args: {}", methodName, JSON.toJSONString(args));

            // Pre-processing: Update operSubjectGuid and enterpriseGuid
            OperSubjectCloudVO operSubjectCloudVO = memberMallToolFeign.queryByOperSubiectGuid(originalOperSubjectGuid);
            if (Objects.nonNull(operSubjectCloudVO) && StringUtils.isNotEmpty(operSubjectCloudVO.getMultiOperSubiectGuid())) {
                String newOperSubjectGuid = operSubjectCloudVO.getMultiOperSubiectGuid();
                String newEnterpriseGuid = operSubjectCloudVO.getMultiEnterpriseGuid();
                rebuildHeaderUserInfo(newOperSubjectGuid, newEnterpriseGuid);
            }

            // Execute the actual method
            Object result = point.proceed();

            // Post-processing: Restore original values
            rebuildHeaderUserInfo(originalOperSubjectGuid, originalEnterpriseGuid);
            log.info("SaasStore method [{}] post-processing, result: {}", methodName, JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error("SaasStore method [{}] processing error", methodName, e);
            // Ensure we restore original values even if an error occurs
            rebuildHeaderUserInfo(originalOperSubjectGuid, originalEnterpriseGuid);
            log.info("Restored original operSubjectGuid and enterpriseGuid after error");
            throw e;
        }
    }


    private void rebuildHeaderUserInfo(String originalOperSubjectGuid, String originalEnterpriseGuid) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        headerUserInfo.setOperSubjectGuid(originalOperSubjectGuid);
        headerUserInfo.setEnterpriseGuid(originalEnterpriseGuid);
        ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
    }
} 