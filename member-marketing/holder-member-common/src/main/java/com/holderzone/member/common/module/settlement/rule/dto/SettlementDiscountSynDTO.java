package com.holderzone.member.common.module.settlement.rule.dto;

import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SettlementDiscountSynDTO {

    @ApiModelProperty(value = "结算优惠项guid,")
    private String settlementDiscountGuid;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate = LocalDateTime.now();
    /**
     * @see SettlementDiscountTypeEnum
     */
    @ApiModelProperty(value = "类型：0 单品级优惠 1订单级优惠 2资产优惠")
    private Integer discountType;

    @ApiModelProperty(value = "数量")
    private Integer discountNum;

    /**
     * 优惠guid，必须
     * SettlementDiscountOptionEnum + discountGuid 唯一
     */
    @ApiModelProperty(value = "优惠guid")
    private String discountGuid;

    @ApiModelProperty(value = "优惠力度")
    private String discountDynamic;

    @ApiModelProperty(value = "优惠名称")
    private String discountName;

    /**
     * 活动规则
     * 共享互斥关系 0-互斥 1-共享
     */
    @ApiModelProperty(value = "活动规则 共享互斥关系 0-互斥 1-共享")
    private Integer relationRule;

    @ApiModelProperty(value = "父级guid,默认为0")
    private String parentGuid;
}
