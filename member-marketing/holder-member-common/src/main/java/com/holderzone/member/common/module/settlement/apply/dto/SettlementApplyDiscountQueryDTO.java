package com.holderzone.member.common.module.settlement.apply.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


/**
 * 结算规则应用订单数据
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementApplyDiscountQueryDTO implements Serializable {


    private static final long serialVersionUID = -2069089418548044794L;
    /**
     * 订单入参
     */
    @ApiModelProperty("订单相关入参")
    @NotNull(message = "订单入参必填！")
    private SettlementApplyOrderInfoDTO orderInfo;

    /**
     * 订单商品
     */
    @ApiModelProperty("购物车商品明细")
    private List<SettlementApplyCommodityDTO> orderCommodity;
}
