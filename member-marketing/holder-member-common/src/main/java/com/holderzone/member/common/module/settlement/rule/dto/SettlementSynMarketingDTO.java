package com.holderzone.member.common.module.settlement.rule.dto;


import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import lombok.Data;


import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 结算规则同步各个活动
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class SettlementSynMarketingDTO implements Serializable {

    /**
     * 优惠项
     */
    private List<SettlementSynDiscountDTO> synDiscounts;

    /**
     * 主体
     */
    private String operSubjectGuid;

    private SettlementDiscountOptionEnum anEnum;


}
