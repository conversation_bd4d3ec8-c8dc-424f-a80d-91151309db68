package com.holderzone.member.common.module.settlement.apply.dto;

import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 结算规则已选优惠项
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Builder
public class SettlementApplyDiscountDTO implements Serializable {

    private static final long serialVersionUID = 1629157993671224102L;
    /**
     * 优惠项
     *
     * @see SettlementDiscountOptionEnum
     */
    private Integer discountOption;

    /**
     * 优惠guid,界面显示为：优惠id
     */
    private String discountGuid;

    /**
     * 数量
     */
    private Integer discountNum;

    /**
     * 是否可叠加
     */
    private Integer isAppend;
}
