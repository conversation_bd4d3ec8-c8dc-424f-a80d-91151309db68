package com.holderzone.member.common.module.settlement.rule.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 结算规则相同场景、门店
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Accessors(chain = true)
public class SettlementRuleSameVO implements Serializable {


    private static final long serialVersionUID = 3084265494067806280L;

    /**
     * 业务：0全部 1部分，为空则未设置
     */
    private Integer applyBusiness;
    /**
     * 业务数组
     */
    private List<String> applyBusinessList;

    /**
     * 门店：0全部 1部分，为空则未设置
     */
    private Integer applicableAllStore;
    /**
     * 应用门店
     */
    private List<SettlementRuleStoreSameVO> applicableStore;

}
