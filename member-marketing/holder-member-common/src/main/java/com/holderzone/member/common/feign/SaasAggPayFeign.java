package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.common.dto.pay.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SaasAggPayClient
 * @date 2019/03/18 11:42
 * @description
 * @program holder-saas-store-member
 */
@Component
@FeignClient(value = "holder-member-pay", fallbackFactory = SaasAggPayFeign.SaasAggPayClientFallBack.class, url = "${feign.pay}")
public interface SaasAggPayFeign {

    @PostMapping("agg/pay")
    AggPayRespDTO pay(SaasAggPayOdooDTO saasAggPayOdooDTO);

    @PostMapping("agg/polling")
    AggPayPollingRespDTO polling(SaasPollingOdooDTO saasPollingOdooDTO);

    @PostMapping("agg/query")
    @ApiOperation(value = "聚合支付查询支付结果接口")
    AggPayPollingRespDTO query(@RequestBody SaasPollingOdooDTO saasPollingDTO);

    @PostMapping("agg/refund")
    AggRefundRespDTO refund(SaasAggRefundOdooDTO saasAggRefundOdooDTO);

    @Component
    class SaasAggPayClientFallBack implements FallbackFactory<SaasAggPayFeign> {
        @Override
        public SaasAggPayFeign create(Throwable throwable) {
            return new SaasAggPayFeign() {
                @Override
                public AggPayRespDTO pay(SaasAggPayOdooDTO saasAggPayOdooDTO) {
                    throw new BusinessException("调用交易中心异常");
                }

                @Override
                public AggPayPollingRespDTO polling(SaasPollingOdooDTO saasPollingOdooDTO) {
                    throw new BusinessException("轮询异常");
                }

                @Override
                public AggPayPollingRespDTO query(SaasPollingOdooDTO saasPollingDTO) {
                    throw new BusinessException("查询异常");
                }

                @Override
                public AggRefundRespDTO refund(SaasAggRefundOdooDTO saasAggRefundOdooDTO) {
                    throw new BusinessException("调用退款异常");
                }

            };
        }
    }

}
