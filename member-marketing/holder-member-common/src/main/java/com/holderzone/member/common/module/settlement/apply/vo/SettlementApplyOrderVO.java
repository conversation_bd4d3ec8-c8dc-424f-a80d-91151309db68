package com.holderzone.member.common.module.settlement.apply.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.enums.SettlementDiscountShowTypeEnum;
import com.holderzone.member.common.module.settlement.apply.vo.detail.SettlementApplyDiscountDetailOfCouponVO;
import com.holderzone.member.common.module.settlement.apply.vo.show.SettlementApplyDiscountShowVO;
import com.holderzone.member.common.module.settlement.apply.vo.show.SettlementApplyOrderShowVO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import io.swagger.annotations.ApiModelProperty;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 优惠应用结果
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementApplyOrderVO implements Serializable {


    private static final long serialVersionUID = 3228557012267950972L;

    /**
     * 结算台规则
     */
    @ApiModelProperty("结算台规则")
    private SettlementApplyRuleVO applyRule;

    /**
     * 订单详情
     */
    @ApiModelProperty("订单详情")
    private SettlementApplyOderInfoVO oderInfo;

    /**
     * 商品明细
     */
    @ApiModelProperty("商品明细")
    private List<SettlementApplyCommodityVO> commodityList;

    /**
     * 共享优惠商品明细
     */
    private List<SettlementApplyShareCommodityVO> shareCommodityVOList;

    /**
     * 可用优惠
     */
    @ApiModelProperty("优惠列表")
    private List<SettlementApplyDiscountVO> discountList;

    /**
     * 提示信息
     */
    @ApiModelProperty("提示信息")
    private String tips;

    /**
     * 是否需要自动勾选
     */
    @ApiModelProperty("是否需要自动勾选")
    private Integer isCheck = BooleanEnum.FALSE.getCode();

    /**
     * 构造一个可用空对象
     *
     * @return 空对象
     */
    public static SettlementApplyOrderVO buildEmpty() {
        SettlementApplyOderInfoVO oderInfo = new SettlementApplyOderInfoVO()
                .setDiscountAmount(BigDecimal.ZERO);
        return new SettlementApplyOrderVO()
                .setOderInfo(oderInfo)
                .setCommodityList(new ArrayList<>())
                .setDiscountList(new ArrayList<>());

    }

    /**
     * 构造一个可用空对象
     *
     * @return 空对象
     */
    public static SettlementApplyOrderVO buildEmpty(SettlementApplyRuleVO applyRule) {
        SettlementApplyOderInfoVO oderInfo = new SettlementApplyOderInfoVO()
                .setDiscountAmount(BigDecimal.ZERO);
        return new SettlementApplyOrderVO()
                .setOderInfo(oderInfo)
                .setApplyRule(applyRule)
                .setCommodityList(new ArrayList<>())
                .setDiscountList(new ArrayList<>());

    }

    /**
     * 优惠列表为空，界面显示：暂无优惠
     *
     * @param discountOption 优惠项
     * @return 结算结果
     */
    public static SettlementApplyOrderVO buildEmptyDiscount(int discountOption) {
        final SettlementApplyOrderVO orderVO = buildEmpty();
        orderVO.setDiscountList(Collections.singletonList(SettlementApplyDiscountVO.build(discountOption)));
        return orderVO;

    }

    /**
     * 构造异常提示
     *
     * @param tips 提示
     * @return 校验提示
     */
    public static SettlementApplyOrderVO buildTips(String tips) {
        return new SettlementApplyOrderVO().setTips(tips);
    }

    /**
     * 构造基础返回
     *
     * @param oderInfo     订单
     * @param discountList 折扣
     * @return 结算结果
     */
    public static SettlementApplyOrderVO build(SettlementApplyOderInfoVO oderInfo,
                                               List<SettlementApplyDiscountVO> discountList) {
        return new SettlementApplyOrderVO()
                .setOderInfo(oderInfo)
                .setDiscountList(discountList);
    }

    public static SettlementApplyOrderVO build(SettlementApplyOderInfoVO oderInfo,
                                               List<SettlementApplyDiscountVO> discountList,
                                               List<SettlementApplyCommodityVO> settlementApplyCommodityVOS) {
        return new SettlementApplyOrderVO()
                .setOderInfo(oderInfo)
                .setCommodityList(settlementApplyCommodityVOS)
                .setDiscountList(discountList);
    }

    /**
     * 构造优惠对象
     *
     * @param discountList 优惠
     * @return 结算结果
     */
    public static SettlementApplyOrderVO build(List<SettlementApplyDiscountVO> discountList) {
        final SettlementApplyOrderVO orderVO = buildEmpty();
        orderVO.setDiscountList(discountList);
        return orderVO;
    }


    /**
     * 转换为前端返回对象
     *
     * @return 显示结果
     */
    public SettlementApplyOrderShowVO toSettlementApplyOrderShowVO() {
        if (StringUtil.isNotBlank(this.tips)) {
            //直接返回
            return new SettlementApplyOrderShowVO()
                    .setTips(this.tips);
        }
        List<SettlementApplyDiscountShowVO> discountShowList = new ArrayList<>(this.discountList.size());
        if (CollectionUtils.isNotEmpty(this.discountList)) {
            //填充showDiscountList
            fillSettlementApplyDiscountShowVO(discountShowList);
        }
        SettlementApplyOrderShowVO showVO = new SettlementApplyOrderShowVO()
                .setOderInfo(this.oderInfo)
//                .setCommodityList(this.commodityList)
                .setShareCommodityVOList(this.shareCommodityVOList)
                .setDiscountList(discountShowList);
        if (Objects.nonNull(this.applyRule)) {
            showVO.setSettlementApplyRuleDetailVO(new SettlementApplyRuleDetailVO()
                    .setName(this.applyRule.getBaseRule().getName())
                    .setUseType(this.applyRule.getBaseRule().getUseType())
                    .setCouponLimitNum(this.applyRule.getBaseRule().getCouponLimitNum())
                    .setCouponLimit(this.applyRule.getBaseRule().getCouponLimit())
                    .setCouponRollback(this.applyRule.getBaseRule().getCouponRollback()));
        }
        return showVO;
    }

    /**
     * 填充前端展示对象
     *
     * @param discountShowList 优惠列表
     */
    private void fillSettlementApplyDiscountShowVO(List<SettlementApplyDiscountShowVO> discountShowList) {
        final Map<Integer, SettlementDiscountShowTypeEnum> optionShowMap = SettlementDiscountShowTypeEnum.optionShowMap();
        Map<Integer, SettlementApplyDiscountShowVO> showVoMap = new LinkedHashMap<>();
        //商品级、订单级、资产级
        this.discountList.sort(Comparator.comparing(d ->
                SettlementDiscountOptionEnum.getEnum(d.getDiscountOption()).getType().getCode()
        ));
        //转为前端展示的对象
        for (SettlementApplyDiscountVO discountVO : this.discountList) {
            final SettlementDiscountShowTypeEnum showTypeEnum = optionShowMap.get(discountVO.getDiscountOption());
            SettlementApplyDiscountShowVO discountShowVO = showVoMap.get(showTypeEnum.getCode());
            if (discountShowVO == null) {
                discountShowVO = SettlementApplyDiscountShowVO.build(showTypeEnum);
                discountShowList.add(discountShowVO);
                showVoMap.put(showTypeEnum.getCode(), discountShowVO);
            }
            if (discountVO.getDiscountAmount() != null) {
                //优惠里默认是多选，单选需要将优惠清除
                final BigDecimal sum = discountShowVO.getDiscountAmount().add(discountVO.getDiscountAmount());
                //累加优惠金额
                discountShowVO.setDiscountAmount(sum);
            }
            //累加数据
            final List<SettlementApplyDiscountDetailVO> detailVOList = discountVO.getDiscountList();

            //积分排序
            if (showTypeEnum == SettlementDiscountShowTypeEnum.INTEGRAL) {
                detailVOList.sort(Comparator.comparing(SettlementApplyDiscountDetailVO::getSort));
            }

            discountShowVO.getDiscountList().addAll(detailVOList);
        }
        //优惠券无数据
        final SettlementApplyDiscountShowVO couponShowVo = showVoMap.get(SettlementDiscountShowTypeEnum.COUPON.getCode());
        if (couponShowVo == null) {
            //空：优惠券占位
            final SettlementApplyDiscountShowVO emptyCouponVo = SettlementApplyDiscountShowVO.build(SettlementDiscountShowTypeEnum.COUPON);
            //优惠券展示下标
            final int index = getCouponShowIndex(discountShowList);
            discountShowList.add(index, emptyCouponVo);
        }
        //除商品券以外，可以按金额倒序查询
        for (SettlementApplyDiscountShowVO discountShowVO : discountShowList) {
            final List<SettlementApplyDiscountDetailVO> detailVOList = discountShowVO.getDiscountList();
            if (detailVOList.size() > 1) {
                sortShowDiscountList(discountShowVO, detailVOList);
            }
        }
    }

    /**
     * 获取优惠券界面展示下标
     *
     * @param discountShowList 界面显示优惠列表
     * @return 下标
     */
    private static int getCouponShowIndex(List<SettlementApplyDiscountShowVO> discountShowList) {
        int i = 0;
        //返回数量的最大
        int size = discountShowList.size();
        for (SettlementDiscountShowTypeEnum value : SettlementDiscountShowTypeEnum.values()) {
            //最后 or 当前是优惠券
            if (i == size || value == SettlementDiscountShowTypeEnum.COUPON) {
                break;
            }
            i++;
        }
        return i;
    }

    /**
     * 界面优惠列表排序
     *
     * @param discountShowVO 界面优惠列表
     * @param detailVOList   优惠详情
     */
    private void sortShowDiscountList(SettlementApplyDiscountShowVO discountShowVO, List<SettlementApplyDiscountDetailVO> detailVOList) {
        //优惠券
        if (discountShowVO.getShowType().equals(SettlementDiscountShowTypeEnum.COUPON.getCode())) {
            final Map<Integer, List<SettlementApplyDiscountDetailVO>> couponMap = detailVOList.stream()
                    .collect(Collectors.groupingBy(SettlementApplyDiscountDetailVO::getIsEnabled));
            //启用的按金额排序
            List<SettlementApplyDiscountDetailVO> enableCouponList = couponMap.getOrDefault(BooleanEnum.TRUE.getCode(), new ArrayList<>());
            enableCouponList.sort(Comparator.comparing(c -> ((SettlementApplyDiscountDetailOfCouponVO) c).getDiscountAmount()).reversed());
            //未启用放最后
            Optional.ofNullable(couponMap.get(BooleanEnum.FALSE.getCode()))
                    .ifPresent(disEnableList -> {
                        //结束时间倒序
                        disEnableList.sort(Comparator.comparing(c -> ((SettlementApplyDiscountDetailOfCouponVO) c).getCouponEffectiveEndTime()).reversed());
                        enableCouponList.addAll(disEnableList);
                    });
            //替换排序后
            detailVOList.clear();
            detailVOList.addAll(enableCouponList);
            return;
        }

        if (discountShowVO.getShowType() == SettlementDiscountShowTypeEnum.INTEGRAL.getCode()){
            return;
        }

        //其他优惠
        final Map<Integer, List<SettlementApplyDiscountDetailVO>> detailMap = detailVOList.stream()
                .collect(Collectors.groupingBy(SettlementApplyDiscountDetailVO::getIsEnabled));
        //启用的 按金额排序倒序
        List<SettlementApplyDiscountDetailVO> enableCouponList = detailMap.getOrDefault(BooleanEnum.TRUE.getCode(), new ArrayList<>());
        enableCouponList.sort(Comparator.comparing(c -> ((SettlementApplyDiscountDetailVO) c).getDiscountAmount()).reversed());
        //未启用放最后
        Optional.ofNullable(detailMap.get(BooleanEnum.FALSE.getCode()))
                .ifPresent(enableCouponList::addAll);
        //替换排序后
        detailVOList.clear();
        detailVOList.addAll(enableCouponList);
    }
}
