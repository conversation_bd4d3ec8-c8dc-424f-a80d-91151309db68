package com.holderzone.member.common.constant;

/**
 * <AUTHOR>
 * @description excel导入常量
 * @date 2021/9/1
 */
public final class ExcelImportConstant {

    public final static String IMPORT_SEPARATOR = "\r\n";

    public final static String IMPORT_ERROR_PHONE_NULL = "【手机号】未填写";

    public final static String IMPORT_ERROR_PHONE_ILLEGAL = "【手机号】格式错误，请检查手机号是否符合所在地区格式要求";

    public final static String IMPORT_ERROR_PHONE_REPETITION = "【手机号】重复导入";

    public final static String IMPORT_ERROR_PHONE_UNREGISTERED = "【手机号】未注册";

    public static final String IMPORT_ERROR_PHONE_DISABLE = "【手机号】已禁用";

    public final static String MEMBER_CARD_INEXISTENCE = "【会员卡】不存在";

    public final static String DOES_NOT_EXIST = "不存在";

    public final static String MEMBER_CARD = "【会员卡】";

    public final static String DO_NOT_OPEN = "不可开通";

    public final static String DISABLED = "已禁用";

    public final static String MEMBER_CARD_DISABLED = "【会员卡】已禁用";

    public final static String TEMPLATE_CHOOSE_MEMBER = "快速导入选择会员模板";

    public final static String TEMPLATE_CHOOSE_MEMBER_SUFFIX = "-失败数据.";

    public final static String BATCH_OPENING_MEMBERSHIP_CARD = "批量开通会员卡";

    public final static String HEADER_MEMBER_NAME = "姓名";

    public final static String HEADER_MEMBER_PHONE = "手机号";

    public static final Integer MEMBER_COMPANY_ADDRESS_LENGTH = 30;

    public static final Integer MEMBER_JOB_TITLE_LENGTH = 10;

    public static final String IMPORT_ERROR_MEMBER_GRADE_EFFECTIVE = "【等级有效期】填写错误";

    public static final String IMPORT_ERROR_MEMBER_PROVINCE = "【省】填写错误";

    public static final String IMPORT_ERROR_MEMBER_CITY = "【市】填写错误";

    public static final String IMPORT_ERROR_MEMBER_AREA = "【区】填写错误";

    public static final String IMPORT_ERROR_MEMBER_ID_CARD = "【证件号码】填写错误";

    public static final String IMPORT_ERROR_MEMBER_ID_CARD_TYPE = "【证件类型】填写错误";

    public static final String IMPORT_ERROR_MEMBER_ID_CARD_EXIST = "【证件号码】证件号码已存在";

}
