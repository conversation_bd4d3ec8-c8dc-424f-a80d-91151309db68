package com.holderzone.member.common.module.settlement.rule.qo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 结算规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Accessors(chain = true)
public class DeleteSettlementRuleDiscountQO implements Serializable {


    private static final long serialVersionUID = -9190266794032720049L;

    private String settlementRuleGuid;

    private Integer isAppend;

    private List<String> newNotAdditivitySettlementDiscount;

}
