package com.holderzone.member.common.enums.exception;

import com.holderzone.member.common.enums.ResponseBase;

/**
 * <AUTHOR>
 * @description 哨兵异常枚举
 * @date 2021/8/10
 */
public enum SentinelExceptionEnum implements ResponseBase {
    //哨兵规则
    FLOW(-1, "流控规则被触发"),

    DEGRADE(-2, "降级规则被触发"),

    AUTHORITY(-3, "授权规则被触发"),

    PARAM_FLOW(-4, "热点规则被触发"),

    SYSTEM_BLOCK(-5, "系统规则被触发"),
    ;
    /** 错误码 */
    private final int code;

    /** 错误描述 */
    private final String des;

    SentinelExceptionEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDes() {
        return des;
    }
}
