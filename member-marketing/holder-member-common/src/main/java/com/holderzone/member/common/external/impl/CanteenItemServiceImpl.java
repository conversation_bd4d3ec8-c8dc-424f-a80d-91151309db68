package com.holderzone.member.common.external.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.util.Page;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.dto.crm.CrmCommodityReqDTO;
import com.holderzone.member.common.dto.excel.ItemUploadVO;
import com.holderzone.member.common.dto.grade.CommodityInfoDTO;
import com.holderzone.member.common.dto.grade.StrategyInfoDTO;
import com.holderzone.member.common.external.ExternalItemService;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.qo.base.CommodityBasePageQO;
import com.holderzone.member.common.qo.grade.CommodityInfoQO;
import com.holderzone.member.common.qo.grade.GradeCommodityBasePageQO;
import com.holderzone.member.common.qo.mall.QueryProductCategoryCrmQO;
import com.holderzone.member.common.qo.tool.CommodityDetailsQO;
import com.holderzone.member.common.util.verify.ObjectUtil;
import com.holderzone.member.common.vo.base.CommodityBaseTypeVO;
import com.holderzone.member.common.vo.base.CommodityBaseVO;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.feign.CrmReturnPairVo;
import com.holderzone.member.common.vo.grade.StrategyInfoVO;
import com.holderzone.member.common.vo.mall.ProductCrmCategoryVO;
import com.holderzone.member.common.vo.tool.CommodityDetailsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @create 2024-03-13
 * @description 食堂商品服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CanteenItemServiceImpl implements ExternalItemService {

    private final CrmFeign crmFeign;

    @Override
    public List<ResArrayStrategyBase> getOperatingStrategy(QueryArrayShopBase queryStoreBasePage) {
        return crmFeign.getOperatingStrategy(queryStoreBasePage).getResult();
    }

    @Override
    public List<ResCategoryBase> queryCategoryByStrategy(QueryArrayShopBase queryStoreBasePage) {
        return crmFeign.getCategoryStrategy(queryStoreBasePage).getResult();
    }

    @Override
    public Pair<Integer, List<ResCommodityBase>> pageStrategyCommodity(QueryCommodityBasePage commodityBasePage) {
        CrmReturnPairVo.PairModel<Integer, List<ResCommodityBase>> result = crmFeign.pageStrategyCommodity(commodityBasePage).getResult();
        return Pair.of(ObjectUtil.objToInt(getValue(result.getData(), StringConstant.LENGTH)), result.getDataList());
    }

    @Override
    public List<ResCommodityBase> listCommodityBase(QueryArrayShopBase queryStoreBasePage) {
        return crmFeign.getCategoryCommodity(queryStoreBasePage).getResult();
    }

    @Override
    public List<ResCommodityBase> listCommodityByDetail(QueryArrayShopBase queryStoreBasePage) {
        return crmFeign.queryCommodityList(queryStoreBasePage).getResult();
    }

    @Override
    public Pair<Integer, List<ResGradeCommodityBase>> listStoreCommodityPage(GradeCommodityBasePageQO pageQO) {
        CrmFeignModel<ResGradeCommodityBase> pageList = crmFeign.listStoreCommodityPage(pageQO);
        return Pair.of(ObjectUtil.objToInt(getValue(pageList.getData(), StringConstant.COUNT)), pageList.getDataList());
    }

    @Override
    public Page<CommodityBaseVO> pageCommodityBase(CommodityBasePageQO pageQO) {
        return null;
    }

    @Override
    public List<CommodityBaseTypeVO> getStoreGoodsComboType() {
        log.info("[获取商品类型基础数据]");
        return null;
    }

    private String getValue(Object obj, String key) {
        if (obj == null) {
            return null;
        }
        String replaced = obj.toString().replace("=", ":");
        JSONObject jsonObject = JSON.parseObject(replaced);
        if (jsonObject == null) {
            return null;
        }
        return jsonObject.getString(key);
    }

    @Override
    public List<CommodityInfoDTO> listCommodity(CommodityInfoQO commodityInfoQO) {
        return crmFeign.getCommodity(commodityInfoQO).getResult().getDataList();
    }

    @Override
    public CrmFeignModel<CommodityInfoDTO> listCommodityHasCount(CommodityInfoQO commodityInfoQO) {
        return crmFeign.getCommodity(commodityInfoQO).getResult();
    }

    @Override
    public List<StrategyInfoVO> listStrategyInfo(StrategyInfoDTO strategyInfoDTO) {
        return crmFeign.getStrategy(strategyInfoDTO).getResult().getDataList();
    }

    @Override
    public String getCommodityUrl(CrmCommodityReqDTO reqDTO) {
        return crmFeign.getCommodityUrl(reqDTO).getData().getCommodity_management_new();
    }

    @Override
    public List<CommodityDetailsVO> getCommodityDetails(CommodityDetailsQO detailsQO) {
        return crmFeign.getCommodityDetails(detailsQO).getDataList2();
    }

    @Override
    public List<CommodityDetailsVO> getNewCommodityDetails(CommodityDetailsQO detailsQO) {
        return crmFeign.getNewCommodityDetails(detailsQO).getData();
    }


    /**
     * @description: 获取策略单分类接口
     * @author: li ao
     * @date: 2024/3/18 15:11
     * @param: categoryCrmQO
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.vo.mall.ProductCrmCategoryVO>
     **/
    @Override
    public List<ProductCrmCategoryVO> getCommodityCategory(QueryProductCategoryCrmQO categoryCrmQO) {
        return crmFeign.getCommodityCategory(categoryCrmQO).getDataList();
    }

    @Override
    public ItemUploadVO itemUploadExcelUrl(String fileUrl, Integer activityType) {
        return null;
    }
}
