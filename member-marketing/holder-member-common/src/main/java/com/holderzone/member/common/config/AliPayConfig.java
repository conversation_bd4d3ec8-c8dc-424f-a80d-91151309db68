package com.holderzone.member.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * 支付宝基础配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "alipay")
public class AliPayConfig {

    private String appId;

    private String authToken;

    private String authLink;

    private String applyPublicKey;

    private String payPublicKey;

    private String privateKey;

    private String aliTokenUrl;

    private String redirectUri;

    private String aliQrcode;

    private String aes;

}
