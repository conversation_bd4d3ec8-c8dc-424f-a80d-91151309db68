package com.holderzone.member.common.constant;

/**
 * 队列通道
 */
public class BinderConstant {

    private BinderConstant() {

    }

    public static final String INPUT_SYNC_COMMODITY = "inputSyncCommodity";

    public static final String INPUT_SYNC_STRATEGY = "inputSyncStrategy";

    public static final String INPUT_SYNC_CATEGORY = "inputSyncCategory";

    public static final String INPUT_SYNC_TAG = "inputSyncTag";

    public static final String OUTPUT_PUSH_CRM_ORDER = "outputPushCrmOrder";

    public static final String OUTPUT_PUSH_CRM_ORDER_STOCK = "outputPushCrmOrderStock";

    public static final String OUTPUT_SYNC_COMMODITY = "outputSyncCommodity";

    public static final String OUTPUT_SYNC_STRATEGY = "outputSyncStrategy";

    public static final String OUTPUT_SYNC_CATEGORY = "outputSyncCategory";

    public static final String OUTPUT_SYNC_TAG = "outputSyncTag";

    public static final String INPUT_SEND_MESSAGE = "inputSendMessage";

    public static final String INPUT_SEND_OPEN_ELECTRONIC_CARD = "inputSendOpenElectronicCard";

    public static final String INPUT_SUBSIDY_SEND_RIGHTS = "inputSubsidySendRights";

    public static final String INPUT_SUBSIDY_SEND_BACK_RIGHTS = "inputSubsidySendBackRights";

    public static final String INPUT_CHANGE_GROWTH_VALUE = "inputChangeGrowthValue";

    public static final String INPUT_GROWTH_SEND_BACK_RIGHTS = "inputGrowthSendBackRights";

    public static final String INPUT_RELATION_LABEL = "inputRelationLabel";

    public static final String INPUT_MEMBER_GRADE_CHANGE = "inputMemberGradeChange";

    public static final String INPUT_PUSH_CRM_ORDER = "inputPushCrmOrder";

    public static final String INPUT_MEMBER_COUPON_PACKAGE = "inputMemberCouponPackage";


    public static final String INPUT_PUSH_SETTLEMENT_DISCOUNT = "inputPushSettlementDiscount";

    public static final String OUTPUT_PUSH_SETTLEMENT_DISCOUNT = "outputPushSettlementDiscount";

    public static final String OUTPUT_SEND_AGG_PAY = "outputSendAggPay";

    public static final String INPUT_SEND_AGG_PAY = "inputSendAggPay";

    public static final String INPUT_PUSH_SHORT_MESSAGE_SEND = "inputPushShortMessageSend";

    public static final String OUTPUT_PUSH_SHORT_MESSAGE_SEND = "outputPushShortMessageSend";

    public static final String INPUT_MEMBER_CONSUMPTION_DISTRIBUTE = "inputMemberConsumptionDistribute";

    public static final String OUTPUT_MEMBER_CONSUMPTION_DISTRIBUTE = "outputMemberConsumptionDistribute";


}
