package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.sales.*;
import com.holderzone.member.common.vo.feign.SalesDataPageModel;
import com.holderzone.member.common.vo.feign.SalesFeignModel;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 销售管理 远程调用
 *
 * <AUTHOR>
 */
@Component
@FeignClient(name = FilterConstant.SALES, fallbackFactory = MarketFeign.ServiceFallBack.class, url = "${feign.sales}")
public interface SalesFeign {
    /**
     * 获取渠道
     */
    @ApiOperation("获取渠道")
    @PostMapping("/sale-channel/pos/v1")
    SalesFeignModel<Integer> querySaleChannel();
    /**
     * 查询销售管理商品列表
     */
    @ApiOperation("查询销售管理商品列表")
    @PostMapping("/store-goods/available-for-sale/member/list/page/v1")
    SalesFeignModel<SalesDataPageModel> querySalesGoodsList(@RequestBody RequestSalesGoodsListDTO request);


    /**
     * 根据spu商品列表
     */
    @ApiOperation("根据spu商品列表")
    @PostMapping("/api/store_goods/list_by_spu")
    SalesFeignModel<SalesDataPageModel> querySalesGoodsListBySpu(@RequestBody RequestSalesGoodsListBySpuDTO request);

    /**
     * 查询商品分类
     */
    @ApiOperation("查询商品分类")
    @GetMapping("/api/v1/sale-category/tree")
    SalesFeignModel<List<ResponseSalesGoodsCategoryDTO>> querySalesGoodsCategory(@RequestParam(value = "table_name") String table_name,
                                                                                 @RequestParam(value = "tree") Boolean tree,
                                                                                 @RequestParam(value = "is_category_data") Boolean is_category_data);

    /**
     * 查询商品分类
     */
    @ApiOperation("查询商品类型")
    @PostMapping("/store-goods/combo-type/list/v1")
    SalesFeignModel<List<ResponseSalesGoodsTypeDTO>> querySalesGoodsType();


    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<SalesFeign> {

        @Override
        public SalesFeign create(Throwable throwable) {
            return new SalesFeign() {

                @Override
                public SalesFeignModel<Integer> querySaleChannel() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "querySaleChannel", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public SalesFeignModel<SalesDataPageModel> querySalesGoodsList(RequestSalesGoodsListDTO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "querySalesGoodsList", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public SalesFeignModel<SalesDataPageModel> querySalesGoodsListBySpu(RequestSalesGoodsListBySpuDTO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "querySalesGoodsListBySpu", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public SalesFeignModel<List<ResponseSalesGoodsCategoryDTO>> querySalesGoodsCategory(String table_name, Boolean tree, Boolean is_category_data) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "querySalesGoodsCategory", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public SalesFeignModel<List<ResponseSalesGoodsTypeDTO>> querySalesGoodsType() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "querySalesGoodsType", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}