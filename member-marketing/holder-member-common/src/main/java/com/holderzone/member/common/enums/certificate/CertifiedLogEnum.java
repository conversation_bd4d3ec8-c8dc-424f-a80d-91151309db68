package com.holderzone.member.common.enums.certificate;

import org.springframework.util.StringUtils;

/**
 * @program: member-marketing
 * @description: 认证日志参数
 * @author: zhanglin
 */
public enum CertifiedLogEnum {

    /**
     * 创建成功
     */
    CREATE_MESSAGE(0, "创建成功"),

    ;

    private final int code;

    private final String des;

    CertifiedLogEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode(){
        return this.code;
    }

    public String getDes() {
        return this.des;
    }

    public static int getCodeByDes(String des) {
        if (StringUtils.isEmpty(des)) {
            return -1;
        }
        CertifiedLogEnum[] values = CertifiedLogEnum.values();
        for (CertifiedLogEnum value : values) {
            if (value.des.equals(des)) {
                return value.getCode();
            }
        }
        return -1;
    }

    public static String getDesByCode(int code) {
        CertifiedLogEnum[] values = CertifiedLogEnum.values();
        for (CertifiedLogEnum value : values) {
            if (value.getCode() == code) {
                return value.getDes();
            }
        }
        return "";
    }
}
