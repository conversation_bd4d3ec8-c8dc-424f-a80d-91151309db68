package com.holderzone.member.common.enums;

import org.springframework.util.StringUtils;

/**
 * 业务中提示消息枚举
 * <AUTHOR>
 */
public enum BusinessMessageEnum {

    /**
     * 请求参数提示
     */
    EQUITIES_REQUEST_MESSAGE(0,"请求参数信息为:{}"),
    ;

    /**
     * 消息code
     */
    private final int code;

    /**
     * 消息描述
     */
    private final String des;

    /**
     * 构造
     * @param code 参数值
     * @param des 信息描述
     */
    BusinessMessageEnum(int code, String des){
        this.code = code;
        this.des = des;
    }

    /**
     * 通过code获取描述信息
     * @param code 参数值
     * @return 信息描述
     */
    public String getDesByCode(int code){
        BusinessMessageEnum[] businessMessages = BusinessMessageEnum.values();
        for (BusinessMessageEnum businessMessage : businessMessages) {
            if(businessMessage.getCode() == code){
                return businessMessage.getDes();
            }
        }
        return "";
    }

    /**
     * 通过描述获取code值
     * @param des 信息描述
     * @return 参数值
     */
    public int getCodeByDes(String des){
        if(StringUtils.isEmpty(des)){
            return -1;
        }
        BusinessMessageEnum[] businessMessages = BusinessMessageEnum.values();
        for (BusinessMessageEnum businessMessage : businessMessages) {
            if(businessMessage.getDes().equals(des)){
                return businessMessage.getCode();
            }
        }
        return -1;
    }

    public String getDes(){
        return des;
    }

    public int getCode(){
        return code;
    }
}
