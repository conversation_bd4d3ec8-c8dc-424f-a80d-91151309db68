package com.holderzone.member.common.enums;

/**
 * <AUTHOR>
 * @description 共用枚举
 * @date 2021/8/10
 */
public enum CommonEnum implements ResponseBase{
    // 数据操作定义
    SUCCESS(200, "成功！"),

    FAILED(-1, "失败！"),

    HEADER_ILLEGAL(-2, "请求头不合法！"),

    SYSTEM_ILLEGAL(-3, "系统来源不合法！"),
    ;
    /** 错误码 */
    private final int code;

    /** 错误描述 */
    private final String des;

    CommonEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDes() {
        return des;
    }
}
