package com.holderzone.member.common.constant;

/**
 * <AUTHOR>
 * @description 积分消耗规则
 */
public final class IntegralConsumerConstant {

    public static final String FOR_NOW_RATIO_STR = "每%s%s抵扣%s元";

    public static final String USE_LIMIT_STR = "单笔订单金额不足%s元不能使用%s";

    public static final String USE_UPPER_LIMIT_STR = "单笔订单最多抵扣%s元";

    public static final String USE_UPPER_LIMIT_RATIO_STR = "单笔订单最多抵扣订单金额的%s";

    public static final String PERIOD_UPPER_LIMIT = "限制每%s最多可抵现%s元";

    public static final String LIMIT_SINGLE_ALL_STORE = "所有门店";

    public static final String NOT_LIMIT = "不限制";


}
