package com.holderzone.member.common.module.base.purchase.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 消费订单（外部下单记录）
 *
 * <AUTHOR>
 * @date 2023/12/4
 * @since 1.8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PurchaseOrderDto implements Serializable {

    private static final long serialVersionUID = 8415097630030913633L;

    /**
     * 运营主体guid
     */
    @ApiModelProperty(value = "运营主体guid")
    private String operSubjectGuid;

    /**
     * 订单编号（外部订单编号）
     */
    @ApiModelProperty(value = "订单编号（外部订单编号）")
    @NotNull(message = "订单编号不能为空")
    private String orderNumber;

    @ApiModelProperty(value = "下单手机号")
    @NotNull(message = "下单手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "下单用户")
    private String userName;

    @ApiModelProperty(value = "memberInfoGuid")
    @NotNull(message = "memberInfoGuid不能为空")
    private String memberInfoGuid;

    @ApiModelProperty(value = "门店guid")
    @NotNull(message = "门店guid不能为空")
    private String storeGuid;

    /**
     * business
     */
    @ApiModelProperty(value = "订单业务")
    @NotNull(message = "订单业务不能为空")
    private String orderType;

    @ApiModelProperty(value = "订单时间")
    @NotNull(message = "订单时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderTime;

    /**
     * 订单商品
     */
    @NotNull(message = "订单商品不能为空")
    private List<PurchaseOrderCommodityDto> commodityList;

    /**
     * 校验是否有商品
     */
    public boolean validate() {
        
        return commodityList.isEmpty();
    }
}
