package com.holderzone.member.common.module.settlement.rule.qo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 结算规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Accessors(chain = true)
public class SettlementRuleDiscountQO implements Serializable {


    private static final long serialVersionUID = -9190266794032720049L;

    /**
     * 主体
     */
    @JsonIgnore
    private String operSubjectGuid;

    /**
     * 规则guid
     */
    private String settlementRuleGuid;

    /**
     * 父级优惠项：首次为null
     */
    private String parentGuid;

    /**
     * 是否可叠加：1是 0否
     */
    @ApiModelProperty(value = "是否可叠加：1是 0否")
    private Integer isAppend;

    /**
     * 可选，指定优惠项guid查询，
     */
    private List<String> settlementDiscountGuidList;
}
