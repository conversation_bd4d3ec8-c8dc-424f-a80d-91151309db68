package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.PageModel;
import com.holderzone.member.common.dto.ReturnModel;
import com.holderzone.member.common.dto.holder.RoleAndPostIdDTO;
import com.holderzone.member.common.dto.holder.StoreDTO;
import com.holderzone.member.common.dto.mall.TeamStoreInfoModel;
import com.holderzone.member.common.dto.mall.TeamStoreInfoPageParamModel;
import com.holderzone.member.common.dto.permission.MemberSystemPermissionDTO;
import com.holderzone.member.common.dto.permission.OperationPermissionQO;
import com.holderzone.member.common.dto.permission.RolePermissionMapModel;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.dto.user.OperSubjectInfo;
import com.holderzone.member.common.vo.base.BusinessDataModel;
import com.holderzone.member.common.vo.feign.FeignModel;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * holder请求
 *
 * <AUTHOR>
 * @date 2022/2/11
 **/
@Component
@FeignClient(name = FilterConstant.FEIGN_HOLDER, fallbackFactory = HolderFeign.ServiceFallBack.class, url = "${feign.goalgo}")
public interface HolderFeign {

    /**
     * 发送验证码
     * @param type 0 短信 1 邮箱
     * @param info 数据
     * @return
     */
    @PostMapping(value = "/tools/verification/requestVerification")
    ReturnModel requestVerification(@RequestParam("type") Integer type, @RequestParam("info") String info);

    /**
     * 查询验证码
     * @param code 验证码
     * @param info 数据
     * @return
     */
    @PostMapping(value = "/tools/verification/validateVerification")
    ReturnModel validateVerification(@RequestParam("code") String code, @RequestParam("info") String info);

    /**
     * 查询业务数据
     *
     * @return
     */
    @PostMapping(value = "/team/teamInfo/findBusinessDataAddress")
    FeignModel<String> findBusinessDataAddress(@RequestParam("teamId") String teamId);

    /**
     * 查询用户信息
     *
     * @return
     */
    @PostMapping(value = "/team/operationSubjectManagement/queryUserInformation")
    FeignModel<HeaderUserInfo> queryUserInformation(@RequestParam("teamId") String teamId);

    /**
     * 查询用户信息，不传teamId,使用holder的默认企业
     *
     * @return
     */
    @PostMapping(value = "/team/operationSubjectManagement/queryUserInformation")
    FeignModel<HeaderUserInfo> queryUserInformation();

    /**
     * 查询用户信息
     *
     * @return
     */
    @PostMapping(value = "/team/operationSubjectManagement/queryUserInformation")
    FeignModel<HeaderUserInfo> queryUserInformation(@RequestParam("teamId") String teamId, @RequestHeader("loginToken") String loginToken);


    /**
     * 查询用户下权限范围
     *
     * @return
     */
    @PostMapping(value = "/team/permission/findAuthUserByTypeAndUser")
    FeignModel<List<String>> queryUserPermission(@RequestParam("teamId") Long teamId,
                                                 @RequestParam("account") String account,
                                                 @RequestParam("permissionType") Integer permissionType,
                                                 @RequestParam("typeId") Long typeId,
                                                 @RequestHeader("loginToken") String loginToken);

    @PostMapping(value = "/team/productTeamStore/findStorePageInfo")
    FeignModel<PageModel<TeamStoreInfoModel>> findStorePageInfo(@RequestBody TeamStoreInfoPageParamModel request);

    /**
     * 通过企业查询运营主体
     *
     * @param teamId 企业id
     * @return 运营主体
     */
    @PostMapping(value = "/team/operationSubjectManagement/queryOperatingSubjectByTeam")
    FeignModel<List<OperSubjectInfo>> queryOperatingSubjectByTeam(@RequestHeader("teamId") String teamId);

    @PostMapping(value = "/team/permission/findUserRoleAndPost")
    FeignModel<RoleAndPostIdDTO> findUserRoleAndPost(@RequestParam("teamId") String teamId, @RequestParam("userAccount") String userAccount);

    /**
     * 查询运营主体
     *
     * @return
     */
    @PostMapping(value = "/team/operationSubjectManagement/queryOperatingSubject")
    FeignModel<List<OperSubjectInfo>> queryOperatingSubject(@RequestParam("teamId") String teamId);

    /**
     * 获取系统权限列表
     *
     * @param operationPermissionRequest 请参参数
     * @return 操作结果
     */
    @PostMapping(value = "/team/permission/system/getUserPermission")
    FeignModel<List<MemberSystemPermissionDTO>> getSystemPermissionList(@RequestBody OperationPermissionQO operationPermissionRequest);

    /**
     * 查询holder用户权限列表
     * @param operationPermissionRequest 请求参数
     * @param token 用户token
     * @return 返回参数
     */
    @PostMapping(value = "/team/permission/system/getUserPermission")
    FeignModel<List<MemberSystemPermissionDTO>> getSystemPermissionList(@RequestBody OperationPermissionQO operationPermissionRequest,@RequestHeader("loginToken") String token);

    /**
     * 获取系统权限列表
     *
     * @param operationPermissionRequest 请参参数
     * @return 操作结果
     */
    @PostMapping(value = "/team/permission/findUserRoleMapPermission")
    RolePermissionMapModel findUserRoleMapPermission(@RequestBody OperationPermissionQO operationPermissionRequest);

    @PostMapping(value = "/team/permission/findUserRoleMapPermission")
    RolePermissionMapModel findUserRoleMapPermission(@RequestBody OperationPermissionQO operationPermissionRequest,@RequestHeader("loginToken") String loginToken);

    /**
     * 查询业务数据
     *
     * @param operSubjectGuid 请求头增加主体
     * @return
     */
    @PostMapping(value = "/team/operationSubjectManagement/queryBusinessData")
    FeignModel<BusinessDataModel> queryBusinessData(@RequestHeader("operSubjectGuid") String operSubjectGuid);


    /**
     * 查询业务数据
     *
     * @param operSubjectGuid 请求头增加主体
     * @param loginToken 登录token
     * @return
     */
    @PostMapping(value = "/team/operationSubjectManagement/queryBusinessData")
    FeignModel<BusinessDataModel> queryBusinessData(@RequestHeader("oper_subject_guid") String operSubjectGuid,@RequestHeader("loginToken") String loginToken);

    /**
     * 通过holder 查询对应模块操作权限
     *
     * @param request
     * @param token
     * @return 是否拥有权限
     */
    @PostMapping(value = "/team/permission/getHasPermissionName")
    FeignModel<Boolean> getHasPermissionName(@RequestBody OperationPermissionQO request,
                                             @RequestHeader("token") String token);

    @PostMapping(value = "/team/user/validateUser")
    FeignModel<Boolean> validateUser(@RequestParam("account") String account, @RequestParam("password") String password);


    /**
     * 创建门店
     */
    @PostMapping(value = "/loginService/team/productTeamStore/creatStore")
    FeignModel<String> createStore(@RequestBody StoreDTO storeDTO);

    /**
     * 更新门店
     */
    @PostMapping(value = "/loginService/team/productTeamStore/updateStoreInfo")
    FeignModel<String> updateStoreName(@RequestBody StoreDTO storeDTO);

    /**
     * 禁用启用门店
     */
    @PostMapping(value = "/loginService/team/productTeamStore/updateStoreState", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    FeignModel<String> updateStoreState(@RequestBody StoreDTO storeDTO);


    /**
     * holder通过账号获取临时token
     */
    @GetMapping(value = "/permission/getTemporaryToken")
    FeignModel<String> getTemporaryToken(@RequestParam("account") String account);

    @PostMapping(value = "/team/operationSubjectManagement/queryAllSubjectId")
    FeignModel<List<String>> listAllOperationSubjectId();

    @PostMapping(value = "/team/permission/findUserRoleIds")
    FeignModel<List<String>> listUserRoleIds(@RequestHeader("loginToken") String loginToken,@RequestParam("teamId") String teamId
            ,@RequestParam("userId") String userId,@RequestParam("isRole") Integer isRole);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<HolderFeign> {

        @Override
        public HolderFeign create(Throwable throwable) {
            return new HolderFeign() {

                @Override
                public ReturnModel requestVerification(Integer type, String info) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "sendVerification", info, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ReturnModel validateVerification(String code, String info) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryVerification", info, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<String> findBusinessDataAddress(String teamId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findBusinessDataAddress", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<HeaderUserInfo> queryUserInformation(String teamId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryUserInformation.teamId", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<HeaderUserInfo> queryUserInformation() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryUserInformation", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<HeaderUserInfo> queryUserInformation(String teamId, String loginToken) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryUserInformation.teamId.loginToken", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<String>> queryUserPermission(Long teamId, String account, Integer permissionType, Long typeId, String loginToken) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryUserPermission", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<PageModel<TeamStoreInfoModel>> findStorePageInfo(TeamStoreInfoPageParamModel request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findStorePageInfo", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<OperSubjectInfo>> queryOperatingSubjectByTeam(String teamId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryOperatingSubjectByTeam", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<RoleAndPostIdDTO> findUserRoleAndPost(String teamId, String userAccount) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findUserRoleAndPost", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<OperSubjectInfo>> queryOperatingSubject(String teamId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryOperatingSubject", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<MemberSystemPermissionDTO>> getSystemPermissionList(OperationPermissionQO operationPermissionRequest) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getSystemPermissionList", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public RolePermissionMapModel findUserRoleMapPermission(OperationPermissionQO operationPermissionRequest) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findUserRoleMapPermission", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<BusinessDataModel> queryBusinessData(String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryBusinessData", operSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<BusinessDataModel> queryBusinessData(String operSubjectGuid, String loginToken) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryBusinessData", loginToken, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<Boolean> getHasPermissionName(OperationPermissionQO request, String token) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getHasPermissionName", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<Boolean> validateUser(String account, String password) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "validateUser", account + ":" + password, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<String> createStore(StoreDTO storeDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "createStore", storeDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<String> updateStoreName(StoreDTO storeDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateStoreName", storeDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<String> updateStoreState(StoreDTO storeDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateStoreState", storeDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<String> getTemporaryToken(String account) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getTemporaryToken", account, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<String>> listAllOperationSubjectId() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listAllOperationSubjectId","", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<MemberSystemPermissionDTO>> getSystemPermissionList(OperationPermissionQO operationPermissionRequest, String token) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getSystemPermissionList",token, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<List<String>> listUserRoleIds(String loginToken, String teamId, String userId, Integer isRole) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listUserRoleIds","", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public RolePermissionMapModel findUserRoleMapPermission(OperationPermissionQO operationPermissionRequest, String loginToken) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findUserRoleMapPermission",loginToken, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}