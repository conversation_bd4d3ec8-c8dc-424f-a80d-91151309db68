package com.holderzone.member.common.module.settlement.apply.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单返回基本信息
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementApplyOderInfoVO implements Serializable {

    private static final long serialVersionUID = -5890901095496938659L;

    /**
     * 优惠类型
     *
     * @see SettlementDiscountOptionEnum
     */
    @ApiModelProperty(value = "优惠类型")
    private Integer discountOption;


    /**
     * 订单优惠金额
     */
    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmount;

    /**
     * 当前优惠券 单笔限制张数
     */
    @ApiModelProperty("优惠券单笔限制张数")
    private Integer couponLimitNum = 1;

    /**
     * 优惠券叠上限限制：0否 1是
     */
    private Integer couponLimit;

    /**
     * 退款是否退优惠：0否 1是
     */
    private Integer couponRollback;

    /**
     * 重新计算id
     */
    @ApiModelProperty("重新计算id")
    private String recountId;

    // ================================== 不要
    /**
     * 商品总金额
     * todo 需要返回 ?
     */
    @ApiModelProperty("商品总金额")
    private BigDecimal commodityTotalAmount;

    /**
     * 订单实付金额
     */
    @ApiModelProperty("实付金额")
    private BigDecimal realPaymentAmount;

    /**
     * 应付金额抵扣完
     *
     * @return
     */
    public boolean useUp() {
        return commodityTotalAmount.compareTo(discountAmount) <= 0;
    }
}