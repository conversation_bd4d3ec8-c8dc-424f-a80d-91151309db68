package com.holderzone.member.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 会员通-行业类型
 *
 * <AUTHOR>
 * @date 2025/3/28
 */
@Getter
@RequiredArgsConstructor
public enum MemberUnilinkIndustryTypeEnum {

    REPAST_INDUSTRY("餐饮行业"),

    RETAIL_INDUSTRY("零售行业");

    /**
     * 行业名称
     */
    private final String industryName;

    /**
     * 获取初始化行业类型
     *
     * @return 行业类型列表
     */
    public static List<MemberUnilinkIndustryTypeEnum> getDefaultIndustryTypeList() {
        return Arrays.asList(
                REPAST_INDUSTRY,
                RETAIL_INDUSTRY
        );
    }
}
