package com.holderzone.member.common.module.settlement.apply.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.equities.DiscountTypeEnum;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;


/**
 * 优惠列表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementApplyDiscountVO implements Serializable {


    private static final long serialVersionUID = -328225475647605451L;
    /**
     * 优惠类型
     *
     * @see SettlementDiscountOptionEnum
     */
    @ApiModelProperty(value = "优惠类型")
    private Integer discountOption;

    /**
     * 优惠guid，必须
     */
    @ApiModelProperty(value = "优惠guid")
    private String discountGuid;

    /**
     * 优惠名称
     */
    @ApiModelProperty(value = "优惠名称")
    private String discountName;

    /**
     * 优惠金额
     * todo 展示顺序：金额倒序
     */
    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmount;

    /**
     * 当前优惠券 单笔限制张数
     */
    @ApiModelProperty("优惠券单笔限制张数")
    private Integer couponLimitNum = 1;

    /**
     * 具体优惠项
     */
    @ApiModelProperty(value = "具体优惠项")
    private List<SettlementApplyDiscountDetailVO> discountList;

    /**
     * 构造对象
     *
     * @param discountType {@link DiscountTypeEnum}
     * @return
     */
    public static SettlementApplyDiscountVO buildByDiscountType(int discountType) {
        final SettlementDiscountOptionEnum optionEnum = DiscountTypeEnum.convertOption(discountType);
        return new SettlementApplyDiscountVO()
                .setDiscountOption(optionEnum.getCode())
                .setDiscountName(optionEnum.getDes());
    }

    public static SettlementApplyDiscountVO buildByDiscountType(SettlementDiscountOptionEnum optionEnum) {
        return new SettlementApplyDiscountVO()
                .setDiscountOption(optionEnum.getCode())
                .setDiscountName(optionEnum.getDes());
    }

    /**
     * 构造对象
     *
     * @param discountOption 优惠项类型
     * @return
     */
    public static SettlementApplyDiscountVO build(int discountOption) {
        final SettlementDiscountOptionEnum optionEnum = SettlementDiscountOptionEnum.getEnum(discountOption);
        return new SettlementApplyDiscountVO()
                .setDiscountOption(optionEnum.getCode())
                .setDiscountName(optionEnum.getDes())
                .setDiscountList(Collections.emptyList());
    }

    public void calculateDiscountAmount() {
        this.discountAmount = discountList.stream()
                .filter(d -> d.getIsChecked() == BooleanEnum.TRUE.getCode())
                .map(SettlementApplyDiscountDetailVO::getDiscountAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
