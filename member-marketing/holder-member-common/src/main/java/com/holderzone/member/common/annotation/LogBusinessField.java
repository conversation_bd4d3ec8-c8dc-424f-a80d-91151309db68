package com.holderzone.member.common.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @description 日志记录业务guid
 * @date 2021/8/24
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogBusinessField {

    String name();

    /**
     * eg:   0:是,1:否
     * @return
     */
    String enumName() default "";

    /**
     * 字段类型，"",list-base,list,map,obj,[]
     * (fieldType == []  说明是String[]、或者List<基础数据类型包装类>)
     *
     * @return
     */
    String fieldType() default "";
}
