package com.holderzone.member.common.module.settlement.constant;

/**
 * 结算台常量
 *
 * <AUTHOR>
 * @date 2023/10/20
 * @since 1.8
 */
public final class SettlementConstant {

    private SettlementConstant() {
        //default
    }

    /**
     * 规则出现不可叠加提示
     */
    public final static String RULE_DIS_APPEND_TIPS = "已选优惠不支持与其它优惠共享，确认核销后其它优惠将取消使用";

    /**
     * 优惠券使用上限数量
     */
    public final static int COUPON_LIMIT_NUM = 99;

    /**
     * 比列精度
     */
    public static final int FOUR_LENGTH = 4;

    /**
     * 比列精度
     */
    public final static int SCALE_LENGTH = 5;

    /**
     * 最终精度
     */
    public final static int FINAL_LENGTH = 2;

    /**
     * 最终精度
     */
    public static final int THREE_LENGTH = 3;

    /**
     * 最终精度
     */
    public static final int ONE_LENGTH = 1;

    /**
     * 折扣显示
     */
    public final static String GRADE_DISCOUNT  = "等级折扣";

    /**
     * 折扣计算缓存时间
     */
    public final static int SETTLEMENT_RECOUNT_TTL  = 5;
}
