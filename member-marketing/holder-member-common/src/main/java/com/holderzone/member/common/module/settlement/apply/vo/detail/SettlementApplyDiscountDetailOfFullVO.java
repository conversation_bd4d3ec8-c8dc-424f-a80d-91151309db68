package com.holderzone.member.common.module.settlement.apply.vo.detail;

import com.holderzone.member.common.dto.activity.FullReductionFoldActivityDTO;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyDiscountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SettlementApplyDiscountDetailOfFullVO extends SettlementApplyDiscountDetailVO {


    private static final long serialVersionUID = -3135496341752617040L;
    /**
     * 使用规则
     */
    @ApiModelProperty("使用规则")
    private List<FullReductionFoldActivityDTO> foldActivityDTOS;

    /**
     * 满减策略类型
     * 0 按阶梯门槛，满足条件后优惠  满 1 单门槛可叠加优惠 每满
     */
    private Integer fullReductionTacticsType;


    /**
     * 满减策略类型
     * 优惠门槛 0 消费门槛 1 购买件数
     */
    private Integer discountSillType;
}
