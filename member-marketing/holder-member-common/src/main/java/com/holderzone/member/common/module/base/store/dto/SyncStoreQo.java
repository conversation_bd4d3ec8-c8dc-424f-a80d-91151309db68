package com.holderzone.member.common.module.base.store.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.enums.sale.SaleStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 查询门店信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/10 11:53
 */
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Data
public class SyncStoreQo extends PageDTO {

    /**
     * 主体
     */
    private String operSubjectGuid;
    /**
     * 门店编号+门店名称   模糊搜索
     */
    private String name;
    /**
     * 门店集合
     */
    private List<String> storeIds;
    /**
     * 门店状态：默认查询启用
     *
     * @see com.holderzone.member.common.enums.sale.SaleStatusEnum#des
     */
    private List<String> status = SaleStatusEnum.enableDes();

    /**
     * 查询来源
     * 0 crm, 1本地
     */
    @JsonIgnore
    private int source;
    public void defaultOperSubjectGuid() {
        if (StringUtils.isBlank(this.operSubjectGuid)) {
            this.operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        }
    }
}
