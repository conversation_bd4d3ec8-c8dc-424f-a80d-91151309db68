package com.holderzone.member.common.enums.activity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 活动状态通用
 *
 * <AUTHOR>
 * 红包活动状态 1 未发布 2 未开始 3 发布中  4 已暂停 5 已结束 6 进行中
 * @version 1.0
 * @className ActivityStateEnum
 * @description 活动状态
 * @program holder-saas-member-dto
 */
@Getter
@AllArgsConstructor
public enum RedActivityStateEnum {

    ACTIVITY_STATE_ROUGH(1, "未发布"),
    ACTIVITY_STATE_NOT_START(2, "未开始"),
    ACTIVITY_STATE_START(3, "发布中"),
    ACTIVITY_STATE_CAN_SEND_STOP(4, "已暂停"),
    ACTIVITY_STATE_OVER(5, "已结束"),
    ACTIVITY_STATE_PROGRESS(6, "进行中"),
    ACTIVITY_PAUSE_PUBLISH(7, "暂停已发布");


    /**
     * 通过code获取描述信息
     *
     * @param code 参数值
     * @return 信息描述
     */
    public static String getDesByCode(int code) {
        RedActivityStateEnum[] businessMessages = RedActivityStateEnum.values();
        for (RedActivityStateEnum businessMessage : businessMessages) {
            if (businessMessage.getCode() == code) {
                return businessMessage.getDes();
            }
        }
        return "";
    }

    /**
     * 通过描述获取code值
     *
     * @param des 信息描述
     * @return 参数值
     */
    public int getCodeByDes(String des) {
        if (StringUtils.isEmpty(des)) {
            return -1;
        }
        RedActivityStateEnum[] businessMessages = RedActivityStateEnum.values();
        for (RedActivityStateEnum businessMessage : businessMessages) {
            if (businessMessage.getDes().equals(des)) {
                return businessMessage.getCode();
            }
        }
        return -1;
    }

    private int code;
    private String des;
}
