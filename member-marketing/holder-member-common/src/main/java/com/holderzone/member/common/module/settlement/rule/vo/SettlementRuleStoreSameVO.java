package com.holderzone.member.common.module.settlement.rule.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementRuleStoreDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 结算规则门店
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SettlementRuleStoreSameVO extends SettlementRuleStoreDTO {


    private static final long serialVersionUID = 8003707131858121955L;

    /**
     * 规则guid
     */
    private String settlementRuleGuid;
    /**
     * 规则名称
     */
    private String settlementRuleName;

    /**
     * 业务：0全部 1部分
     */
    @JsonIgnore
    private Integer applyBusiness;

    /**
     * 业务数组
     */
    @JsonIgnore
    private String applyBusinessJson;

    /**
     * 门店：0全部 1部分
     */
    private Integer applicableAllStore;

    private String  storeTeamInfoId;
}
