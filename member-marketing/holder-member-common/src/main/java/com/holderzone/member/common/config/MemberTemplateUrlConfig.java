package com.holderzone.member.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 模板下载地址
 * @date 2021/9/1
 */
@Component
@Data
@ConfigurationProperties(prefix = "member.upload")
public class MemberTemplateUrlConfig {

    private String memberDownloadExcelUrl;

    private String memberOpenCardDownloadExcelUrl;

    private String memberRelationCreditDownloadExcelUrl;

    private String memberRelationOfflineActivityDownloadExcelUrl;

    private String memberRechargeGiftDownloadExcelUrl;

    private String memberSubsidyDownloadExcelUrl;

    private String memberCouponPackageDownloadExcelUrl;

    private String limitSpecialsActivityMemberDownloadExcelUrl;

    private String limitSpecialsActivityItemDownloadExcelUrl;

    private String limitSpecialsActivitySalesItemDownloadExcelUrl;

    private String fullReductionFoldActivityItemDownloadExcelUrl;
}
