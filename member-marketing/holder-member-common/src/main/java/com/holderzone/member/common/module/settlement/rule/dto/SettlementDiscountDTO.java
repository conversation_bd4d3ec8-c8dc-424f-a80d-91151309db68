package com.holderzone.member.common.module.settlement.rule.dto;

import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberMarketingException;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 结算规则优惠项
 *
 * <AUTHOR>
 * @date 2023/9/1
 **/
@Data
public class SettlementDiscountDTO {

    /**
     * 主体，必传
     */
    private String operSubjectGuid;

    /**
     * 优惠类型
     *
     * @see SettlementDiscountOptionEnum
     */
    private Integer option;

    /**
     * 新增、更新优惠项
     * discountList、delDiscountGuids 可同时传递
     */
    private List<Discount> discountList;

    /**
     * 删除优惠项
     * discountList、delDiscountGuids 可同时传递
     */
    private List<String> delDiscountGuids;


    /**
     * 优惠项
     */
    @Data
    @Accessors(chain = true)
    public static class Discount {

        /**
         * 创建时间
         */
        @ApiModelProperty(value = "创建时间")
        private LocalDateTime gmtCreate = LocalDateTime.now();
        /**
         * @see SettlementDiscountTypeEnum
         */
        @ApiModelProperty(value = "类型：0 单品级优惠 1订单级优惠 2资产优惠")
        private Integer discountType;

        @ApiModelProperty(value = "数量")
        private Integer discountNum;

        /**
         * 优惠guid，必须
         * SettlementDiscountOptionEnum + discountGuid 唯一
         */
        @ApiModelProperty(value = "优惠guid")
        private String discountGuid;

        @ApiModelProperty(value = "优惠力度")
        private String discountDynamic;

        @ApiModelProperty(value = "优惠名称")
        private String discountName;

        /**
         * 是否首次新增
         */
        private Integer isFirstAdd;

        /**
         * 活动规则
         * 共享互斥关系 0-互斥 1-共享
         */
        @ApiModelProperty(value = "活动规则 共享互斥关系 0-互斥 1-共享")
        private Integer relationRule;
    }

    public void validate() {
        if (operSubjectGuid == null
                || option == null
                || (CollectionUtils.isEmpty(discountList)
                && CollectionUtils.isEmpty(delDiscountGuids))
        ) {
            throw new MemberMarketingException(MemberAccountExceptionEnum.ERROR_VERIFY);
        }
    }
}
