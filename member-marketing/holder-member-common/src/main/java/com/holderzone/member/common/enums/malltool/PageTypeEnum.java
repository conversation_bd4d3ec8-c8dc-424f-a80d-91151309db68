package com.holderzone.member.common.enums.malltool;

import java.util.Objects;

/**
 * 页面类型枚举
 */
public enum PageTypeEnum {

    BUSINESS_PAGE(1, "业务页面"),

    MALL_PAGE(2, "商城页面"),

    COMMON_PAGE(3, "通用页面"),

    MARKETING_ACTIVITY(4, "营销活动"),
    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return desc;
    }

    PageTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PageTypeEnum getEnumByName(String name) {

        if (Objects.isNull(name)) {
            return null;
        }
        for (PageTypeEnum pageTypeEnum : PageTypeEnum.values()) {
            if (Objects.equals(pageTypeEnum.name(), name)) {
                return pageTypeEnum;
            }
        }
        return null;
    }
}
