package com.holderzone.member.common.module.settlement.apply.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * 结算规则应用订单数据
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SettlementApplyOrderCalculateDTO extends SettlementApplyOrderDTO implements Serializable {

    private static final long serialVersionUID = -5180663183089277902L;
    /**
     * 优惠项排序 大项
     */
    private List<Integer> optionHandlers;
}
