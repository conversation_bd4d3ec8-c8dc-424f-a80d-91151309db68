package com.holderzone.member.common.module.marketing.purchase.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 限量抢购活动商品
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Data
@Accessors(chain = true)
public class PurchaseReserveCommodityVO implements Serializable {


    private static final long serialVersionUID = 5973069689269929422L;

    @ApiModelProperty(value = "限购活动订单guid")
    private String purchaseOrderGuid;

    @ApiModelProperty(value = "渠道")
    private String channel;

    @ApiModelProperty(value = "商品id")
    private String commodityId;

    @ApiModelProperty(value = "商品编号")
    private String commodityCode;

    @ApiModelProperty(value = "商品数量")
    private Integer commodityNum;

    @ApiModelProperty(value = "purchaseGuid")
    private String purchaseGuid;

    /**
     * 订单时间
     */
    @ApiModelProperty("订单时间")
    private LocalDateTime reserveTime;
}
