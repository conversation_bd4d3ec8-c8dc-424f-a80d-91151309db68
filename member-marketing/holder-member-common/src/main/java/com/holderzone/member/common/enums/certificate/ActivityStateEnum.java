package com.holderzone.member.common.enums.certificate;

import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * 活动状态通用
 *
 * <AUTHOR>
 * 红包活动状态 1 未发布 2 未开始 3 发布中  4 已暂停 5 已结束 6 进行中 7 暂停已发布
 * @description 活动状态
 */
public enum ActivityStateEnum {

    /**
     * 未发布
     */
    ACTIVITY_STATE_ROUGH(1, "未发布"),

    /**
     * 未开始
     */
    ACTIVITY_STATE_NOT_START(2, "未开始"),

    /**
     * 发布中
     */
    ACTIVITY_STATE_START(3, "发布中"),

    /**
     * 已暂停
     */
    ACTIVITY_STATE_CAN_SEND_STOP(4, "已暂停"),

    /**
     * 进行中
     */
    ACTIVITY_STATE_PROGRESS(6, "进行中"),

    /**
     * 已结束
     */
    ACTIVITY_STATE_OVER(5, "已结束"),

    /**
     * 暂停已发布
     */
    ACTIVITY_PAUSE_PUBLISH(7, "暂停已发布");


    private final int code;

    private final String des;

    ActivityStateEnum(int code, String des){
        this.code = code;
        this.des = des;
    }

    public String getDes(){
        return this.des;
    }

    public int getCode(){
        return this.code;
    }

    /**
     * 通过des获取code
     * @param des 描述信息
     * @return code参数
     */
    public int getCodeByDes(String des){
        if (StringUtils.isEmpty(des)) {
            return -1;
        }
        CertificationConditionTypeEnum[] conditionTypeEnums = CertificationConditionTypeEnum.values();
        for (CertificationConditionTypeEnum conditionTypeEnum : conditionTypeEnums) {
            if (conditionTypeEnum.getDes().equals(des)) {
                return conditionTypeEnum.getCode();
            }
        }
        return -1;
    }

    public String getDesByCode(Integer code){
        if (Objects.isNull(code)) {
            return "";
        }
        CertificationConditionTypeEnum[] conditionTypeEnums = CertificationConditionTypeEnum.values();
        for (CertificationConditionTypeEnum conditionTypeEnum : conditionTypeEnums) {
            if (code.equals(conditionTypeEnum.getCode())) {
                return conditionTypeEnum.getDes();
            }
        }
        return "";
    }
}
