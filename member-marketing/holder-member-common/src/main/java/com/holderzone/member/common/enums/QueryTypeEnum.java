package com.holderzone.member.common.enums;

import com.holderzone.member.common.enums.specials.SpecialsTypeEnum;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/6/5
 * @description 查询类型
 */
public enum QueryTypeEnum {

    UNKNOWN_TYPE(-1, "未知类型"),

    LIMIT_SPECIALS_ACTIVITY(1, "限时特价活动"),

    FOLLOW_RED_PACKET_ACTIVITY(2, "随行红包"),

    NTH_ACTIVITY(3, "第N份优惠活动"),

    ;

    private final int code;

    private final String des;

    QueryTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return this.code;
    }

    public String getDes() {
        return this.des;
    }

    public static QueryTypeEnum getEnum(int code) {
        for (QueryTypeEnum typeEnum : QueryTypeEnum.values()) {
            if (Objects.equals(typeEnum.getCode(), code)) {
                return typeEnum;
            }
        }
        return QueryTypeEnum.UNKNOWN_TYPE;
    }

}
