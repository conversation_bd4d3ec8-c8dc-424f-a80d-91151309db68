package com.holderzone.member.common.module.settlement.apply.dto;

import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.module.settlement.util.SettlementVerifyUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * 商品
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementApplyCommodityDTO implements Serializable {

    private static final long serialVersionUID = 3559064153837638379L;

    /**
     * 唯一id
     * todo 商品id相同时，这个需要保持唯一
     */
    @ApiModelProperty("唯一id")
    @NotEmpty(message = "唯一id")
    private String rid;

    /**
     * 商品id
     * todo 组合商品 需要相同结构
     */
    @ApiModelProperty("商品唯一id")
    @NotEmpty(message = "商品id必传")
    private String commodityId;

    /**
     * 商品skuId
     */
    private String skuId;

    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String commodityCode;

    /**
     * 商品数量
     */
    @ApiModelProperty("商品数量")
    @NotEmpty(message = "商品数量必传")
    private BigDecimal commodityNum;
    /**
     * 商品单价
     */
    @ApiModelProperty("商品单价")
    @NotEmpty(message = "商品单价必传")
    private BigDecimal commodityPrice;


    /**
     * 商品名称
     * todo 需要缓存，防止过程中变更
     */
    @ApiModelProperty("商品名称")
    private String commodityName;

    /**
     * 商品总额,减少计算次数
     * 缓存 :一进来就算好
     */
    @ApiModelProperty("商品总额")
    private BigDecimal commodityTotalPrice = BigDecimal.ZERO;

    /**
     * 商品 优惠总额金额
     */
    @ApiModelProperty("商品优惠总额")
    private BigDecimal discountFee = BigDecimal.ZERO;

    /**
     * 不为空 表示这个商品有改价
     * 改价后的单金额
     */
    private BigDecimal discountPriceInShopCar;

    /**
     * 改价后的总额
     */
    private BigDecimal discountTotalPriceInShopCar;

    /**
     * 经过优惠后的商品总额（后端字段）  计算商品总额 优先此字段
     */
    private BigDecimal afterDiscountTotalPrice;

    /**
     * 商品优惠总金额 = 单品优惠 x 数量
     */
    @ApiModelProperty("商品优惠金额")
    private BigDecimal commodityMinistryPrice = new BigDecimal(BigDecimal.ROUND_UP);

    /**
     * 商品类型  只计算销售中心且无改价的商品
     * 2无码加购  3 快速结账商品  1 来自于销售中心的商品  11 扫码加购的商品
     */
    private Integer goodsType;

    /**
     * 商品 优惠总数量（兑换券的数量）
     */
    @ApiModelProperty("优惠总数量")
    private BigDecimal exchangeDiscountNum = new BigDecimal(BigDecimal.ROUND_UP);

    /**
     * 限时特价后的单价
     */
    private BigDecimal timeLimitPrice;

    /**
     * 限时特价的商品数量
     */
    private BigDecimal timeLimitNum = new BigDecimal(BigDecimal.ROUND_UP);

    /**
     * 商品所属店铺guid
     */
    private String storeGuid;

    /**
     * 增加商品优惠金额
     *
     * @param amount 金额
     */
    public void addDiscountFee(BigDecimal amount) {

        this.discountFee = this.discountFee.add(amount);
    }

    /**
     * 递减商品优惠后金额
     *
     * @param amount 金额
     */
    public void subtractAfterDiscountTotalPrice(BigDecimal amount) {

        BigDecimal secondPrice;
        if (Objects.nonNull(this.afterDiscountTotalPrice)) {
            secondPrice = this.afterDiscountTotalPrice.subtract(amount);
        } else {
            secondPrice = this.discountTotalPriceInShopCar.subtract(amount);
        }
        this.afterDiscountTotalPrice = secondPrice.compareTo(BigDecimal.ZERO) > NumberConstant.NUMBER_0 ?
                secondPrice : BigDecimal.ZERO;
    }

    /**
     * 唯一主键
     *
     * @return 主键
     */
    public String key() {
        return rid + commodityId;
    }

    /**
     * 验证
     */
    public void validate() {
        SettlementVerifyUtil.isNull(commodityNum, "商品数量!");
        SettlementVerifyUtil.isNull(commodityPrice, "商品单价必传!");
    }

    /**
     * 参与分摊商品
     */
    public boolean checkApplyCommodity() {
        return (Objects.nonNull(this.getAfterDiscountTotalPrice())
                && this.getAfterDiscountTotalPrice().compareTo(BigDecimal.ZERO) == 0)
                || this.getDiscountTotalPriceInShopCar().compareTo(BigDecimal.ZERO) == 0;
    }
}
