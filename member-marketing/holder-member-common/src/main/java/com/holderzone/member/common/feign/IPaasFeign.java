package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.PageModel;
import com.holderzone.member.common.dto.ipaas.AddAndUpdateIPaasDTO;
import com.holderzone.member.common.dto.ipaas.CheckPasswordDTO;
import com.holderzone.member.common.dto.ipaas.QueryUserEnterpriseDTO;
import com.holderzone.member.common.dto.ipaas.TeamOperationStoreIdModel;
import com.holderzone.member.common.dto.mall.TeamOperationSubjectModel;
import com.holderzone.member.common.dto.mall.TeamStoreAllModel;
import com.holderzone.member.common.dto.mall.TeamStoreAllParamModel;
import com.holderzone.member.common.dto.message.SendShortMessageDTO;
import com.holderzone.member.common.vo.feign.CrmFeignVo;
import com.holderzone.member.common.vo.feign.IPaasFeignModel;
import com.holderzone.member.common.vo.ipass.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @BelongsProject: member-marketing
 * @BelongsPackage: com.holderzone.member.common.feign
 * @Author: li ao
 * @CreateTime: 2024-03-20  08:52
 * @Description: iPass feign
 * @Version: 1.0
 */
@Component
@FeignClient(name = FilterConstant.I_PAAS, fallbackFactory = IPaasFeign.ServiceFallBack.class, url = "${feign.iPaas-member}")
public interface IPaasFeign {

    @PostMapping(value = "/notification/batchSendCustomSmsNotice/v1")
    IPaasFeignModel<String> sendShortMessage(@RequestBody SendShortMessageDTO messageDTO);

    /**
     * 主体下零售门店查询
     *
     * @return
     */
    @GetMapping(value = "/subject/detail/v1")
    IPaasFeignModel<SubjectListVO> findSubjectDetail(@RequestParam(value = "id") Integer id);


    @PostMapping(value = "/store/findStoreByTeamInfoIds/v1")
    IPaasFeignModel<StoreSubjectListVO> findStoreByTeamInfoIds(@RequestBody TeamOperationStoreIdModel model);

    /**
     * 主体下零售门店查询
     *
     * @param model
     * @return
     */
    @PostMapping(value = "/store/findStoreByOperationSubjectIds/v1")
    IPaasFeignModel<StoreSubjectListVO> findStoreByOperationSubjectId(@RequestBody TeamOperationSubjectModel model);

    /**
     * 零售门店查询
     *
     * @param model
     * @return
     */
    @PostMapping(value = "/store/findStoreByPage/v1")
    CrmFeignVo<PageModel<TeamStoreAllModel>> findStoreByPage(@RequestBody TeamStoreAllParamModel model);


    /**
     * 查询企业下所有运营主体
     *
     * @param teamInfoId 企业ID
     **/
    @GetMapping("/subject/all/list/v1")
    IPaasFeignModel<SubjectListVO> queryOperatingSubjectByTeam(@RequestParam("teamInfoId") Long teamInfoId);

    /**
     * 查询企业下所有门店信息
     *
     * @param teamInfoId 企业id
     **/
    @GetMapping("/store/all/list/v1")
    IPaasFeignModel<StoreListVO> getStoreByTeam(@RequestParam("teamInfoId") Long teamInfoId);

    /**
     * 查询门店详情
     *
     * @param id 门店id
     **/
    @GetMapping("/store/info/v1")
    IPaasFeignModel<StoreListVO> getStoreDetail(@RequestParam("id") Long id);

    /**
     * 新增/编辑门店基础信息
     *
     * @param addAndUpdateIPaasDTO addAndUpdateIPaasDTO
     **/
    @PostMapping("/store/save/v1")
    void addAndUpdateStore(@RequestBody AddAndUpdateIPaasDTO addAndUpdateIPaasDTO);


    /**
     * 查询用户信息
     *
     * @param account 用户账号
     **/
    @GetMapping("/user/info/v1")
    IPaasFeignModel<UserInfoVO> queryUserInfoDetail(@RequestParam("account") String account);


    /**
     * 校验用户密码是否正确
     *
     * @param checkPasswordDTO checkPasswordDTO
     **/
    @PostMapping("/user/password/check/v1")
    IPaasFeignModel<Boolean> checkPassword(@RequestBody CheckPasswordDTO checkPasswordDTO);

    /**
     * 查询账号在指定产品下的所有企业信息
     *
     * @param dto 请求参数
     * @return 企业信息
     */
    @PostMapping("/findUserRoleEnterpriseWithProduct/v1")
    IPaasFeignModel<EnterpriseUserRoleInfoVO> findUserRoleEnterpriseWithProduct(@RequestBody QueryUserEnterpriseDTO dto);

    /**
     * 根据企业查询所有部门信息
     *
     * @param teamId 企业id
     * @return 企业/部门信息
     */
    @GetMapping("/team/findTeamTreeByTeamId/v1")
    IPaasFeignModel<TeamInfoVO> findTeamTreeByTeamId(@RequestParam("teamId") Long teamId);

    /**
     * 获取用户在企业下的功能权限
     *
     * @param account 手机号
     * @param teamId  企业id
     * @return 功能权限
     */
    @GetMapping("/enterpriseUser/queryIdentificationNames/v1")
    IPaasFeignModel<String> queryIdentificationNames(@RequestParam("account") String account, @RequestParam("teamId") String teamId);

    /**
     * 根据账号查询所属企业有权限的运营主体
     *
     * @param account            手机号
     * @param identificationName 权限标识
     * @return 运营主体
     */
    @GetMapping("/subject/listByPermission/v1")
    IPaasFeignModel<SubjectVO> listSubjectByPermission(@RequestParam("account") String account, @RequestParam("identificationName") String identificationName);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<IPaasFeign> {

        @Override
        public IPaasFeign create(Throwable throwable) {
            return new IPaasFeign() {

                @Override
                public IPaasFeignModel<String> sendShortMessage(SendShortMessageDTO messageDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "sendShortMessage", messageDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public IPaasFeignModel<SubjectListVO> findSubjectDetail(Integer id) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findSubjectDetail", id, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public IPaasFeignModel<StoreSubjectListVO> findStoreByTeamInfoIds(TeamOperationStoreIdModel model) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findStoreByTeamInfoIds", model, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public IPaasFeignModel<StoreSubjectListVO> findStoreByOperationSubjectId(TeamOperationSubjectModel model) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findStoreByOperationSubjectId", model, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignVo<PageModel<TeamStoreAllModel>> findStoreByPage(TeamStoreAllParamModel model) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findStoreByPage", model, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public IPaasFeignModel<SubjectListVO> queryOperatingSubjectByTeam(Long teamInfoId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryOperatingSubjectByTeam", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public IPaasFeignModel<StoreListVO> getStoreByTeam(Long teamInfoId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getStoreByTeam", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public IPaasFeignModel<StoreListVO> getStoreDetail(Long id) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getStoreDetail", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void addAndUpdateStore(AddAndUpdateIPaasDTO addAndUpdateIPaasDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "addAndUpdateStore", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public IPaasFeignModel<UserInfoVO> queryUserInfoDetail(String account) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryUserInformation", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public IPaasFeignModel<Boolean> checkPassword(CheckPasswordDTO checkPasswordDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "checkPassword", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public IPaasFeignModel findUserRoleEnterpriseWithProduct(QueryUserEnterpriseDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findUserRoleEnterpriseWithProduct", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public IPaasFeignModel<TeamInfoVO> findTeamTreeByTeamId(Long teamId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findTeamTreeByTeamId", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public IPaasFeignModel<String> queryIdentificationNames(String account, String teamId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryIdentificationNames", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public IPaasFeignModel<SubjectVO> listSubjectByPermission(String account, String identificationName) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listSubjectByPermission", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }
}
