package com.holderzone.member.common.constant;

import com.beust.jcommander.internal.Lists;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description 文件常量
 * @date 2021/8/31
 */
public final class FileConstant {

    /**
     * 10M
     * 1024 * 1024 * 10
     */
    public static final long BYTE_SIZE_10_TRILLION = 10485760;

    /**
     * 20M
     * 1024 * 1024 * 20
     */
    public static final long BYTE_SIZE_20_TRILLION = 20971520;

    public static final List<String> CORRECT_TYPE_LIST = Arrays.asList("xls", "xlsx", "XLS", "XLSX");

    /**
     * 图片（image）：10MB，支持JPG,PNG格式
     */
    public static final List<String> ENTERPRISE_WECHAT_IMAGE_LIST = Lists.newArrayList("jpg", "png");

    /**
     * 语音（voice） ：2MB，播放长度不超过60s，仅支持AMR格式
     */
    public static final List<String> ENTERPRISE_WECHAT_VOICE_LIST = Lists.newArrayList("amr");

    /**
     * 视频（video） ：10MB，支持MP4格式
     */
    public static final List<String> ENTERPRISE_WECHAT_VIDEO_LIST = Lists.newArrayList("mp4");

    /**
     * 普通文件（file）：20MB
     */
    public static final List<String> ENTERPRISE_WECHAT_FILE_LIST = Lists.newArrayList();

    public final static String UPLOAD_PHYSICAL_CARD_SECRET_NAME = "卡密";

    public final static String UPLOAD_PHYSICAL_CARD_SECRET_BIND_SHEET = "已绑定账户";

    public final static String UPLOAD_PHYSICAL_CARD_SECRET_NOT_BIND_SHEET = "未绑定账户";

    public final static String DEFAULT_SHEET = "sheet1";

    public final static String UPLOAD_MEMBER_GROWTH_NAME = "成长记录";

}
