package com.holderzone.member.common.enums.commodity;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 分表
 */
@Getter
@AllArgsConstructor
public enum ShardingTableEnum {


    COMMODITY(0, "COMMODITY", "hsa_commodity", "商品"),
    CATEGORY(1, "CATEGORY", "hsa_category", "分类"),
    STRATEGY(2, "STRATEGY", "hsa_strategy", "策略"),
    TAG(3, "TAG", "hsa_tag", "标签");

    private int code;

    private String codeName;

    private String logicTableName;

    private String des;

}
