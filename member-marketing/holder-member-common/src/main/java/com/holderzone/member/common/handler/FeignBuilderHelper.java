package com.holderzone.member.common.handler;

import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.RedisKeyConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.external.ExternalSupport;
import feign.Feign;
import feign.Target;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * Feign Builder
 * 覆写Target的url()方法
 *
 * <AUTHOR>
 * @date 2022-3-9 15:10:12
 **/
@Component
@Slf4j
public class FeignBuilderHelper extends Feign.Builder {

    @Resource
    private RedisTemplate redisTemplate;

    // TODO holder改造（已改）
    @Resource
    @Lazy
    private ExternalSupport externalSupport;

    @Override
    public <T> T target(Target<T> target) {
        return super.target(new Target.HardCodedTarget<T>(target.type(), target.name(), target.url()) {
            @Override
            public String url() {
                if (FilterConstant.FEIGN_ODOO.equals(this.name())) {
                    //按企业动态配置地址
                    //指定企业适用
                    return getOdooRealFeignUrl(super.url());
                }
                return super.url();
            }
        });
    }

    private String getOdooRealFeignUrl(String configUrl) {
        String item = FilterConstant.FEIGN_ODOO + StringConstant.COLON + ThreadLocalCache.getEnterpriseGuid();
        String realUrl = (String) redisTemplate.opsForHash().get(RedisKeyConstant.CACHE_FEIGN, item);
        if (Objects.isNull(realUrl) || StringUtils.isEmpty(realUrl + "")) {
            String dataUrl = externalSupport.baseServer(ThreadLocalCache.getSystem()).findBusinessDataAddress(ThreadLocalCache.getEnterpriseGuid());
            if (StringUtils.isEmpty(dataUrl)) {
                realUrl = configUrl;
            } else {
                realUrl = Optional.ofNullable(dataUrl).orElse(configUrl);
                redisTemplate.opsForHash().put(RedisKeyConstant.CACHE_FEIGN, item, realUrl);
            }

        }
        log.info("getFeignUrl dynamicFeign -> item:{},value:{}", item, realUrl);
        return realUrl;
    }
}
