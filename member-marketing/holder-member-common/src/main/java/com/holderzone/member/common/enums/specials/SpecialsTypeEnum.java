package com.holderzone.member.common.enums.specials;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/23
 * @description 折扣方式枚举
 */
public enum SpecialsTypeEnum {
    // 特价类型 1打折 2减价 3指定价格

    UNKNOWN_TYPE(-1, "未知类型"),

    DISCOUNT(1, "打折"),

    SALE(2, "减价"),

    SPECIFY_PRICE(3, "指定价格"),
    ;

    private final int code;
    private final String desc;

    SpecialsTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getName(int code) {
        for (SpecialsTypeEnum typeEnum : SpecialsTypeEnum.values()) {
            if (typeEnum.getCode() == code) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }

    public static int getCode(String desc) {
        for (SpecialsTypeEnum typeEnum : SpecialsTypeEnum.values()) {
            if (Objects.equals(typeEnum.getDesc(), desc)) {
                return typeEnum.getCode();
            }
        }
        return SpecialsTypeEnum.UNKNOWN_TYPE.getCode();
    }

    public static SpecialsTypeEnum getEnum(String desc) {
        for (SpecialsTypeEnum typeEnum : SpecialsTypeEnum.values()) {
            if (Objects.equals(typeEnum.getDesc(), desc)) {
                return typeEnum;
            }
        }
        return SpecialsTypeEnum.UNKNOWN_TYPE;
    }

    public static SpecialsTypeEnum getEnum(Integer code) {
        for (SpecialsTypeEnum typeEnum : SpecialsTypeEnum.values()) {
            if (typeEnum.getCode()== code) {
                return typeEnum;
            }
        }
        return SpecialsTypeEnum.UNKNOWN_TYPE;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
