package com.holderzone.member.common.enums.integral;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分摊项类型枚举
 * 定义了优惠分摊中支持的各种项目类型
 * 
 * @date 2025-10-15
 */
@Getter
@AllArgsConstructor
public enum DiscountItemType {
    
    /**
     * 商品
     */
    COMMODITY(0, "commodity", "商品"),
    
    /**
     * 运费
     */
    FREIGHT(1, "freight", "运费"),
    
    /**
     * 桌台费
     */
    TABLE(2, "table", "桌台费");
    
    /**
     * 类型编码
     */
    private final int code;
    
    /**
     * 类型标识
     */
    private final String identifier;
    
    /**
     * 类型描述
     */
    private final String description;
    
    /**
     * 根据编码获取枚举
     * 
     * @param code 类型编码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static DiscountItemType fromCode(int code) {
        for (DiscountItemType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
}

