package com.holderzone.member.common.module.settlement.apply.enums;

import com.google.common.collect.Lists;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 前端界面展示的类型
 *
 * <AUTHOR>
 * @date 2023/10/25
 * @since 1.8
 */
@AllArgsConstructor
@Getter
public enum SettlementDiscountShowTypeEnum {

    /**
     * 会员优惠
     */
    MEMBER_PRICE(2, "会员优惠",
            Lists.newArrayList(
                    SettlementDiscountOptionEnum.MEMBER_DISCOUNT.getCode(),
                    SettlementDiscountOptionEnum.MEMBER_CARD_DISCOUNT.getCode()
            )),

    /**
     * 优惠券
     */
    COUPON(10, "优惠券", Lists.newArrayList(
            SettlementDiscountOptionEnum.COUPON_VOUCHER.getCode(),
            SettlementDiscountOptionEnum.COUPON_DISCOUNT.getCode(),
            SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode())),

    /**
     * 限时特价
     */
    LIMITED_TIME_SPECIAL(22, "限时特价", Lists.newArrayList(
            SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode())),

    /**
     * 第N份优惠
     */
    NTH_DISCOUNT(23, "第N份优惠", Lists.newArrayList(
            SettlementDiscountOptionEnum.NTH_DISCOUNT.getCode())),

    /**
     * 积分抵现
     */
    INTEGRAL(91, "积分抵现", Lists.newArrayList(
            SettlementDiscountOptionEnum.INTEGRAL_EXPLAIN.getCode()
    )),

    /**
     * 满减满折
     */
    FULL_OFF(20, "满减满折", Lists.newArrayList(
            SettlementDiscountOptionEnum.FULL_OFF.getCode()));

    /**
     * 编码
     */
    private final int code;

    /**
     * 信息
     */
    private final String des;

    /**
     * 后台优惠项
     */
    private final List<Integer> options;


    public static SettlementDiscountShowTypeEnum getEnumByOption(int option) {
        for (SettlementDiscountShowTypeEnum value : SettlementDiscountShowTypeEnum.values()) {
            if (value.getOptions().contains(option)) {
                return value;
            }
        }
        return null;
    }

    /**
     * todo 最终展示并不是按这个顺序，是按结算规则顺序
     * <p>
     * 倒排map <option,showType>
     *
     * @return
     */
    public static Map<Integer, SettlementDiscountShowTypeEnum> optionShowMap() {
        Map<Integer, SettlementDiscountShowTypeEnum> groupMap = new HashMap<>();
        for (SettlementDiscountShowTypeEnum value : SettlementDiscountShowTypeEnum.values()) {
            for (Integer valueOption : value.getOptions()) {
                groupMap.put(valueOption, value);
            }
        }
        return groupMap;
    }

    public static Map<Integer, Integer> optionShowCodeMap() {
        Map<Integer, Integer> groupMap = new HashMap<>();
        for (SettlementDiscountShowTypeEnum value : SettlementDiscountShowTypeEnum.values()) {
            for (Integer valueOption : value.getOptions()) {
                groupMap.put(valueOption, value.code);
            }
        }
        return groupMap;
    }

    /**
     * 获取当前同级option
     *
     * @param option 当前类型
     * @return
     */
    public static List<Integer> getSiblingOptions(Integer option) {
        for (SettlementDiscountShowTypeEnum value : SettlementDiscountShowTypeEnum.values()) {
            if (value.getOptions().contains(option)) {
                return value.getOptions();
            }
        }
        return Collections.emptyList();
    }
}
