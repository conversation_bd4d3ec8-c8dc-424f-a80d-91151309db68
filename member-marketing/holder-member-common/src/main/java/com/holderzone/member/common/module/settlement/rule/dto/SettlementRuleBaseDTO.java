package com.holderzone.member.common.module.settlement.rule.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.ChoinceEnum;
import com.holderzone.member.common.exception.MemberMarketingException;
import com.holderzone.member.common.module.settlement.constant.SettlementConstant;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 结算规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Accessors(chain = true)
public class SettlementRuleBaseDTO implements Serializable {


    private static final long serialVersionUID = -7093033769784127223L;

    /**
     * 主键
     */
    private String guid;

    /**
     * 是否更新
     */
    @JsonIgnore
    private Boolean isUpdate;
    /**
     * 主体
     */
    private String operSubjectGuid;

    /**
     * 名称
     */
    @NotEmpty
    @Size(message = "规则名称必须是{min}-{max}位", min = 1, max = 10)
    private String name;

    /**
     * 优惠券叠上限限制：0否 1是
     */
    @NotNull(message = "优惠券叠加使用上限必填")
    private Integer couponLimit;

    /**
     * 优惠券单笔限制张数
     */
    private Integer couponLimitNum;

    /**
     * 优惠券单笔限制张数(前端展示)
     */
    private Integer couponViewLimitNum;

    /**
     * 退款是否退优惠：0否 1是
     */
    private Integer couponRollback;

    /**
     * 核销顺序及规则：0自动 1手动
     */
    @NotNull(message = "计算顺序及规则必填")
    private Integer useType;


    /**
     * 业务：0全部 1部分 2无
     *
     * @see com.holderzone.member.common.enums.ChoinceEnum
     */
    private Integer applyBusiness;

    /**
     * 业务数组
     */
    private List<String> applyBusinessList;

    /**
     * 门店：0全部 1部分 2无
     *
     * @see com.holderzone.member.common.enums.ChoinceEnum
     */
    private Integer applicableAllStore;

    /**
     * 是否默认
     */
    private Integer isDefault;

    public void validate() {
        if (applyBusiness == ChoinceEnum.PART.getCode() && CollectionUtils.isEmpty(applyBusinessList)) {
            throw new MemberMarketingException("业务类型不能为空!");
        }
    }

    public String getName() {
        if (StringUtils.isBlank(name)){
            return name;
        }
        return name.trim();
    }

    /**
     * 优惠券最大可用数量
     *
     * @return 数量
     */
    public Integer couponLimitNum() {
        return couponLimit == BooleanEnum.FALSE.getCode() ?
                SettlementConstant.COUPON_LIMIT_NUM
                : couponLimitNum;
    }
}
