package com.holderzone.member.common.config.mybatis;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlScriptUtils;
import com.baomidou.mybatisplus.extension.injector.AbstractLogicMethod;
import com.holderzone.member.common.annotation.FunctionLabel;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * @date 2020/05/09 14:46
 * @description QueryByGuid-sql拼接
 */
public class LogicQueryByGuids extends AbstractLogicMethod {

    private static final String QUERY_MAPPER_METHOD = "queryByGuids";
    private static final String QUERY_GUID_NAME = "guid";
    private static final String QUERY_MAPPER_SQL = "<script>\nSELECT %s FROM %s WHERE %s IN (%s)\n</script>";


    public LogicQueryByGuids() {
    }

    @Override
    @FunctionLabel
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        SqlSource querySqlSource = this.languageDriver.createSqlSource(this.configuration,
                String.format(QUERY_MAPPER_SQL, this.sqlSelectColumns(tableInfo, false),
                        tableInfo.getTableName(), QUERY_GUID_NAME,
                        SqlScriptUtils.convertForeach("#{guid}", "guids", (String) null, "guid", ","),
                        tableInfo.getLogicDeleteSql(true, false)), modelClass);
        return this.addSelectMappedStatement(mapperClass, QUERY_MAPPER_METHOD, querySqlSource, modelClass, tableInfo);
    }
}
