package com.holderzone.member.common.module.settlement.rule.dto;

import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountItemEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 结算规则已选优惠项
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Accessors(chain = true)
public class SettlementSynDiscountDTO implements Serializable {

    /**
     * 优惠结算项
     */
    @ApiModelProperty(value = "结算优惠项guid,")
    private String guid;

    /**
     * 优惠guid
     */
    @ApiModelProperty(value = "优惠guid")
    private String discountGuid;

    /**
     * 优惠项大类：前端展示
     *
     * @see SettlementDiscountItemEnum
     */
    @ApiModelProperty(value = "优惠项： SettlementDiscountItemEnum")
    private Integer discountItem;

    /**
     * 优惠具体优惠项
     */
    @ApiModelProperty(value = "优惠具体优惠项： SettlementDiscountOptionEnum")
    private Integer discountOption;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer rank;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer discountNum;

    /**
     * 活动规则
     * 共享互斥关系 0-互斥 1-共享
     */
    @ApiModelProperty(value = "活动规则 共享互斥关系 0-互斥 1-共享")
    private Integer relationRule;
}
