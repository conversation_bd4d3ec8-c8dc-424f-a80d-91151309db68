package com.holderzone.member.common.enums.certificate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 老会员优惠券生效日期类型枚举
 */
@Getter
@AllArgsConstructor
public enum MemberCouponValidityTypeEnum {

    DEFINE_DAY(0, "已经定义了有效期"),

    COLLECT_DELAY_DATE(1, "领取后延多少天生效，有过期日期"),

    COLLECT_DELAY_DAY(2, "领取后延多少天生效，有过期天数"),

    OTHER(-1, "其他");

    private final Integer type;

    private final String name;


}
