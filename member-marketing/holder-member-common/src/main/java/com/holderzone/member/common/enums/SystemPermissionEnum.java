package com.holderzone.member.common.enums;

import org.springframework.util.StringUtils;

public enum SystemPermissionEnum {

    /**
     * 会员系统权限
     */
    MEMBER_PERMISSION(1, "sr_member_marketing"),

    /**
     * 营销中心权限
     */
    MARKETING_PERMISSION(2, "sr_marketing_count"),

    /**
     * 权益中心
     */
    EQUITIES_CENTER(3, "sr_member_rights"),

    /**
     * 会员商城
     */
    MEMBER_MALL(4, "sr_member_mall"),

    /**
     * 好搭档
     */
    MEMBER_PARTNER(5, "sr_member_partner"),

    /**
     * 小程序工具
     */
    MEMBER_TOOL(6, "sr_member_tool");


    /**
     * 权限来源
     */
    private final int code;

    /**
     * 系统层级权限唯一表示
     */
    private final String des;

    SystemPermissionEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public String getIdentByCode(int code) {
        SystemPermissionEnum[] permissionEnums = SystemPermissionEnum.values();
        for (SystemPermissionEnum permissionEnum : permissionEnums) {
            if (permissionEnum.getCode() == code) {
                return permissionEnum.getDes();
            }
        }
        return "";
    }

    public int getCodeByIdent(String identification) {
        if (StringUtils.isEmpty(identification)) {
            return -1;
        }
        SystemPermissionEnum[] permissionEnums = SystemPermissionEnum.values();
        for (SystemPermissionEnum permissionEnum : permissionEnums) {
            if (permissionEnum.getDes().equals(identification)) {
                return permissionEnum.getCode();
            }
        }
        return -1;
    }

    public String getDes() {
        return des;
    }

    public int getCode() {
        return code;
    }
}
