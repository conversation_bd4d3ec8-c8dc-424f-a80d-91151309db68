package com.holderzone.member.common.module.marketing.purchase.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <p>
 * 限量抢购活动商品
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Data
@Accessors(chain = true)
public class PurchaseApplyCommodityVo implements Serializable {


    private static final long serialVersionUID = 5973069689269929422L;

    /**
     * 限购活动id
     */
    @ApiModelProperty(value = "限购活动id")
    private String purchaseId;

    /**
     * 商品编码
     */
    private String commodityCode;


    /**
     * 限购数量,不小于0
     */
    private Integer limitNumber;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 订单时间
     */
    @ApiModelProperty("预定时间")
    private LocalDateTime reserveTime;

    /**
     * 限购数量
     */
    public void setLimitNumber(Integer limitNumber) {
        this.limitNumber = Objects.isNull(limitNumber) || limitNumber <= 0 ? 0 : limitNumber;
    }
}
