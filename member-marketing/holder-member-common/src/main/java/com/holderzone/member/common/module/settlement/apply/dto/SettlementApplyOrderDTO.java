package com.holderzone.member.common.module.settlement.apply.dto;

import cn.hutool.core.collection.CollUtil;
import com.beust.jcommander.internal.Lists;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyDiscountDetailVO;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyRuleVO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.module.settlement.rule.vo.SettlementRuleDiscountTreeVO;
import com.holderzone.member.common.module.settlement.rule.vo.SettlementRuleDiscountVO;
import com.holderzone.member.common.module.settlement.util.SettlementVerifyUtil;
import com.holderzone.member.common.util.settlement.SettlementBusinessUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 结算规则应用订单数据
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementApplyOrderDTO implements Serializable {


    private static final long serialVersionUID = 3212351245090146002L;
    /**
     * 自动勾选：0自动（计算最优） 1手动
     */
    @ApiModelProperty("订单相关入参")
    private Integer autoCheck;

    /**
     * 确认选择：0否（校验选择项是否是叠加） 1是
     */
    @ApiModelProperty("订单相关入参")
    private int confirmCheck;

    /**
     * 订单入参
     */
    @ApiModelProperty("订单相关入参")
    @NotNull(message = "订单入参必填！")
    private SettlementApplyOrderInfoDTO orderInfo;

    /**
     * 订单商品
     * 商品需要合并成 id x 数量
     */
    @ApiModelProperty("购物车商品明细")
    private List<SettlementApplyCommodityDTO> orderCommodityList;

    /**
     * 已选优惠： 计算使用
     * 按选择顺序传入，保留当前优惠（当前可叠加时，同时保留其他可叠加项）
     */
    @ApiModelProperty("已选优惠")
    private List<SettlementApplyDiscountBaseReqDTO> checkDiscountList;

    /**
     * 已选优惠同上  数据结构：map<discountGuid,SettlementApplyDiscountBaseReqDTO>
     */
    @ApiModelProperty("已选优惠")
    private Map<String, SettlementApplyDiscountBaseReqDTO> checkDiscountMap;

    /**
     * 指定优惠唯一标识查询  如guid
     */
    private List<String> discountOptionId;

    /**
     * 指定优惠项查询
     */
    private List<Integer> listOptions;

    /**
     * 叠加优惠券
     * map<discountOption,set<discountGuid>>
     */
    private Map<Integer, Set<String>> appendMap;

    /**
     * 优惠券限制数量大于1的规则
     */
    private Map<String, Integer> couponLimtNumberMap;

    /**
     * 是否需要指定优惠查询
     */
    private Integer isAppointDiscount = BooleanEnum.FALSE.getCode();

    /**
     * 筛选出来指定可叠加的活动
     */
    private List<String> appendDiscountOptionGuid;

    /**
     * 校验入参
     */
    public void validate() {
        SettlementVerifyUtil.isNull(orderInfo, "订单信息必传");
        orderInfo.validate();
        //传了商品
        if (!CollectionUtils.isEmpty(orderCommodityList)) {
            //校验字段
            orderCommodityList.forEach(SettlementApplyCommodityDTO::validate);

            //检查并设置商品rid
            SettlementBusinessUtil.ensureRidValues(orderCommodityList);
        }
    }

    /**
     * 校验选中参数
     */
    public void validateCheck() {
        SettlementVerifyUtil.isEmpty(checkDiscountList, "优惠项必传！");

        //检查并设置商品rid
        SettlementBusinessUtil.ensureRidValues(orderCommodityList);
    }

    /**
     * 处理重新计算
     */
    public void removeCardDiscount() {
        //移除卡权益：剩下的重新再算一次
        this.getCheckDiscountList().removeIf(c -> c.getDiscountOption() == SettlementDiscountOptionEnum.MEMBER_CARD_DISCOUNT.getCode());
    }

    /**
     * 锁定操作
     */
    public void locked() {
        //只计算
        this.getOrderInfo().setIsOfferLocked(BooleanEnum.TRUE.getCode());
        //手动
        this.setAutoCheck(BooleanEnum.TRUE.getCode());
        //跳过叠加校验，直接来
        this.setConfirmCheck(BooleanEnum.TRUE.getCode());
    }

    /**
     * 计算商品总金额
     */
    public void handlerParam() {
        //主体
        this.orderInfo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        if (CollectionUtils.isEmpty(orderCommodityList)) {
            //todo 商品为空时，用前端传回的累计金额
            this.orderInfo.setCommodityTotalAmount(Optional.ofNullable(orderInfo.getCommodityTotalAmount()).orElse(BigDecimal.ZERO));
        } else {
            for (int i = 0; i < orderCommodityList.size(); i++) {
                final SettlementApplyCommodityDTO c = orderCommodityList.get(i);
                c.setCommodityTotalPrice(c.getCommodityNum().multiply(c.getCommodityPrice()));
                if (StringUtils.isEmpty(c.getRid())) {
                    //默认给他下标
                    c.setRid(String.valueOf(i));
                }
            }
            //累加 单价x数量
            final BigDecimal commodityTotalAmount = orderCommodityList.stream()
                    .map(SettlementApplyCommodityDTO::getCommodityTotalPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            this.orderInfo.setCommodityTotalAmount(commodityTotalAmount);
        }
        //合并选中参数
        mergeCheckDiscountOptionId();
    }

    /**
     * 计算实时商品金额
     *
     * @return 实时金额
     */
    public BigDecimal calculateCommodityTotalAmount() {
        return orderCommodityList.stream()
                .map(c -> c.getCommodityTotalPrice().subtract(c.getDiscountFee()))
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 合并选中项的discountOptionIds
     */
    private void mergeCheckDiscountOptionId() {
        if (CollectionUtils.isEmpty(checkDiscountList)) {
            return;
        }
        if (checkDiscountList.size() == 1) {
            final SettlementApplyDiscountBaseReqDTO baseReqDTO = checkDiscountList.get(0);
            baseReqDTO.setDiscountOptionIds(Collections.singletonList(baseReqDTO.getDiscountOptionId()));
            List<ExchangeApplyCouponOrderDTO> applyCouponOrderDTOS = getExchangeApplyCouponOrderDTOS(baseReqDTO);
            baseReqDTO.setExchangeApplyCouponOrderDTOS(applyCouponOrderDTOS);
            return;
        }
        Map<String, SettlementApplyDiscountBaseReqDTO> checkDiscountMap = new LinkedHashMap<>();
        for (SettlementApplyDiscountBaseReqDTO checkDiscount : checkDiscountList) {
            //组合类型
            final String key = checkDiscount.getDiscountOption() + StringConstant.COLON + checkDiscount.getDiscountGuid();
            SettlementApplyDiscountBaseReqDTO baseReqDTO = checkDiscountMap.get(key);
            putCheckDiscountMap(checkDiscountMap, checkDiscount, key, baseReqDTO);
        }
        this.checkDiscountList = new ArrayList<>(checkDiscountMap.values());
    }

    /**
     * 填充选中优惠map
     *
     * @param checkDiscountMap 选中优惠map
     * @param checkDiscount    选中项
     * @param key              主键
     * @param baseReqDTO       请求优惠
     */
    private void putCheckDiscountMap(Map<String, SettlementApplyDiscountBaseReqDTO> checkDiscountMap,
                                     SettlementApplyDiscountBaseReqDTO checkDiscount,
                                     String key,
                                     SettlementApplyDiscountBaseReqDTO baseReqDTO) {
        if (baseReqDTO == null) {
            checkDiscount.setDiscountOptionIds(Lists.newArrayList(checkDiscount.getDiscountOptionId()));
            List<ExchangeApplyCouponOrderDTO> applyCouponOrderDTOS = getExchangeApplyCouponOrderDTOS(checkDiscount);
            checkDiscount.setExchangeApplyCouponOrderDTOS(applyCouponOrderDTOS);
            checkDiscountMap.put(key, checkDiscount);
            return;
        }
        //合并optionId
        baseReqDTO.getDiscountOptionIds().add(checkDiscount.getDiscountOptionId());
        if (Objects.nonNull(checkDiscount.getRequiresTimes())) {
            ExchangeApplyCouponOrderDTO exchangeApplyCouponOrderDTO = new ExchangeApplyCouponOrderDTO();
            exchangeApplyCouponOrderDTO.setRequiresTimes(checkDiscount.getRequiresTimes());
            exchangeApplyCouponOrderDTO.setDiscountOptionIds(checkDiscount.getDiscountOptionId());
            baseReqDTO.getExchangeApplyCouponOrderDTOS().add(exchangeApplyCouponOrderDTO);
        }
    }

    private static List<ExchangeApplyCouponOrderDTO> getExchangeApplyCouponOrderDTOS(SettlementApplyDiscountBaseReqDTO checkDiscount) {
        List<ExchangeApplyCouponOrderDTO> applyCouponOrderDTOS = Lists.newArrayList();
        if (Objects.nonNull(checkDiscount.getRequiresTimes())) {
            ExchangeApplyCouponOrderDTO exchangeApplyCouponOrderDTO = new ExchangeApplyCouponOrderDTO();
            exchangeApplyCouponOrderDTO.setRequiresTimes(checkDiscount.getRequiresTimes());
            exchangeApplyCouponOrderDTO.setDiscountOptionIds(checkDiscount.getDiscountOptionId());
            applyCouponOrderDTOS.add(exchangeApplyCouponOrderDTO);
        }
        return applyCouponOrderDTOS;
    }

    /**
     * 优惠清零
     */
    public void clearCommodityFee() {

        if (!CollectionUtils.isEmpty(orderCommodityList)) {
            orderCommodityList.forEach(c -> {
                c.setDiscountFee(BigDecimal.ZERO);
                if (Objects.nonNull(c.getAfterDiscountTotalPrice())) {
                    c.setAfterDiscountTotalPrice(null);
                    c.setExchangeDiscountNum(BigDecimal.ZERO);
                    c.setTimeLimitNum(BigDecimal.ZERO);
                    c.setTimeLimitPrice(null);
                }
            });
        }
        if (!CollectionUtils.isEmpty(appendDiscountOptionGuid)) {
            appendDiscountOptionGuid = new ArrayList<>();
        }

        if (CollUtil.isNotEmpty(appendDiscountOptionGuid)) {
            appendDiscountOptionGuid.clear();
        }
    }

    /**
     * 填充规则入参
     *
     * @param applyRule 结算规则
     */
    public void fillApplyRule(SettlementApplyRuleVO applyRule) {
        if (applyRule.getBaseRule().getGuid().equals(StringConstant.PARENT)) {
            //无规则
            this.orderInfo.setCouponLimitNum(1);
            this.appendMap = new HashMap<>();
            this.couponLimtNumberMap = new HashMap<>();
            return;
        }

        //零售渠道
        if (orderInfo.getTerminal().equals("pos")) {
            this.orderInfo.setStoreGuidList(Collections.singletonList(this.orderInfo.getStoreGuid()));

            this.getOrderCommodityList().forEach(
                    c -> c.setStoreGuid(this.orderInfo.getStoreGuid())
            );
        }

        //私域商城渠道
        if (!orderInfo.getTerminal().equals("pos")) {
            this.getOrderCommodityList().forEach(
                    c -> c.setDiscountTotalPriceInShopCar(c.getCommodityTotalPrice())
            );
        }

        this.appendMap = newAppendMap();
        //叠加key
        applyRule.getAppendDiscounts().stream()
                .collect(Collectors.groupingBy(SettlementRuleDiscountTreeVO::getDiscountOption))
                .forEach((k, v) -> {
                    LinkedHashSet<String> linkedHashSet = new LinkedHashSet<>(v.stream().map(SettlementRuleDiscountVO::getDiscountGuid).collect(Collectors.toList()));
                    this.getAppendMap().put(k, linkedHashSet);
                });
        //优惠券订单限制数量
        this.getOrderInfo().setCouponLimitNum(applyRule.getBaseRule().couponLimitNum());
        //优惠券限制数量 > 1 的更新，其他的默认1
        this.setCouponLimtNumberMap(applyRule.getCouponLimtNumberMap());
        if (!couponLimtNumberMap.isEmpty() && Objects.nonNull(checkDiscountList)) {
            checkDiscountList.stream()
                    .filter(d -> SettlementDiscountOptionEnum.isCoupon(d.getDiscountOption()))
                    .forEach(d -> {
                        final String key = SettlementDiscountOptionEnum.optionMapKey(d.getDiscountOption(), d.getDiscountGuid());
                        //优惠券限制数量
                        Optional.ofNullable(couponLimtNumberMap.get(key)).ifPresent(d::setCouponLimitNum);
                    });
        }

        mergeCheckDiscountOptionId();
        setDiscountMap();
    }

    private HashMap<Integer, Set<String>> newAppendMap() {

        return new HashMap<>();
    }

    public void setDiscountMap() {

        if (CollUtil.isNotEmpty(this.checkDiscountList)) {
            this.setCheckDiscountMap(this.checkDiscountList
                    .stream()
                    .collect(Collectors.toMap(SettlementApplyDiscountBaseReqDTO::getDiscountGuid, Function.identity(), (entity1, entity2) -> entity1)));
        }

    }

    /**
     * 处理外部优惠金额
     */
    public void handlerDiscountParam() {
        if (CollUtil.isNotEmpty(orderCommodityList)) {
            for (SettlementApplyCommodityDTO settlementApplyCommodityDTO : orderCommodityList) {
                if (Objects.nonNull(settlementApplyCommodityDTO.getDiscountPriceInShopCar())) {
                    BigDecimal discountTotalPrice = settlementApplyCommodityDTO.getCommodityTotalPrice()
                            .subtract(settlementApplyCommodityDTO.getDiscountPriceInShopCar());
                    settlementApplyCommodityDTO.setDiscountFee(discountTotalPrice);
                }
            }
        }
    }

    public boolean isCheckShare(String discountGuid, Integer code) {
        final Set<String> apendSet = this.appendMap.get(code);
        return CollUtil.isNotEmpty(apendSet) && apendSet.contains(discountGuid);
    }
}
