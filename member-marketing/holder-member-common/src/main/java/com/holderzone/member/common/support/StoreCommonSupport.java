package com.holderzone.member.common.support;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.RedisKeyConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.SqlConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.QueryStoreBasePage;
import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.sale.SaleStatusEnum;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.module.base.store.dto.SyncStoreDTO;
import com.holderzone.member.common.module.base.store.dto.SyncStoreQo;
import com.holderzone.member.common.qo.tool.StoreByIdQO;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.common.vo.equities.ApplyModuleVO;
import com.holderzone.member.common.vo.equities.ApplyTypeVO;
import jodd.util.StringUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 门店业务支持类
 *
 * <AUTHOR>
 * @date 2023/9/5
 **/
@Component
public class StoreCommonSupport {

    @Resource
    MemberBaseFeign baseFeign;

    @Resource
    CrmFeign crmFeign;

    @Resource
    RedisTemplate<String, Object> redisTemplate;

    @Resource
    private ExternalSupport externalSupport;

    /**
     * 是否存在关键字的业务
     *
     * @param keywords
     * @return
     */
    public List<String> getApplyBusiness(String keywords) {
        if (StringUtil.isBlank(keywords)) {
            return Collections.emptyList();
        }
        final List<ApplyTypeVO> applyBusinessList = getApplyBusinessList();
        if (CollectionUtils.isEmpty(applyBusinessList)) {
            return Collections.emptyList();
        }
        return applyBusinessList.stream()
                .filter(v -> v.getTypeName().contains(keywords))
                .map(ApplyTypeVO::getType)
                .collect(Collectors.toList());
    }

    /**
     * 所有业务
     *
     * @return
     */
    public List<String> getAllApplyBusiness() {
        List<ApplyTypeVO> applyBusinessList = externalSupport.baseServer(ThreadLocalCache.getSystem()).getApplyBusiness();

        if (CollectionUtils.isEmpty(applyBusinessList)) {
            return Collections.emptyList();
        }
        return applyBusinessList.stream()
                .map(ApplyTypeVO::getType)
                .collect(Collectors.toList());
    }

    /**
     * 转业务字符串
     *
     * @param business 业务数组
     * @return
     */
    public String getBusinessStr(String business) {
        final List<ApplyTypeVO> applyBusinessList = getApplyBusinessList();
        if (CollectionUtils.isEmpty(applyBusinessList)) {
            return "";
        }
        return applyBusinessList.stream()
                .filter(b -> business.contains(b.getType()))
                .findFirst()
                .map(ApplyTypeVO::getTypeName)
                .orElse("");
    }

    /**
     * 转业务字符串
     *
     * @param businessList 业务数组
     * @return
     */
    public String getApplyBusinessStr(List<String> businessList) {
        final List<ApplyTypeVO> applyBusinessList = getAllApplyBusinessList();
        if (CollectionUtils.isEmpty(applyBusinessList)) {
            return "";
        }
        return applyBusinessList.stream()
                .filter(b -> businessList.contains(b.getType()))
                .map(ApplyTypeVO::getTypeName)
                .collect(Collectors.joining(","));
    }

    /**
     * 转终端字符串
     *
     * @param terminalList 终端数据
     * @return
     */
    public String getApplyTerminalStr(List<String> terminalList) {
        final List<ApplyTypeVO> applyTerminalList = getApplyTerminalList();
        if (CollectionUtils.isEmpty(applyTerminalList)) {
            return "";
        }
        return applyTerminalList.stream()
                .filter(t -> terminalList.contains(t.getType()))
                .map(ApplyTypeVO::getTypeName)
                .collect(Collectors.joining(","));
    }

    /**
     * 业务终端
     *
     * @return
     */
    public List<ApplyTypeVO> getApplyTerminalList() {
        final String key = RedisKeyConstant.APPLY_TERMINAL_LIST + ThreadLocalCache.getOperSubjectGuid();
        final List<ApplyTypeVO> voList = Optional.ofNullable(redisTemplate.opsForValue().get(key))
                .map(json -> JacksonUtils.toObjectList(ApplyTypeVO.class, json.toString()))
                .orElse(Collections.emptyList());
        if (!voList.isEmpty()) {
            return voList;
        }
        List<ApplyTypeVO> applyTypeVOS = externalSupport.baseServer(ThreadLocalCache.getSystem(), ThreadLocalCache.getOperSubjectGuid()).getAllApplyTerminal();
        redisTemplate.opsForValue().set(key, JacksonUtils.writeValueAsString(applyTypeVOS));
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
        return applyTypeVOS;
    }

    /**
     * 业务类型
     *
     * @return
     */
    public List<ApplyTypeVO> getApplyBusinessList() {
        final String key = RedisKeyConstant.APPLY_BUSINESS_LIST + ThreadLocalCache.getOperSubjectGuid();
        final List<ApplyTypeVO> voList = Optional.ofNullable(redisTemplate.opsForValue().get(key))
                .map(json -> JacksonUtils.toObjectList(ApplyTypeVO.class, json.toString()))
                .orElse(Collections.emptyList());
        if (!voList.isEmpty()) {
            return voList;
        }
        List<ApplyTypeVO> applyTypeVOS = externalSupport.baseServer(ThreadLocalCache.getSystem(), ThreadLocalCache.getOperSubjectGuid()).getApplyBusiness();

        redisTemplate.opsForValue().set(key, JacksonUtils.writeValueAsString(applyTypeVOS));
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
        return applyTypeVOS;
    }

    /**
     * 业务类型
     *
     * @return
     */
    public List<ApplyTypeVO> getAllApplyBusinessList() {
        final String key = RedisKeyConstant.ALL_APPLY_BUSINESS_LIST + ThreadLocalCache.getOperSubjectGuid();
        final List<ApplyTypeVO> voList = Optional.ofNullable(redisTemplate.opsForValue().get(key))
                .map(json -> JacksonUtils.toObjectList(ApplyTypeVO.class, json.toString()))
                .orElse(Collections.emptyList());
        if (!voList.isEmpty()) {
            return voList;
        }
        List<ApplyTypeVO> applyTypeVOS = externalSupport.baseServer(ThreadLocalCache.getSystem(), ThreadLocalCache.getOperSubjectGuid()).getAllApplyBusiness();

        redisTemplate.opsForValue().set(key, JacksonUtils.writeValueAsString(applyTypeVOS));
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
        return applyTypeVOS;
    }

    /**
     * 关键字搜索门店
     *
     * @param keywords 门店id、门店名称
     * @return
     */
    public List<String> getStoreIds(String keywords) {
        if (StringUtil.isBlank(keywords)) {
            return Collections.emptyList();
        }
        List<SyncStoreDTO> storeList = getStoreList(keywords);
        return Optional.ofNullable(storeList)
                .map(list ->
                        list.stream().map(SyncStoreDTO::getId).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    /**
     * 所有门店
     *
     * @return
     */
    public List<String> getAllStoreIds() {
        List<SyncStoreDTO> storeList = getStoreList("", Collections.emptyList());
        return Optional.ofNullable(storeList)
                .map(list ->
                        list.stream().map(SyncStoreDTO::getId).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }


    /**
     * 根据系统获取门店列表
     * @param keywords 关键字
     * @param storeIds 门店id
     * @return 门店列表
     */
    public List<SyncStoreDTO> getStoreListBySystem(String keywords, Map<String, Set<String>> storeIds) {
        SyncStoreQo storeQo = new SyncStoreQo();
        storeQo.defaultOperSubjectGuid();
        storeQo.setName(keywords);
        List<SyncStoreDTO> storeList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(storeIds)) {
            for (Map.Entry<String, Set<String>> storeIdsMap : storeIds.entrySet()) {
                StoreByIdQO storageByIdQuery = new StoreByIdQO();
                storageByIdQuery.toLongList(storeIdsMap.getValue().stream()
                        .filter(item -> item != null && !item.isEmpty())
                        .collect(Collectors.toList()));
                Object object = externalSupport.storeServer(SystemEnum.getSystemCodeBySystemName(storeIdsMap.getKey())).getStoreById(storageByIdQuery);
                List<StoreBaseInfo> storeBaseInfoList;
                if (object instanceof List) {
                    storeBaseInfoList = JSON.parseArray(JSON.toJSONString(object),
                            StoreBaseInfo.class);
                } else {
                    // 单个对象转为列表
                    StoreBaseInfo singleStore = JSON.parseObject(JSON.toJSONString(object),
                            StoreBaseInfo.class);
                    storeBaseInfoList = Lists.newArrayList(singleStore);
                }

                if (CollUtil.isEmpty(storeBaseInfoList)) {
                    continue;
                }
                int beforeSize = storeList.size();
                addStoreList(storeBaseInfoList, storeList);
                // 只设置当前循环新添加的门店的系统来源
                for (int i = beforeSize; i < storeList.size(); i++) {
                    storeList.get(i).setSystem(storeIdsMap.getKey());
                }
            }
        }
        return Optional.of(storeList).orElse(Collections.emptyList());
    }

    /**
     * 门店数组
     *
     * @param keywords
     * @param storeIds
     * @return
     */
    public List<SyncStoreDTO> getStoreList(String keywords, List<String> storeIds) {
        SyncStoreQo storeQo = new SyncStoreQo();
        storeQo.defaultOperSubjectGuid();
        storeQo.setName(keywords);
        List<SyncStoreDTO> storeList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(storeIds)) {
            StoreByIdQO storageByIdQuery = new StoreByIdQO();
            storageByIdQuery.toLongList(storeIds);
            Object object = externalSupport.storeServer(ThreadLocalCache.getSystem()).getStoreById(storageByIdQuery);
            List<StoreBaseInfo> storeBaseInfoList = JSON.parseArray(JSON.toJSONString(object), StoreBaseInfo.class);

            if (CollUtil.isEmpty(storeBaseInfoList)) {
                return storeList;
            }
            addStoreList(storeBaseInfoList, storeList);
            return Optional.of(storeList).orElse(Collections.emptyList());
        } else {
            List<StoreBaseInfo> dataList = externalSupport.storeServer(ThreadLocalCache.getSystem()).listStore(new QueryStoreBasePage());
            if (CollUtil.isEmpty(dataList)) {
                return storeList;
            }
            addStoreList(dataList, storeList);
            return storeList;
        }
    }

    private static void addStoreList(List<StoreBaseInfo> dataList, List<SyncStoreDTO> storeList) {
        for (StoreBaseInfo storeBaseInfo : dataList) {
            SyncStoreDTO storeDTO = new SyncStoreDTO();
            storeDTO.setStoreGuid(storeBaseInfo.getStoreGuid());
            storeDTO.setId(storeBaseInfo.getId());
            storeDTO.setStoreName(storeBaseInfo.getStoreName());
            storeDTO.setStoreNumber(storeBaseInfo.getId());
            storeDTO.setStoreTeamInfoId(storeBaseInfo.getStoreTeamInfoId());
            storeList.add(storeDTO);
        }
    }

    /**
     * 门店数组
     *
     * @param storeIds 门店数组
     * @return
     */
    public List<SyncStoreDTO> getAllStoreList(List<String> storeIds) {
        SyncStoreQo syncStoreQo = new SyncStoreQo();
        syncStoreQo.defaultOperSubjectGuid();
        //启用、禁用
        syncStoreQo.setStatus(SaleStatusEnum.allDes());
        syncStoreQo.setStoreIds(storeIds);
        syncStoreQo.setPageSize(SqlConstant.MAX_PAGE_SIZE);

        final Result<PageResult<SyncStoreDTO>> pageSyncStore = baseFeign.pageSyncStore(syncStoreQo);
        return Optional.ofNullable(pageSyncStore.getData())
                .map(PageResult::getRecords)
                .orElse(Collections.emptyList());
    }

    /**
     * 关键字搜索门店
     *
     * @param keywords
     * @return
     */
    public List<SyncStoreDTO> getStoreList(String keywords) {
        return getStoreList(keywords, Collections.emptyList());
    }

    /**
     * ids查询门店
     *
     * @param storeIds
     * @return
     */
    public List<SyncStoreDTO> getStoreList(List<String> storeIds) {
        return getStoreList("", storeIds);
    }

    /**
     * 门店转换为map
     *
     * @param storeIds
     * @return
     */
    public Map<String, SyncStoreDTO> mapStore(List<String> storeIds) {
        final List<SyncStoreDTO> storeList = getStoreList(storeIds);
        if (CollectionUtils.isEmpty(storeList)) {
            return Collections.emptyMap();
        }
        return storeList.stream().collect(Collectors.toMap(SyncStoreDTO::getStoreTeamInfoId, s -> s, (v1, v2) -> v2));
    }
}
