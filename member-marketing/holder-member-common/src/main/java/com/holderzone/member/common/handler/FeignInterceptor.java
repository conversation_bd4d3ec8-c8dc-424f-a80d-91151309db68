package com.holderzone.member.common.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.util.TraceidUtils;
import com.holderzone.member.common.config.IPaasSecretConfig;
import com.holderzone.member.common.config.MallSecretConfig;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.util.HttpsClientUtils;
import com.holderzone.member.common.util.ServletUtils;
import com.holderzone.member.common.util.verify.ObjectUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class FeignInterceptor implements RequestInterceptor {

    @Resource
    private IPaasSecretConfig iPaasSecretConfig;

    @Resource
    private StringRedisTemplate stringredisTemplate;

    /**
     * 微信accessToken缓存key
     */
    private static final String I_PAAS_TOKEN_KEY = "iPaasMemberToken:";

    @Override
public void apply(RequestTemplate template) {
    addTraceIdHeader(template);

    String targetName = template.feignTarget().name();
    if (handleSpecialTargets(template, targetName)) {
        return;
    }

    if (FilterConstant.MARKET.equals(targetName)) {
        addMarketHeaders(template);
    }

    handleRequestAttributes(template, targetName);
    handleOdooEnterprise(template, targetName);
    addSystemHeader(template);

    log.info("feign拦截器传递头部参数：header={}", template.headers());
    addThreadLocalHeaders(template);
}

private void addTraceIdHeader(RequestTemplate template) {
    String traceid = TraceidUtils.getTraceid();
    if (StringUtils.hasText(traceid)) {
        template.header("traceId", traceid);
    }
}

private boolean handleSpecialTargets(RequestTemplate template, String targetName) {
    if (FilterConstant.I_PAAS.equals(targetName)) {
        handlerPassHeader(template);
        return true;
    }
    if (FilterConstant.SALES.equals(targetName)) {
        handlerSalesHeader(template);
        return true;
    }
    if (FilterConstant.S2B2C_MALL.equals(targetName)) {
        handlerS2b2cMallHeader(template);
        return true;
    }
    return false;
}

private void addMarketHeaders(RequestTemplate template) {
    String accessToken = getAccessToken();
    template.header(StringConstant.AUTHORIZATION, StringConstant.HEADER_BEARER + accessToken);
}

    private void handleRequestAttributes(RequestTemplate template, String targetName) {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (Objects.nonNull(attributes)) {
            ServletRequestAttributes servletAttributes = (ServletRequestAttributes) attributes;
            addTokenHeaders(template, targetName, servletAttributes);
            addOperSubjectHeaders(template, servletAttributes);
            String sourceHeader = ServletUtils.getHeader(FilterConstant.SOURCE);
            final int source = (sourceHeader == null || "null".equals(sourceHeader))
                    ? SourceTypeEnum.ADD_BACKGROUND.getCode() : ObjectUtil.objToInt(sourceHeader);
            template.header(FilterConstant.SOURCE, source + "");
            template.header(FilterConstant.HAS_RETURN_VALUE, BooleanEnum.TRUE.getCode() + "");
            putSassHeader(template);
        } else {
            HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
            template.header(FilterConstant.TOKEN, headerUserInfo.getToken());
            template.header(FilterConstant.ENTERPRISE_GUID, ThreadLocalCache.getEnterpriseGuid());
            Integer sourceHeader = headerUserInfo.getSource();
            final int source = Objects.isNull(sourceHeader)
                    ? SourceTypeEnum.ADD_BACKGROUND.getCode() : sourceHeader;
            template.header(FilterConstant.SOURCE, source + "");
            template.header(FilterConstant.HAS_RETURN_VALUE, BooleanEnum.TRUE.getCode() + "");
            putSassHeader(template);
        }
    }

private void addTokenHeaders(RequestTemplate template, String targetName, ServletRequestAttributes attributes) {
    final String token = attributes.getRequest().getHeader(FilterConstant.TOKEN);
    if (StringUtil.isNotEmpty(token)) {
        if (FilterConstant.FEIGN_HOLDER.equals(targetName)) {
            template.header(FilterConstant.LOGIN_TOKEN, token);
        } else {
            template.header(FilterConstant.TOKEN, token);
            template.header(FilterConstant.ENTERPRISE_GUID, ThreadLocalCache.getEnterpriseGuid());
        }
    }
}

private void addOperSubjectHeaders(RequestTemplate template, ServletRequestAttributes attributes) {
    final String operSubjectGuid = attributes.getRequest().getHeader(FilterConstant.OPER_SUBJECT_GUID);
    template.header(FilterConstant.OPER_SUBJECT_GUID, operSubjectGuid);
}

private void handleOdooEnterprise(RequestTemplate template, String targetName) {
    if (FilterConstant.FEIGN_ODOO.equals(targetName)) {
        template.header(FilterConstant.ODOO_ENTERPRISE_GUID, ThreadLocalCache.getEnterpriseGuid());
        template.removeHeader(FilterConstant.SOURCE);
        template.header(FilterConstant.SOURCE, "2");
    }
}

private void addSystemHeader(RequestTemplate template) {
    String system = getSystem();
    if (!StringUtils.isEmpty(system)) {
        template.header(FilterConstant.SYSTEM, system);
    }
}

private void addThreadLocalHeaders(RequestTemplate template) {
    try {
        if (ThreadLocalCache.get() == null) {
            return;
        }
        template.feignTarget().url();
        template.header(FilterConstant.USER_INFO, URLEncoder.encode(ThreadLocalCache.get(), "utf-8"));

        if (StringUtils.hasText(ThreadLocalCache.getOperSubjectGuid())) {
            template.header(FilterConstant.OPER_SUBJECT_GUID, ThreadLocalCache.getOperSubjectGuid());
        }
        if (StringUtils.hasText(ThreadLocalCache.getEnterpriseGuid())) {
            template.header(FilterConstant.ENTERPRISE_GUID, ThreadLocalCache.getEnterpriseGuid());
        }
        log.info("feign拦截器传递参数：header={}", template.headers());
    } catch (UnsupportedEncodingException e) {
        log.error("UnsupportedEncodingException", e);
    }
}

    private void handlerS2b2cMallHeader(RequestTemplate template) {

        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();

        template.header(FilterConstant.MALL_ENTERPRISE_GUID, headerUserInfo.getEnterpriseGuid());

        template.header(FilterConstant.MALL_PROVIDER_ID, headerUserInfo.getOperSubjectGuid());

        String system = getSystem();

        template.header(FilterConstant.SYSTEM, system);

        log.info("feign请求Mall拦截器传递参数：header={}", template.headers());
    }

    private void handlerSalesHeader(RequestTemplate template) {
        String accessToken = getAccessToken();
        template.header(StringConstant.AUTHORIZATION, StringConstant.HEADER_BEARER + accessToken);

        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        template.header(FilterConstant.COMPANY_ID, headerUserInfo.getEnterpriseGuid());

        template.header(FilterConstant.USER_ID, headerUserInfo.getUserGuid());

        template.header(FilterConstant.ACCOUNT, headerUserInfo.getTel());

        String system = getSystem();

        template.header(FilterConstant.SYSTEM, system);

        log.info("feign请求SALES拦截器传递参数：header={}", template.headers());
    }

    private void handlerPassHeader(RequestTemplate template) {
        String accessToken = getAccessToken();
        template.header(StringConstant.AUTHORIZATION, StringConstant.HEADER_BEARER + accessToken);
        if (RequestContextHolder.getRequestAttributes() != null) {
            final String token = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest().getHeader(FilterConstant.TOKEN);
            template.header(FilterConstant.TOKEN, token);
        }
        template.header(FilterConstant.ENTERPRISE_GUID, ThreadLocalCache.getEnterpriseGuid());

        template.header(FilterConstant.COMPANY_ID, ThreadLocalCache.getEnterpriseGuid());

        String system = getSystem();

        template.header(FilterConstant.SYSTEM, system);
    }

    private String getAccessToken() {
        String accessToken = stringredisTemplate.opsForValue().get(I_PAAS_TOKEN_KEY);
        if (StringUtils.isEmpty(accessToken)) {
            Map<String, String> headerMap = Maps.newHashMap();
            String originalBytes = iPaasSecretConfig.getConsumerKey() + ":" + iPaasSecretConfig.getConsumerSecret();
            String data = StringConstant.HEADER_BASIC + Base64.getEncoder().encodeToString(originalBytes.getBytes(StandardCharsets.UTF_8));
            headerMap.put(StringConstant.AUTHORIZATION, data);
            headerMap.put(StringConstant.STR_CONTENT_TYPE, StringConstant.I_PASS_CONTENT_TYPE);
            String jsonStr = HttpsClientUtils.doPost(iPaasSecretConfig.getUrl(), StringConstant.CLIENT_CREDENTIALS, headerMap);
            log.info("get iPaas access token:{} ", jsonStr);
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            accessToken = jsonObject.get("access_token").toString();
            stringredisTemplate.opsForValue().set(I_PAAS_TOKEN_KEY, accessToken, 1, TimeUnit.HOURS);
        }
        return accessToken;
    }

    private static String getSystem() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();

        if (Objects.nonNull(attributes)) {
            String system = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                    .getRequest().getHeader(FilterConstant.SYSTEM);


            if (StringUtils.isEmpty(system)) {
                system = ThreadLocalCache.getSystem() + "";
            }

            log.info("handlerPassHeader：system={}", system);
            return system;
        }
        return null;
    }

    /**
     * 老门店header
     *
     * @param template 请求
     */
    private void putSassHeader(RequestTemplate template) {
        final HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        //老门店
        if (FilterConstant.FEIGN_SAAS.equals(template.feignTarget().name())) {
            //登录类型
            Optional.ofNullable(headerUserInfo.getLoginType()).ifPresent(loginType -> {
                template.header(FilterConstant.LOGIN_TYPE, loginType);
            });

            //终端
            Optional.ofNullable(headerUserInfo.getTerminalCode()).ifPresent(terminalCode -> {
                template.header(FilterConstant.TERMINAL_CODE, terminalCode);
            });

            //菜单
            Optional.ofNullable(headerUserInfo.getMenuGuid()).ifPresent(menuGuid -> {
                //todo 菜单是动态的，需要在老门店把相应接口配置在这个菜单下面
                template.header(FilterConstant.MENU_GUID, menuGuid);
            });

            //企业
            Optional.ofNullable(headerUserInfo.getEnterpriseGuid()).ifPresent(enterpriseGuid -> {
                template.header(FilterConstant.ENTERPRISE_GUID, enterpriseGuid);
            });

            //运营主体
            Optional.ofNullable(headerUserInfo.getOperSubjectGuid()).ifPresent(operSubjectGuid -> {
                template.removeHeader(FilterConstant.OPER_SUBJECT_GUID);
                template.header(FilterConstant.OPER_SUBJECT_GUID, operSubjectGuid);
            });

            // token
            String system = getSystem();
            if (Objects.nonNull(system) && !system.equals(String.valueOf(SystemEnum.REPAST.getCode()))) {
                // 如果不是餐饮云进入，则不校验token
                template.header(FilterConstant.IS_CHECK_TOKEN, BooleanEnum.FALSE.getDes());
            }
        }
    }
}
