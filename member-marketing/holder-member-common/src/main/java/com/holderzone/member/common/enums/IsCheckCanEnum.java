package com.holderzone.member.common.enums;

import lombok.Getter;
import org.springframework.util.StringUtils;

@Getter
public enum IsCheckCanEnum {

    //已勾选
    ALREADY_CHECK(0,"already_check"),

    //可勾选
    CAN_CHECK(1,"can_check"),

    //不可勾选
    NOT_CAN_CHECK(2,"NOT_CAN_CHECK");;

    //权限类型
    private final int code;

    private final String des;

    IsCheckCanEnum(int code, String des){
        this.code = code;
        this.des = des;
    }

    public String getMessageByType(int code){
        IsCheckCanEnum[] permissionEnums = IsCheckCanEnum.values();
        for (IsCheckCanEnum permissionEnum : permissionEnums) {
            if(permissionEnum.getCode() == code){
                return permissionEnum.getDes();
            }
        }
        return "";
    }

    public int getTypeByMessage(String message){
        if(StringUtils.isEmpty(message)){
            return -1;
        }
        IsCheckCanEnum[] permissionEnums = IsCheckCanEnum.values();
        for (IsCheckCanEnum permissionEnum : permissionEnums) {
            if(permissionEnum.getDes().equals(message)){
                return permissionEnum.getCode();
            }
        }
        return -1;
    }

    public String getDes(){
        return des;
    }

    public int getCode(){
        return code;
    }
}
