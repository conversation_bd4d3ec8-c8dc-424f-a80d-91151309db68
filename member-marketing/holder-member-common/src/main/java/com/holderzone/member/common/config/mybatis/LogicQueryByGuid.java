package com.holderzone.member.common.config.mybatis;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.extension.injector.AbstractLogicMethod;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.scripting.defaults.RawSqlSource;

/**
 * @date 2020/05/09 14:46
 * @description QueryByGuid-sql拼接
 */
public class LogicQueryByGuid extends AbstractLogicMethod {
    private static final String LOGIC_QUERY_MAPPER_METHOD = "queryByGuid";
    private static final String LOGIC_QUERY_MAPPER_SQL = "SELECT %s FROM %s WHERE %s=#{%s}";
    private static final String LOGIC_QUERY_GUID_NAME = "guid";

    public LogicQueryByGuid() {
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        SqlSource logicQuerySqlSource = new RawSqlSource(this.configuration,
                String.format(LOGIC_QUERY_MAPPER_SQL, this.sqlSelectColumns(tableInfo, false),
                        tableInfo.getTableName(), LOGIC_QUERY_GUID_NAME, LOGIC_QUERY_GUID_NAME,
                        tableInfo.getLogicDeleteSql(true, false)), Object.class);
        return this.addSelectMappedStatement(mapperClass, LOGIC_QUERY_MAPPER_METHOD, logicQuerySqlSource, modelClass, tableInfo);
    }
}
