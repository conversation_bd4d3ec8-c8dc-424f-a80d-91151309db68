package com.holderzone.member.common.module.marketing.purchase.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 限购活动校验冲突
 *
 * <AUTHOR>
 * @date 2023/11/28
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PurchaseSameQO implements Serializable {


    private static final long serialVersionUID = -6324769715286881284L;
    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 主体
     */
    private String operSubjectGuid;

    /**
     * 是否适用于所有门店(0全部 1部分)
     */
    private Integer applyStoreType;

    /**
     * 门店列表
     */
    private String applyStoreStr;

    /**
     * 活动开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityStartTime;

    /**
     * 活动结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityEndTime;


    /**
     * 应用场景
     */
    private String applyBusinessStr;

    /**
     * 应用商品
     * 渠道+商品编码
     */
    private List<String> chanelCommodityCodes;
}
