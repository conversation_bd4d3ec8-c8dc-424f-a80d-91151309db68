package com.holderzone.member.common.module.settlement.rule.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 结算规则门店
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Accessors(chain = true)
public class SettlementRuleStoreDTO implements Serializable {


    private static final long serialVersionUID = -8198749219647941957L;

    private String guid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店编号
     */
    private String storeNumber;

    /**
     * 规则guid
     */
    @JsonIgnore
    private String settlementRuleGuid;

    @ApiModelProperty(value = "门店部门id")
    private String storeTeamInfoId;

    /**
     * 来源系统
     * @see com.holderzone.member.common.enums.SystemEnum
     */
    @ApiModelProperty(value = "来源系统")
    private String system;

}
