package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.qo.permission.PerMerchantBasicQO;
import com.holderzone.member.common.vo.permission.PerMerchantBasicVO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/3/23 下午4:56
 * @description 好搭档平台端远程调用
 */
@Component
@FeignClient(name = FilterConstant.MEMBER_PARTNER_PLATFORM, fallbackFactory = PartnerPlatformFeign.ServiceFallBack.class)
public interface PartnerPlatformFeign {

    @PostMapping("/platform/merchant/available/phones")
    List<String> getAvailablePhones(@RequestBody List<String> merchantPhones);

    @PostMapping("/platform/merchant/operSubjectGuid/phones")
    List<String> getAvailablePhonesByOperSubjectGuid(@RequestParam(value = "operSubjectGuid") String operSubjectGuid);


    @PostMapping("/platform/merchant/get/by/operSubjectGuid")
    List<PerMerchantBasicVO> getMerchantData(PerMerchantBasicQO basicQO);


    @GetMapping("/platform/merchant/un_auth/guids")
    List<String> getUnAuthGuidsByMerchantPhone(String merchantPhone);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<PartnerPlatformFeign> {

        @Override
        public PartnerPlatformFeign create(Throwable throwable) {
            return new PartnerPlatformFeign() {

                @Override
                public List<String> getAvailablePhones(List<String> merchantPhones) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAvailablePhones", merchantPhones,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<String> getAvailablePhonesByOperSubjectGuid(String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAvailablePhonesByOperSubjectGuid", operSubjectGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<String> getUnAuthGuidsByMerchantPhone(String merchantPhone) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getUnAuthGuidsByMerchantPhone", merchantPhone,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PerMerchantBasicVO> getMerchantData(PerMerchantBasicQO basicQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getMerchantData", basicQO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }
}
