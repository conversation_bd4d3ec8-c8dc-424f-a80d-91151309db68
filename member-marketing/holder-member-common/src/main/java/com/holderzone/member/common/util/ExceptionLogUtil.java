package com.holderzone.member.common.util;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 统一异常日志记录工具
 * 防止重复打印堆栈信息，提供智能的异常日志记录
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Component
public class ExceptionLogUtil {

    @Autowired
    private ExceptionDuplicateFilter duplicateFilter;

    /**
     * 记录异常日志（使用默认logger）
     * 
     * @param message 日志消息
     * @param exception 异常对象
     */
    public void logException(String message, Throwable exception) {
        logException(log, message, exception);
    }

    /**
     * 记录异常日志
     * 
     * @param logger 日志记录器
     * @param message 日志消息
     * @param exception 异常对象
     */
    public void logException(Logger logger, String message, Throwable exception) {
        if (duplicateFilter.shouldLogFullStack(exception)) {
            // 首次出现或超出时间窗口，记录完整堆栈
            logger.error("{}", message, exception);
        } else {
            // 重复异常，只记录简要信息和统计
            int count = duplicateFilter.getExceptionCount(exception);
            logger.error("{} [异常重复出现，已忽略堆栈信息，累计次数: {}] - {}: {}", 
                        message, count, exception.getClass().getSimpleName(), exception.getMessage());
        }
    }

    /**
     * 记录业务异常日志（通常不需要完整堆栈）
     * 
     * @param logger 日志记录器
     * @param message 日志消息
     * @param exception 业务异常对象
     */
    public void logBusinessException(Logger logger, String message, Throwable exception) {
        // 业务异常通常只记录消息，不记录堆栈
        logger.error("{} - {}: {}", message, exception.getClass().getSimpleName(), exception.getMessage());
    }

    /**
     * 记录系统异常日志（总是记录完整堆栈）
     * 
     * @param logger 日志记录器
     * @param message 日志消息
     * @param exception 系统异常对象
     */
    public void logSystemException(Logger logger, String message, Throwable exception) {
        // 系统异常总是记录完整堆栈，但仍然使用去重机制
        if (duplicateFilter.shouldLogFullStack(exception)) {
            logger.error("{} [系统异常]", message, exception);
        } else {
            int count = duplicateFilter.getExceptionCount(exception);
            logger.error("{} [系统异常-重复出现，累计次数: {}] - {}: {}", 
                        message, count, exception.getClass().getSimpleName(), exception.getMessage());
            // 对于系统异常，即使是重复的，也记录一次简化的堆栈（只记录前5层）
            logSimplifiedStack(logger, exception);
        }
    }

    /**
     * 记录简化的堆栈信息
     * 
     * @param logger 日志记录器
     * @param exception 异常对象
     */
    private void logSimplifiedStack(Logger logger, Throwable exception) {
        StackTraceElement[] stackTrace = exception.getStackTrace();
        StringBuilder sb = new StringBuilder();
        sb.append("简化堆栈: ");
        
        int count = 0;
        for (StackTraceElement element : stackTrace) {
            if (count >= 5) break;
            
            String className = element.getClassName();
            // 只记录业务相关的堆栈
            if (!className.startsWith("java.") && 
                !className.startsWith("javax.") && 
                !className.startsWith("sun.") && 
                !className.contains("$$")) {
                
                if (count > 0) sb.append(" -> ");
                sb.append(element.getClassName()).append(".").append(element.getMethodName())
                  .append("(").append(element.getFileName()).append(":").append(element.getLineNumber()).append(")");
                count++;
            }
        }
        
        if (count > 0) {
            logger.error(sb.toString());
        }
    }

    /**
     * 记录Feign调用异常
     * 
     * @param logger 日志记录器
     * @param serviceName 服务名称
     * @param methodName 方法名称
     * @param exception 异常对象
     */
    public void logFeignException(Logger logger, String serviceName, String methodName, Throwable exception) {
        String message = String.format("Feign调用异常 [服务: %s, 方法: %s]", serviceName, methodName);
        logException(logger, message, exception);
    }

    /**
     * 记录数据库操作异常
     * 
     * @param logger 日志记录器
     * @param operation 操作类型（如：查询、插入、更新、删除）
     * @param tableName 表名
     * @param exception 异常对象
     */
    public void logDatabaseException(Logger logger, String operation, String tableName, Throwable exception) {
        String message = String.format("数据库操作异常 [操作: %s, 表: %s]", operation, tableName);
        logException(logger, message, exception);
    }

    /**
     * 记录Redis操作异常
     * 
     * @param logger 日志记录器
     * @param operation 操作类型
     * @param key Redis键
     * @param exception 异常对象
     */
    public void logRedisException(Logger logger, String operation, String key, Throwable exception) {
        String message = String.format("Redis操作异常 [操作: %s, Key: %s]", operation, key);
        logException(logger, message, exception);
    }

    /**
     * 记录MQ操作异常
     * 
     * @param logger 日志记录器
     * @param operation 操作类型（发送、接收、处理）
     * @param topic 主题或队列名
     * @param exception 异常对象
     */
    public void logMqException(Logger logger, String operation, String topic, Throwable exception) {
        String message = String.format("MQ操作异常 [操作: %s, Topic: %s]", operation, topic);
        logException(logger, message, exception);
    }

    /**
     * 记录第三方接口调用异常
     * 
     * @param logger 日志记录器
     * @param apiName 接口名称
     * @param url 请求URL
     * @param exception 异常对象
     */
    public void logThirdPartyApiException(Logger logger, String apiName, String url, Throwable exception) {
        String message = String.format("第三方接口调用异常 [接口: %s, URL: %s]", apiName, url);
        logException(logger, message, exception);
    }

    /**
     * 检查异常是否为业务异常
     * 
     * @param exception 异常对象
     * @return true-业务异常，false-系统异常
     */
    private boolean isBusinessException(Throwable exception) {
        String className = exception.getClass().getName();
        return className.contains("Business") || 
               className.contains("Member") || 
               className.contains("Mall") || 
               className.contains("Marketing") ||
               className.endsWith("Exception") && !className.contains("Runtime") && !className.contains("System");
    }
}
