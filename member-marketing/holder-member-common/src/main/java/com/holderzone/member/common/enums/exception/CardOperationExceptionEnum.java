package com.holderzone.member.common.enums.exception;

import com.holderzone.member.common.enums.ResponseBase;

/**
 * <AUTHOR>
 * @description 卡操作枚举
 * @date 2021/9/1
 */
public enum CardOperationExceptionEnum implements ResponseBase {

    // 数据操作错误定义
    CREATE_PHYSICAL_CARD_SECRET_PARAMS_NULL(40000, "参数有误"),

    MEMBER_CARD_GUID_NULL(40001, "当前会员卡不能为空"),

    CREATE_PHYSICAL_CARD_SECRET_CREATE_COUNT_NULL(40002, "生成卡数量不能为空"),

    CREATE_PHYSICAL_CARD_SECRET_CREATE_COUNT_ERROR(40003, "生成实体卡为空"),

    CREATE_PHYSICAL_CARD_SECRET_CREATE_FAIL(40004, "生成实体卡失败"),

    EXPORT_PHYSICAL_CARD_SECRET_FAIL(40005, "下载卡密失败"),

    CREATE_PHYSICAL_CARD_SECRET_CREATE_COUNT_LITTLE(40006, "选择绑定会员数量不能大于生成卡密数量"),

    CREATE_PHYSICAL_CARD_SECRET_BIND_ELECTRONIC_FAIL(40007, "生成卡密同时开通电子卡失败"),

    MEMBER_GUID_NULL(40008, "当前会员不能为空"),

    OPEN_CARD_GUID_NULL(40009, "需要开通的会员卡不能为空"),

    IMPORT_MEMBER_AND_BIND_ELECTRONIC_FAIL(40010, "导入会员到系统并并绑定电子卡失败"),

    MEMBER_CARD_WITH_MEMBER_NULL(40011, "会员卡关联guid不能为空"),

    PHYSICAL_CARD_NULL(40012, "实体卡不存在"),

    PHYSICAL_CARD_HAS_BIND(40012, "实体卡被已经绑定"),

    PHYSICAL_CARD_NOT_BELONG_MEMBER_CARD(40013, "实体卡与当前会员卡必须为同一卡"),

    MEMBER_CARD_NOT_SUPPORT(40014, "此会员卡不支持开通电子卡"),

    MEMBER_NOT_EXIST_OR_STATE_ERROR(40015, "会员不存在或者状态异常"),

    THE_CURRENT_RULE_IS_UP_TODAY(40016, "当前规则今日最多还可生成%s条"),

    THE_MEMBER_CAN_NOT_CREATE_PHYSICAL_CARD(40017, "当前会员卡不能生成实体卡"),

    MEMBER_CARD_NULL(40018, "当前会员卡不存在"),

    MEMBER_CARD_CLOSE_OPEN(40019, "会员卡已结束开通"),

    MEMBER_ALREADY_OPEN(40020, "会员卡已经开通"),

    ELECTRONIC_CARD_NULL(40021, "电子卡不存在"),

    PASSWORD_ERROR(40021, "旧密码错误"),

    MEMBER_CARD_INVALID(40022, "会员卡无效"),

    MEMBER_CREDIT_INVALID(40023, "挂账账户无效"),

    MEMBER_CREDIT_NO(40024, "会员卡不存在"),
    ;

    /** 错误码 */
    private final int code;

    /** 错误描述 */
    private final String des;

    CardOperationExceptionEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDes() {
        return des;
    }
}
