package com.holderzone.member.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 操作日志
 * @date 2021/8/24
 */
@Getter
@AllArgsConstructor
public enum OperationLogTypeEnum {

    MEMBER_ACCOUNT(1, "会员账户"),
    MEMBER_TAG(2, "会员标签"),
    FOLLOW_RED_PACKET(3, "随行红包"),
    CERTIFICATION_ACTIVITY(4, "认证活动"),
    INTEGRAL_TASK(5, "积分任务"),
    ;

    private int code;

    private String des;
}
