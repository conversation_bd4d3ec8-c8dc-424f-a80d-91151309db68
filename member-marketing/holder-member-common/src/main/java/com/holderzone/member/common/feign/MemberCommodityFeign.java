package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.commodity.callback.CallbackDTO;
import com.holderzone.member.common.dto.logistics.LogisticsCommodityDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.commodity.CategoryCommodityQO;
import com.holderzone.member.common.qo.commodity.CommodityConditionQO;
import com.holderzone.member.common.qo.logistics.LogisticsCommodityQO;
import com.holderzone.member.common.qo.mall.CommodityPageQO;
import com.holderzone.member.common.qo.tool.CommodityDetailsQO;
import com.holderzone.member.common.vo.commodity.CommodityVO;
import com.holderzone.member.common.vo.commodity.StrategyVO;
import com.holderzone.member.common.vo.tool.CategoryCommodityVo;
import com.holderzone.member.common.vo.tool.CategoryVO;
import com.holderzone.member.common.vo.tool.CommodityDetailsVO;
import com.holderzone.member.common.vo.tool.LinkUniteVO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;


/**
 * 会员商品远程调用
 */
@FeignClient(name = FilterConstant.MEMBER_COMMODITY, fallbackFactory = MemberCommodityFeign.ServiceFallBack.class)
public interface MemberCommodityFeign {

    @ApiOperation("商品列表")
    @PostMapping("/list_commodity")
    List<CommodityVO> listCommodity(@RequestBody CommodityConditionQO request);


    @ApiOperation("查询策略单")
    @PostMapping("/listStrategyByCondition")
    List<StrategyVO> listStrategyByCondition(@RequestBody CommodityConditionQO qo);

    /**
     * 商品列表分页
     */
    @PostMapping("/page_commodity")
    PageResult pageCommodity(@RequestBody CommodityConditionQO request);

    @PostMapping("/mall/page_commodity")
    PageResult findMallBaseProductPage(@RequestBody CommodityPageQO query);

    @PostMapping("/get_commodity")
    CommodityDetailsVO getCommodityDetails(@RequestBody CommodityDetailsQO commodityDetailsQO);


    /**
     * 查询运营主体下分类 直接走crm
     */
    @PostMapping("/crm/list_category")
    List<CategoryVO> getCommodityCategory();

    /**
     * 主体下所有分类
     */
    @PostMapping("/list_category")
    List<LinkUniteVO> listCategory(@RequestBody CommodityConditionQO request);

    @ApiOperation("纯分类列表")
    @PostMapping("/list_category_pure")
    List<CategoryCommodityVo> listCategoryPure(@RequestBody CategoryCommodityQO request);

    /**
     * crm 回调商品相关数据
     */
    @PostMapping("/callback")
    Result<Void> commodityCallback(@RequestBody CallbackDTO callbackDTO);

    @PostMapping("/logistics/get_templateGuid/{commodityCode}")
    String getTemplateGuidByCommodityCodeByCache(@PathVariable("commodityCode") String commodityCode);

    @PostMapping("/logistics/get_templateGuids")
    Map<String, String> getTemplateGuidsByCommodityCodesByCache(@RequestBody List<String> commodityCodes);

    @PostMapping("/logistics/page_commodity")
    PageResult pageLogisticsCommodity(@RequestBody LogisticsCommodityQO query);

    @PostMapping("/logistics/exclude/page_commodity")
    PageResult pageExcludeLogisticsCommodity(@RequestBody LogisticsCommodityQO query);

    @PostMapping("/logistics/count/commodity")
    Map<String, Integer> countLogisticsCommodity(@RequestBody List<String> templateGuids);

    @PostMapping("/logistics/save/commodity")
    void saveBatchLogisticsCommodity(@RequestBody List<LogisticsCommodityDTO> commodityDTOList);

    @PutMapping("/logistics/update/commodity/{templateGuid}/{commodityCode}")
    void updateTemplateGuidByCommodityCode(@PathVariable("templateGuid") String templateGuid,
                                           @PathVariable("commodityCode") String commodityCode);

    /**
     * 通过商品id批量查询商品列表
     */
    @PostMapping("/list_commodity_by_ids")
    List<CommodityVO> listCommodityByIds(@RequestBody CommodityConditionQO request);

    @PostMapping("/list_category_commodity")
    List<CategoryCommodityVo> listCategoryCommodity(CategoryCommodityQO conditionQO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MemberCommodityFeign> {

        @Override
        public MemberCommodityFeign create(Throwable throwable) {
            return new MemberCommodityFeign() {

                @Override
                public List<CommodityVO> listCommodity(CommodityConditionQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listCommodity", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<StrategyVO> listStrategyByCondition(CommodityConditionQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listStrategyByCondition", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PageResult pageCommodity(CommodityConditionQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageCommodity", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<CategoryVO> getCommodityCategory() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCommodityCategory", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<LinkUniteVO> listCategory(CommodityConditionQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listCategory", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<CategoryCommodityVo> listCategoryPure(CategoryCommodityQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listCategoryPure", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> commodityCallback(CallbackDTO callbackDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "commodityCallback", callbackDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String getTemplateGuidByCommodityCodeByCache(String commodityCode) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getTemplateGuidByCommodityCodeByCache", commodityCode, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, String> getTemplateGuidsByCommodityCodesByCache(List<String> commodityCodes) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getTemplateGuidsByCommodityCodesByCache", commodityCodes, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PageResult pageLogisticsCommodity(LogisticsCommodityQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageLogisticsCommodity", query, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PageResult pageExcludeLogisticsCommodity(LogisticsCommodityQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageExcludeLogisticsCommodity", query, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, Integer> countLogisticsCommodity(List<String> templateGuids) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "countLogisticsCommodity", templateGuids, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void saveBatchLogisticsCommodity(List<LogisticsCommodityDTO> commodityDTOList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveBatchLogisticsCommodity", commodityDTOList, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateTemplateGuidByCommodityCode(String templateGuid, String commodityCode) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateTemplateGuidByCommodityCode",
                            templateGuid + "," + commodityCode,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<CommodityVO> listCommodityByIds(CommodityConditionQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listCommodityByIds",
                            JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PageResult findMallBaseProductPage(CommodityPageQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findMallBaseProductPage", query, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CommodityDetailsVO getCommodityDetails(CommodityDetailsQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCommodityDetails", query, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<CategoryCommodityVo> listCategoryCommodity(CategoryCommodityQO conditionQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCommodityDetails", conditionQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
