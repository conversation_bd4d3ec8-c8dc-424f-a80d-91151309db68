package com.holderzone.member.common.enums.certificate;

import org.springframework.util.StringUtils;

/**
 * 认证类型
 * <AUTHOR>
 */

public enum CertifiedInfoTypeEnum {

    /**
     * 火车票（火车票/动车票/高铁票）
     */
    THE_TRAIN_TICKET(1,"火车票（火车票/动车票/高铁票）"),

    /**
     * 飞机票
     */
    AIR_TICKETS(2,"飞机票");

    private final int code;

    private final String des;

    CertifiedInfoTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode(){
        return this.code;
    }

    public String getDes() {
        return this.des;
    }

    public static int getCodeByDes(String des) {
        if (StringUtils.isEmpty(des)) {
            return -1;
        }
        CertifiedInfoTypeEnum[] values = CertifiedInfoTypeEnum.values();
        for (CertifiedInfoTypeEnum value : values) {
            if (value.des.equals(des)) {
                return value.getCode();
            }
        }
        return -1;
    }

    public static String getDesByCode(int code) {
        CertifiedInfoTypeEnum[] values = CertifiedInfoTypeEnum.values();
        for (CertifiedInfoTypeEnum value : values) {
            if (value.getCode() == code) {
                return value.getDes();
            }
        }
        return "";
    }
}
