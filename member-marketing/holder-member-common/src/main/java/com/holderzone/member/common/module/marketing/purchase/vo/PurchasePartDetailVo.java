package com.holderzone.member.common.module.marketing.purchase.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 限量抢购活动详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Data
@Accessors(chain = true)
public class PurchasePartDetailVo implements Serializable {


    private static final long serialVersionUID = 4023658097706126004L;
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * guid
     */
    @ApiModelProperty(value = "guid")
    private String guid;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String name;
    
    /**
     * 活动标签
     */
    @ApiModelProperty(value = "活动标签")
    private String labelGuidJson;
}
