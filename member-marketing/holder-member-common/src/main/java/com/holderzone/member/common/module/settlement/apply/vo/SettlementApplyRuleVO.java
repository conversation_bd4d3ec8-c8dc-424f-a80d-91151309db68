package com.holderzone.member.common.module.settlement.apply.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementRuleBaseDTO;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import com.holderzone.member.common.module.settlement.rule.vo.SettlementRuleDiscountTreeVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * 优惠应用结果
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementApplyRuleVO implements Serializable {

    private static final long serialVersionUID = -3190628054419138188L;
    /**
     * 基础规则
     */
    private SettlementRuleBaseDTO baseRule;

    /**
     * 可叠加优惠项
     * 为空则：所有优惠不可叠加
     */
    private List<SettlementRuleDiscountTreeVO> appendDiscounts;

    /**
     * 不可叠加优惠项
     */
    private List<SettlementRuleDiscountTreeVO> disAppendDiscounts;

    /**
     * 叠加+不可叠加
     */
    @JsonIgnore
    private List<SettlementRuleDiscountTreeVO> allDiscounts;

    /**
     * 所有大项,按规则排序
     */
    private List<Integer> allDiscountOptions;

    /**
     * 优惠券限制数量大于1的规则
     */
    @JsonIgnore
    private Map<String, Integer> couponLimtNumberMap;

    /**
     * 默认规则
     *
     * @return 规则
     */
    public static SettlementApplyRuleVO defaultRule() {
        final SettlementRuleBaseDTO baseRule = new SettlementRuleBaseDTO()
                .setGuid(StringConstant.PARENT)
                .setCouponLimit(BooleanEnum.TRUE.getCode())
                //优惠券上限：默认一张
                .setCouponLimitNum(1)
                //默认回退优惠：默认是
                .setCouponRollback(BooleanEnum.TRUE.getCode())
                .setUseType(BooleanEnum.TRUE.getCode());
        return new SettlementApplyRuleVO()
                .setBaseRule(baseRule)
                .setAppendDiscounts(Collections.emptyList())
                .setDisAppendDiscounts(Collections.emptyList())
                .setAllDiscounts(Collections.emptyList())
                .setCouponLimtNumberMap(Collections.emptyMap())
                .setAllDiscountOptions(SettlementDiscountOptionEnum.implOptions());
    }

    public Map<String, Integer> getCouponLimtNumberMap() {
        return Optional.ofNullable(couponLimtNumberMap)
                .orElse(Collections.emptyMap());
    }

    public List<SettlementRuleDiscountTreeVO> getAllDiscounts() {
        return Optional.ofNullable(allDiscounts)
                .orElse(Collections.emptyList());
    }
}
