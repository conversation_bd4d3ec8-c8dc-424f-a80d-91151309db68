package com.holderzone.member.common.external;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.common.enums.PlatformEnum;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description
 */
@Component
public class ExternalBaseFactory {

    private final ExternalBaseService holderBaseServiceImpl;

    private final ExternalBaseService passBaseServiceImpl;

    private final ExternalBaseService saasBaseServiceImpl;

    public ExternalBaseFactory(@Qualifier("holderBaseServiceImpl")ExternalBaseService holderBaseServiceImpl, @Qualifier("marketBaseServiceImpl")ExternalBaseService passBaseServiceImpl, ExternalBaseService saasBaseServiceImpl) {
        this.holderBaseServiceImpl = holderBaseServiceImpl;
        this.passBaseServiceImpl = passBaseServiceImpl;
        this.saasBaseServiceImpl = saasBaseServiceImpl;
    }

    public ExternalBaseService build(PlatformEnum platformEnum){
        switch (platformEnum){
            case HOLDER_CANTEEN:
                return holderBaseServiceImpl;
            case PASS_RETAIL:
                return passBaseServiceImpl;
            case SAAS:
                return saasBaseServiceImpl;
            default:
                throw new BusinessException("平台有误");
        }
    }
}
