package com.holderzone.member.common.module.marketing.purchase.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 限量抢购活动应用统计
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Data
@Accessors(chain = true)
public class PurchaseApplyCountVo implements Serializable {


    private static final long serialVersionUID = -1801767716348456683L;

    /**
     * 订单数
     */
    @ApiModelProperty(value = "订单数")
    private Integer orderNum;

    /**
     * 实付金额
     * 统计筛选条件下活动订单数据“实付金额”合计；
     */
    @ApiModelProperty(value = "实付金额")
    private BigDecimal orderAmount;

    /**
     * 客单价
     * 统计筛选条件下订单实付金额合计÷下单用户去重人数，保留2位小数；
     */
    @ApiModelProperty(value = "客单价")
    private BigDecimal personAmount;
}
