package com.holderzone.member.common.module.settlement.rule.enums;

import cn.hutool.core.collection.CollUtil;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyDiscountVO;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyDiscountDetailVO;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyOrderVO;

import cn.hutool.core.lang.Console;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 结算台优惠具体项
 * （最底层的类型，后端区分）
 *
 * <AUTHOR>
 * @create 2023-09-02
 * @description 结算台优惠类型
 * 优惠项大类 {@link SettlementDiscountItemEnum}
 */
@AllArgsConstructor
@Getter
public enum SettlementDiscountOptionEnum {

    NONE(0, "无", null, null, 0),
    /**
     * 商品会员价
     */
    MEMBER_PRICE(1, "商品会员价权益", SettlementDiscountTypeEnum.SINGLE_ITEM, SettlementDiscountItemEnum.MEMBER_RIGHTS, 1),
    /**
     * 会员折扣权益
     */
    MEMBER_DISCOUNT(2, "会员折扣权益", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.MEMBER_RIGHTS, 1),
    /**
     * 会员卡折扣权益
     */
    MEMBER_CARD_DISCOUNT(3, "会员卡折扣权益", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.MEMBER_RIGHTS, 1),
    /**
     * 代金券
     */
    COUPON_VOUCHER(10, "代金券", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.COUPON_VOUCHER, 0),
    /**
     * 折扣券
     */
    COUPON_DISCOUNT(11, "折扣券", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.COUPON_DISCOUNT, 0),
    /**
     * 兑换券
     */
    COUPON_EXCHANGE(12, "兑换券", SettlementDiscountTypeEnum.SINGLE_ITEM, SettlementDiscountItemEnum.COUPON_EXCHANGE, 0),
    /**
     * 满减满折
     */
    FULL_OFF(20, "满减满折", SettlementDiscountTypeEnum.ORDER, SettlementDiscountItemEnum.FULL_OFF, 1),
    /**
     * 限时特价
     */
    LIMITED_TIME_SPECIAL(22, "限时特价", SettlementDiscountTypeEnum.SINGLE_ITEM, SettlementDiscountItemEnum.LIMITED_TIME_SPECIAL, 1),
    /**
     * 第N份优惠
     */
    NTH_DISCOUNT(23, "第N份优惠", SettlementDiscountTypeEnum.SINGLE_ITEM, SettlementDiscountItemEnum.NTH_DISCOUNT, 1),
    /**
     * 积分兑换
     */
    INTEGRAL_EXPLAIN(91, "积分抵现", SettlementDiscountTypeEnum.PROPERTY, SettlementDiscountItemEnum.ASSET_PREFERENCE, 1);

    /**
     * 编码
     */
    private final int code;

    /**
     * 信息
     */
    private final String des;

    /**
     * 类型
     */
    private final SettlementDiscountTypeEnum type;

    /**
     * 优惠项
     */
    private final SettlementDiscountItemEnum item;

    /**
     * 单笔限用数量
     */
    private final int limitNum;

    /**
     * 根据code获取name
     *
     * @param code code
     * @return 操作结果
     */

    public static String getDesByCode(int code) {
        for (SettlementDiscountOptionEnum anEnum : SettlementDiscountOptionEnum.values()) {
            if (anEnum.getCode() == code) {
                return anEnum.getDes();
            }
        }
        return null;
    }

    public static SettlementDiscountOptionEnum getEnum(Integer code) {
        for (SettlementDiscountOptionEnum anEnum : SettlementDiscountOptionEnum.values()) {
            if (anEnum.getCode() == code) {
                return anEnum;
            }
        }
        return NONE;
    }

    /**
     * 需要初始化的父级
     *
     * @return
     */
    public static List<SettlementDiscountOptionEnum> initValues() {
//        final List<SettlementDiscountOptionEnum> optionEnums = Arrays.stream(SettlementDiscountOptionEnum.values())
        //todo 暂时只初始化这三个
        final List<SettlementDiscountOptionEnum> optionEnums = Lists.newArrayList(
                        NONE,
                        MEMBER_DISCOUNT,
                        COUPON_VOUCHER,
                        COUPON_DISCOUNT,
                        COUPON_EXCHANGE,
                        INTEGRAL_EXPLAIN,
                        LIMITED_TIME_SPECIAL,
                        FULL_OFF,
                        NTH_DISCOUNT).stream()
                .filter(e -> !noChildren(e))
                .collect(Collectors.toList());
        //移除第一个none
        optionEnums.remove(0);
        return optionEnums;
    }

    public static boolean isShare(Integer discountOption) {
        return discountOption == SettlementDiscountOptionEnum.LIMITED_TIME_SPECIAL.getCode()
                || discountOption == SettlementDiscountOptionEnum.FULL_OFF.getCode()
                || discountOption == SettlementDiscountOptionEnum.MEMBER_DISCOUNT.getCode()
                || discountOption == SettlementDiscountOptionEnum.NTH_DISCOUNT.getCode()
                ;
    }


    public static boolean noShowDiscountGuid(Integer option) {
        return noShowDiscountGuid().contains(option);
    }

    /**
     * 前端不显示discountGuid
     * 商品会员价、会员折扣、积分抵现
     *
     * @return
     */
    public static List<Integer> noShowDiscountGuid() {
        return Lists.newArrayList(
                MEMBER_PRICE.code,
                MEMBER_CARD_DISCOUNT.code,
                INTEGRAL_EXPLAIN.code
        );
    }

    /**
     * 无子集的项
     *
     * @param optionEnum 类型
     * @return
     */
    public static boolean noChildren(SettlementDiscountOptionEnum optionEnum) {
        return noChildren(optionEnum.code);
    }

    /**
     * 老的实现方法，没有直接修改入参dto
     * 折扣、积分
     *
     * @return 是否
     */
    public static boolean oldImpl(int option) {
        return option == MEMBER_DISCOUNT.code || option == INTEGRAL_EXPLAIN.code;
    }

    /**
     * 老的实现方法类型
     *
     * @return 老的类型
     */
    public static List<Integer> oldImpl() {
        //积分只对接了商城，按新的来算, INTEGRAL_EXPLAIN.code
        return Lists.newArrayList(MEMBER_DISCOUNT.code, INTEGRAL_EXPLAIN.code);
    }

    /**
     * 限时特价类型
     *
     * @param option 类型
     * @return
     */
    public static boolean isCoupon(int option) {
        return COUPON_VOUCHER.code == option
                || COUPON_DISCOUNT.code == option
                || COUPON_EXCHANGE.code == option;
    }

    /**
     * 单品级判断
     *
     * @param option 类型
     * @return
     */
    public static boolean isSingleDiscount(int option) {
        return LIMITED_TIME_SPECIAL.code == option
                || MEMBER_PRICE.code == option
                || COUPON_EXCHANGE.code == option;
    }

    /**
     * 单品级判断
     *
     * @param option 类型
     * @return
     */
    public static boolean isSingleDiscount(List<Integer> option) {
        return option.contains(LIMITED_TIME_SPECIAL.code)
                || option.contains(MEMBER_PRICE.code)
                || option.contains(COUPON_EXCHANGE.code);
    }


    /**
     * 订单级判断
     *
     * @param option 类型
     * @return
     */
    public static boolean isOrderDiscount(List<Integer> option) {
        return option.contains(MEMBER_DISCOUNT.code)
                || option.contains(MEMBER_CARD_DISCOUNT.code)
                || option.contains(COUPON_VOUCHER.code)
                || option.contains(COUPON_DISCOUNT.code)
                || option.contains(FULL_OFF.code);
    }

    /**
     * 订单级判断
     *
     * @param option 类型
     * @return
     */
    public static boolean isOrderDiscount(int option) {
        return MEMBER_DISCOUNT.code == option
                || MEMBER_CARD_DISCOUNT.code == option
                || COUPON_VOUCHER.code == option
                || COUPON_DISCOUNT.code == option
                || FULL_OFF.code == option;
    }

    /**
     * 是否是折扣券优惠
     *
     * @param option 类型
     * @return
     */
    public static boolean isOrderCouponDiscount(int option) {
        return COUPON_DISCOUNT.code == option;
    }

    /**
     * 是否代金券优惠
     *
     * @param option 类型
     * @return
     */
    public static boolean isCouponVoucher(int option) {
        return COUPON_VOUCHER.code == option;
    }

    /**
     * 资产级类型
     *
     * @param option 类型
     * @return
     */
    public static boolean isPropertyDiscount(int option) {
        return INTEGRAL_EXPLAIN.code == option;
    }

    /**
     * 资产级类型
     *
     * @param option 类型
     * @return
     */
    public static boolean isPropertyDiscount(List<Integer> option) {
        return option.contains(INTEGRAL_EXPLAIN.code);
    }

    /**
     * 优惠券类型
     *
     * @return
     */
    public static List<Integer> getCoupon() {
        return Lists.newArrayList(
                COUPON_VOUCHER.code,
                COUPON_DISCOUNT.code,
                COUPON_EXCHANGE.code);
    }

    /**
     * 目前实现了的优惠
     *
     * @param option
     * @return
     */
    public static boolean isImplOption(int option) {
        return implOptions().contains(option);
    }

    /**
     * todo 目前实现了的优惠
     *
     * @return
     */
    public static List<Integer> implOptions() {
        return Lists.newArrayList(
                SettlementDiscountOptionEnum.COUPON_EXCHANGE.getCode(),
                SettlementDiscountOptionEnum.MEMBER_DISCOUNT.getCode(),
                SettlementDiscountOptionEnum.MEMBER_CARD_DISCOUNT.getCode()
                , SettlementDiscountOptionEnum.COUPON_VOUCHER.getCode()
                , SettlementDiscountOptionEnum.COUPON_DISCOUNT.getCode()
                , SettlementDiscountOptionEnum.INTEGRAL_EXPLAIN.getCode()
                , SettlementDiscountOptionEnum.FULL_OFF.getCode()
        );
    }

    /**
     * 是否存在子级
     * 商品会员价、积分抵现只有第一层
     *
     * @param option 类型
     * @return
     */
    public static boolean noChildren(Integer option) {
        return option.equals(MEMBER_PRICE.code);
    }

    /**
     * 优惠类型key ，用来确定唯一
     *
     * @param discountOption 类型
     * @param discountGuid   类型guid
     * @return
     */
    public static String optionMapKey(Integer discountOption, String discountGuid) {
        return discountOption + StringConstant.COLON + discountGuid;
    }

    /**
     * 优惠类型key ，用来确定唯一
     *
     * @param discountOption 类型
     * @param discountGuid   类型guid
     * @param discountId     具体类型的数据（优惠券guid）
     * @return
     */
    public static String optionMapKey(Integer discountOption, String discountGuid, String discountId) {
        return discountOption + StringConstant.COLON + discountGuid + StringConstant.COLON + discountId;
    }

    /**
     * 获取当前优惠可以共享的其他优惠类型
     * 共享规则：
     * SINGLE_ITEM级可以共享优先级更低的SINGLE_ITEM级，以及所有ORDER级和PROPERTY级
     * ORDER级不能共享SINGLE_ITEM级，只能共享PROPERTY级
     * ORDER级内部根据applyRule.allDiscountOptions排序决定共享关系
     * PROPERTY级优先级最低，不能共享任何其他优惠
     *
     * @param dto           结算申请参数
     * @param discountVoMap 当前可用的优惠选项map
     * @param currentOption 当前正在处理的优惠选项
     * @return 可共享的其他优惠选项列表
     */
    public static List<Integer> getShareOptionCode(SettlementApplyOrderDTO dto,
                                                   Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                                   SettlementDiscountOptionEnum currentOption) {
        List<Integer> settlementDiscountOptionEnumList = Lists.newArrayList();

        // 获取当前优惠的类型级别
        SettlementDiscountTypeEnum currentType = currentOption.getType();

        switch (currentType) {
            case SINGLE_ITEM:
                handleSingleItemShare(dto, discountVoMap, currentOption, settlementDiscountOptionEnumList);
                break;

            case ORDER:
                handleOrderShare(dto, discountVoMap, currentOption, settlementDiscountOptionEnumList);
                break;

            case PROPERTY:
                // PROPERTY级优先级最低，不能共享任何其他优惠
                break;

            default:
                break;
        }

        return settlementDiscountOptionEnumList;
    }

    /**
     * 处理SINGLE_ITEM级优惠的共享逻辑
     * SINGLE_ITEM级可以共享优先级更低的SINGLE_ITEM级，以及所有ORDER级和PROPERTY级
     */
    private static void handleSingleItemShare(SettlementApplyOrderDTO dto,
                                              Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                              SettlementDiscountOptionEnum currentOption,
                                              List<Integer> resultList) {

        // 添加优先级更低的SINGLE_ITEM级优惠
        addLowerPrioritySingleItemOptions(dto, discountVoMap, currentOption, resultList);

        // SINGLE_ITEM级可以共享所有ORDER级和PROPERTY级
        addOrderLevelOptions(dto, discountVoMap, resultList);
        addIfShareable(dto, discountVoMap, INTEGRAL_EXPLAIN, resultList);
    }

    /**
     * 处理ORDER级优惠的共享逻辑
     * ORDER级不能共享SINGLE_ITEM级，只能共享PROPERTY级
     * ORDER级内部根据applyRule.allDiscountOptions排序决定共享关系
     */
    private static void handleOrderShare(SettlementApplyOrderDTO dto,
                                         Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                         SettlementDiscountOptionEnum currentOption,
                                         List<Integer> resultList) {

        // ORDER级内部根据优先级排序决定共享关系
        addLowerPriorityOrderOptions(dto, discountVoMap, currentOption, resultList);

        // INTEGRAL_EXPLAIN优先级最低，放在最后
        addIfShareable(dto, discountVoMap, INTEGRAL_EXPLAIN, resultList);
    }

    /**
     * 添加所有ORDER级优惠选项
     * 根据结算规则配置的优先级顺序动态添加
     */
    private static void addOrderLevelOptions(SettlementApplyOrderDTO dto,
                                             Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                             List<Integer> resultList) {

        // 获取配置的ORDER级优惠优先级列表
        List<Integer> orderPriorityList = getOrderPriorityList(discountVoMap);

        if (CollUtil.isNotEmpty(orderPriorityList)) {
            // 按配置的优先级顺序添加ORDER级优惠
            for (Integer optionCode : orderPriorityList) {
                SettlementDiscountOptionEnum optionEnum = getEnum(optionCode);
                if (optionEnum != null) {
                    addIfShareable(dto, discountVoMap, optionEnum, resultList);
                }
            }
        }
    }

    /**
     * 添加优先级更低的ORDER级优惠选项
     * 根据applyRule.allDiscountOptions的排序来判断优先级
     */
    private static void addLowerPriorityOrderOptions(SettlementApplyOrderDTO dto,
                                                     Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                                     SettlementDiscountOptionEnum currentOption,
                                                     List<Integer> resultList) {

        // 尝试从discountVoMap中获取applyRule信息
        List<Integer> allDiscountOptions = getOrderPriorityList(discountVoMap);

        if (CollUtil.isEmpty(allDiscountOptions)) {
            return;
        }

        int currentIndex = allDiscountOptions.indexOf(currentOption.getCode());
        if (currentIndex == -1) {
            // 当前优惠不在配置列表中，不进行共享
            return;
        }

        // 只添加优先级更低的ORDER级优惠（索引更大的）
        for (int i = currentIndex + 1; i < allDiscountOptions.size(); i++) {
            Integer lowerPriorityOption = allDiscountOptions.get(i);
            SettlementDiscountOptionEnum lowerOption = getEnum(lowerPriorityOption);

            // 确保是ORDER级优惠，并且是可共享的优惠或积分抵现
            if (lowerOption != null && lowerOption.getType() == SettlementDiscountTypeEnum.ORDER &&
                    (isShare(lowerPriorityOption) || lowerPriorityOption.equals(INTEGRAL_EXPLAIN.getCode()))) {
                addIfShareable(dto, discountVoMap, lowerOption, resultList);
            }
        }
    }

    /**
     * 添加优先级更低的SINGLE_ITEM级优惠选项
     */
    private static void addLowerPrioritySingleItemOptions(SettlementApplyOrderDTO dto,
                                                          Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                                          SettlementDiscountOptionEnum currentOption,
                                                          List<Integer> resultList) {

        // 获取配置的SINGLE_ITEM级优惠优先级列表
        List<Integer> singleItemPriorityList = getSingleItemPriorityList(discountVoMap);

        if (CollUtil.isEmpty(singleItemPriorityList)) {
            return;
        }

        // 找到当前优惠在配置列表中的位置
        int currentIndex = singleItemPriorityList.indexOf(currentOption.getCode());
        if (currentIndex == -1) {
            // 当前优惠不在配置列表中，不进行共享
            return;
        }

        // 只添加优先级更低的SINGLE_ITEM级优惠（索引更大的）
        for (int i = currentIndex + 1; i < singleItemPriorityList.size(); i++) {
            Integer lowerPriorityOption = singleItemPriorityList.get(i);
            SettlementDiscountOptionEnum lowerOption = getEnum(lowerPriorityOption);

            // 确保是SINGLE_ITEM级优惠，并且是可共享的优惠
            if (lowerOption != null && lowerOption.getType() == SettlementDiscountTypeEnum.SINGLE_ITEM &&
                    isShare(lowerPriorityOption)) {
                addIfShareable(dto, discountVoMap, lowerOption, resultList);
            }
        }
    }

    /**
     * 获取SINGLE_ITEM级优惠的优先级列表
     * 从discountVoMap中的第一个OrderVO的applyRule获取
     */
    private static List<Integer> getSingleItemPriorityList(Map<Integer, List<SettlementApplyOrderVO>> discountVoMap) {
        // 尝试从任意一个OrderVO中获取applyRule
        for (List<SettlementApplyOrderVO> orderVOList : discountVoMap.values()) {
            if (CollUtil.isNotEmpty(orderVOList)) {
                SettlementApplyOrderVO orderVO = orderVOList.get(0);
                if (orderVO.getApplyRule() != null &&
                        CollUtil.isNotEmpty(orderVO.getApplyRule().getAllDiscountOptions())) {

                    // 只返回SINGLE_ITEM级的优惠选项
                    return orderVO.getApplyRule().getAllDiscountOptions().stream()
                            .filter(option -> {
                                SettlementDiscountOptionEnum optionEnum = getEnum(option);
                                return optionEnum != null && optionEnum.getType() == SettlementDiscountTypeEnum.SINGLE_ITEM;
                            })
                            .collect(Collectors.toList());
                }
            }
        }

        return Collections.emptyList();
    }

    /**
     * 获取ORDER级优惠的优先级列表
     * 从discountVoMap中的第一个OrderVO的applyRule获取
     */
    private static List<Integer> getOrderPriorityList(Map<Integer, List<SettlementApplyOrderVO>> discountVoMap) {
        // 尝试从任意一个OrderVO中获取applyRule
        for (List<SettlementApplyOrderVO> orderVOList : discountVoMap.values()) {
            if (CollUtil.isNotEmpty(orderVOList)) {
                SettlementApplyOrderVO orderVO = orderVOList.get(0);
                if (orderVO.getApplyRule() != null &&
                        CollUtil.isNotEmpty(orderVO.getApplyRule().getAllDiscountOptions())) {

                    // 只返回ORDER级的优惠选项
                    return orderVO.getApplyRule().getAllDiscountOptions().stream()
                            .filter(option -> {
                                SettlementDiscountOptionEnum optionEnum = getEnum(option);
                                return optionEnum != null && optionEnum.getType() == SettlementDiscountTypeEnum.ORDER
                                        && (isShare(option));
                            })
                            .collect(Collectors.toList());
                }
            }
        }

        return Collections.emptyList();
    }

    /**
     * 如果指定优惠类型可以叠加，则添加到结果列表中
     */
    private static void addIfShareable(SettlementApplyOrderDTO dto,
                                       Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                       SettlementDiscountOptionEnum targetOption,
                                       List<Integer> resultList) {
        if (isaDiscountShare(dto, discountVoMap, targetOption)) {
            Console.log("优惠类型可以叠加：" + targetOption.getDes());
            resultList.add(targetOption.getCode());
        }
    }

    private static boolean isaDiscountShare(SettlementApplyOrderDTO dto,
                                            Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                            SettlementDiscountOptionEnum optionEnum) {
        return isDiscountOptionShare(dto, discountVoMap, optionEnum);
    }

    private static boolean isDiscountOptionShare(SettlementApplyOrderDTO dto,
                                                 Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                                 SettlementDiscountOptionEnum optionEnum) {
        return isProperty(dto, discountVoMap, optionEnum);
    }

    private static boolean isProperty(SettlementApplyOrderDTO dto,
                                      Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
                                      SettlementDiscountOptionEnum optionEnum) {
        return discountVoMap.containsKey(optionEnum.getCode())
                && isCheckedByOption(dto, discountVoMap, optionEnum.getCode());
    }

    /**
     * 校验当前优惠项是否叠加
     * 修复Bug：检查所有优惠方案的共享属性，采用更严格的策略
     */
    private static boolean isCheckedByOption(
            SettlementApplyOrderDTO dto,
            Map<Integer, List<SettlementApplyOrderVO>> discountVoMap,
            Integer code) {
        List<SettlementApplyOrderVO> selectOrderVOList = discountVoMap.get(code);

        if (CollUtil.isEmpty(selectOrderVOList)) {
            return false;
        }

        // 当前采用方案1：只要有一个优惠方案可以共享，就认为该类型可以共享
        return selectOrderVOList.stream()
                .filter(orderVO -> CollUtil.isNotEmpty(orderVO.getDiscountList()))
                .flatMap(orderVO -> orderVO.getDiscountList().stream())
                .filter(discountVO -> CollUtil.isNotEmpty(discountVO.getDiscountList()))
                .flatMap(discountVO -> discountVO.getDiscountList().stream())
                .map(SettlementApplyDiscountDetailVO::getDiscountGuid)
                .filter(Objects::nonNull)
                .anyMatch(discountGuid -> isCheckShare(discountGuid, dto, code));
    }

    public static boolean isCheckShare(String discountGuid, SettlementApplyOrderDTO dto, Integer code) {
        //判断当前优惠是否叠加
        final Map<Integer, Set<String>> appendMap = dto.getAppendMap();
        final Set<String> apendSet = appendMap.get(code);
        return CollUtil.isNotEmpty(apendSet) && apendSet.contains(discountGuid);
    }

    /**
     * 获取SINGLE_ITEM类型优惠的排序顺序
     * 统一的排序规则：COUPON_EXCHANGE(12) -> LIMITED_TIME_SPECIAL(22) -> NTH_DISCOUNT(23)
     *
     * @param option 优惠选项代码
     * @return 排序权重，数值越小优先级越高
     */
    public static int getSingleItemOrder(Integer option) {
        if (option == null) {
            return 999;
        }
        if (option.equals(COUPON_EXCHANGE.getCode())) {
            return 1;
        } else if (option.equals(LIMITED_TIME_SPECIAL.getCode())) {
            return 2;
        } else if (option.equals(NTH_DISCOUNT.getCode())) {
            return 3;
        }
        // 其他未知的SINGLE_ITEM类型，排在最后
        return 999;
    }
}
