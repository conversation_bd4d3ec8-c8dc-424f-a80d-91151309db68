package com.holderzone.member.common.module.settlement.apply.vo.detail;

import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyDiscountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 会员折扣返回
 * <p>
 * 规定了必须返回的字段
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SettlementApplyDiscountDetailOfDiscountVO extends SettlementApplyDiscountDetailVO implements Serializable {


    private static final long serialVersionUID = -757585663147511367L;
    /**
     * 折扣力度
     */
    @ApiModelProperty("折扣力度")
    private BigDecimal discountDynamics;

    /**
     * 单次优惠限制金额
     */
    @ApiModelProperty("单次优惠限制金额")
    private BigDecimal singleDiscountsLimitedAmount;

    /**
     * 单次优惠限制 0：不限制 1：限制
     * <p>
     * todo 去掉，使用 singleDiscountsLimitedAmount > 0限制，<=0不限制
     * todo 考虑将 单次、周期、累计 合并为一个字段
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @ApiModelProperty("单次优惠限制")
    private Integer singleDiscountsLimited;

    /**
     * 会员卡guid
     */
    @ApiModelProperty("会员卡guid")
    private String memberCardGuid;

    /**
     * 会员价限制次数
     */
    @ApiModelProperty("会员价限制次数")
    private Integer memberPriceLimited;


    /**
     * 周期优惠限制 0：不限制 1：限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @ApiModelProperty("周期优惠限制 0：不限制 1：限制")
    private Integer periodDiscountLimited;

    /**
     * 周期最低优惠
     */
    @ApiModelProperty("周期最低优惠")
    private BigDecimal minimumThisMonthAmount;

    /**
     * 累计优惠限制 0：不限制 1：限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    private Integer totalDiscountLimited;
    /**
     * 周期类型 -1:自定义 0：日 1：周 2：月 3：年 4 累计
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    private Integer equitiesTimeLimitedType;

    /**
     * 累计优惠限制金额
     */
    private BigDecimal totalDiscountLimitedAmount;
}
