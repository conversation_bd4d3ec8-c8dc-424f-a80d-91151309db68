package com.holderzone.member.common.module.marketing.purchase.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseStateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 限购活动列表查询
 *
 * <AUTHOR>
 * @date 2023/11/27
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchasePageQo extends PageDTO implements Serializable {

    private static final long serialVersionUID = 1588108984417029178L;
    /**
     * 主体
     */
    private String operSubjectGuid;

    /**
     * 活动id、名称
     */
    private String keywords;


    /**
     * 活动开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityStartTime;

    /**
     * 活动结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityEndTime;

    /**
     * 限购规则:0每人限购 1每单限购 2周期限购
     *
     * @see com.holderzone.member.common.module.marketing.purchase.enums.PurchaseRuleTypeEnum
     */
    @ApiModelProperty(value = "限购规则:0每人限购 1每单限购 2周期限购")
    private Integer purchaseRuleType;

    /**
     * 限购场景
     */
    private String businessType;

    /**
     * 活动状态
     *
     * @see PurchaseStateEnum
     */
    private Integer activityState;
}
