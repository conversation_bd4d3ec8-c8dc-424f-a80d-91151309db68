package com.holderzone.member.common.module.settlement.apply.vo.show;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.holderzone.member.common.module.settlement.apply.enums.SettlementDiscountShowTypeEnum;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyDiscountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 优惠列表
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementApplyDiscountShowVO implements Serializable {
    
    private static final long serialVersionUID = -6666050001256221990L;

    /**
     * 展示优惠名称
     * @see com.holderzone.member.common.module.settlement.apply.enums.SettlementDiscountShowTypeEnum
     */
    @ApiModelProperty(value = "优惠类型")
    @JsonInclude
    private Integer showType;

    /**
     * 优惠名称
     */
    @ApiModelProperty(value = "优惠名称")
    @JsonInclude
    private String showName;

    /**
     * 优惠金额
     * todo 展示顺序：金额倒序
     */
    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmount;
    
    /**
     * 具体优惠项
     */
    @ApiModelProperty(value = "具体优惠项")
    private List<SettlementApplyDiscountDetailVO> discountList;

    public  static SettlementApplyDiscountShowVO build(SettlementDiscountShowTypeEnum typeEnum){
        return new SettlementApplyDiscountShowVO()
                .setShowType(typeEnum.getCode())
                .setShowName(typeEnum.getDes())
                .setDiscountAmount(BigDecimal.ZERO)
                .setDiscountList(new ArrayList<>());
    }
}
