package com.holderzone.member.common.module.marketing.purchase.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 充值活动门店表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PurchaseStoreVo implements Serializable {


    private static final long serialVersionUID = -934180738769102523L;

    /**
     * storeGuid
     */
    private String storeGuid;

    /**
     * storeName
     */
    private String storeName;
}
