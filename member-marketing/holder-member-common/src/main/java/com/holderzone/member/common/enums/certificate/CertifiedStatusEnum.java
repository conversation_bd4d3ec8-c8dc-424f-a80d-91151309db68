package com.holderzone.member.common.enums.certificate;

/**
 * @program: member-marketing
 * @description: 认证审核状态枚举
 * @author: pan tao
 * @create: 2022-04-12 11:59
 */
public enum CertifiedStatusEnum {

    AWAIT_AUDIT(0, "待审核"),

    AUDIT_PASS(1, "审核通过"),

    AUDIT_REJECTED(2, "驳回"),

    OVERDUE(3, "已过期"),

    ERROR_PARAM(-1, "错误参数");

    private int code;

    private String des;

    CertifiedStatusEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return this.code;
    }

    public String getDes() {
        return this.des;
    }

    public static CertifiedStatusEnum getEnumByCode(int code) {

        for (CertifiedStatusEnum certifiedStatusEnum : CertifiedStatusEnum.values()) {
            if (code == certifiedStatusEnum.getCode()) {
                return certifiedStatusEnum;
            }
        }
        return ERROR_PARAM;
    }
}
