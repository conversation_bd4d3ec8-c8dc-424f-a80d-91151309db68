package com.holderzone.member.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @ProjectName: member-marketing
 * @ClassName: DateUnit
 */
@Getter
@AllArgsConstructor
public enum DateUnitEnum {
    HOUR(0, "时"),
    DAY(1, "日"),
    WEE<PERSON>(2, "周"),
    MONTH(3, "月"),
    YEAR(4, "年");

    private Integer code;
    private String des;


    public static String getNameByCode(int code) {
        for (DateUnitEnum titleEnum : DateUnitEnum.values()) {
            if (titleEnum.getCode() == code) {
                return titleEnum.getDes();
            }
        }
        return null;
    }

    public static String getCustomizedNameByCode(int code) {
        for (DateUnitEnum titleEnum : DateUnitEnum.values()) {
            if (titleEnum.getCode() == code) {
                if (Objects.equals(MONTH.code, titleEnum.code)) {
                    return "个月";
                }
                return titleEnum.getDes();
            }
        }
        return null;
    }
}
