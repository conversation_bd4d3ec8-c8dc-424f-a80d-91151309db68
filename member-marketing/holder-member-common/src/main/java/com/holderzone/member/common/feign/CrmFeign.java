package com.holderzone.member.common.feign;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.dto.crm.*;
import com.holderzone.member.common.dto.grade.CommodityInfoDTO;
import com.holderzone.member.common.dto.grade.StoreInfoDTO;
import com.holderzone.member.common.dto.grade.StrategyInfoDTO;
import com.holderzone.member.common.dto.order.CrmOrderDTO;
import com.holderzone.member.common.dto.order.CrmOrderStockDTO;
import com.holderzone.member.common.qo.crm.CrmOrderDetailQo;
import com.holderzone.member.common.qo.crm.CrmOrderDetailVo;
import com.holderzone.member.common.qo.grade.CommodityInfoQO;
import com.holderzone.member.common.qo.grade.GradeCommodityBasePageQO;
import com.holderzone.member.common.qo.growth.AppletGrowthStoreQO;
import com.holderzone.member.common.qo.mall.*;
import com.holderzone.member.common.qo.tool.CategoryQO;
import com.holderzone.member.common.qo.tool.CheckContentQO;
import com.holderzone.member.common.qo.tool.CommodityDetailsQO;
import com.holderzone.member.common.qo.tool.StoreByIdQO;
import com.holderzone.member.common.vo.feign.*;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import com.holderzone.member.common.vo.grade.StrategyInfoVO;
import com.holderzone.member.common.vo.mall.ProductCrmCategoryVO;
import com.holderzone.member.common.vo.mall.ProductCrmVO;
import com.holderzone.member.common.vo.member.OperationMemberDetailVO;
import com.holderzone.member.common.vo.tool.CategoryVO;
import com.holderzone.member.common.vo.tool.CheckContentVO;
import com.holderzone.member.common.vo.tool.CommodityDetailsVO;
import com.holderzone.member.common.vo.tool.OperSubjectVO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/12/29 下午3:09
 * @description crm-base请求
 */
@Component
@FeignClient(name = FilterConstant.FEIGN_ODOO, fallbackFactory = CrmFeign.ServiceFallBack.class, url = "${feign.crm}")
public interface CrmFeign {

    /**
     * 查询主体下商品
     */
    @PostMapping(value = "/api/equity_center/get_commodity_code", produces = "application/json;charset=utf-8")
    CrmFeignModel<ProductCrmVO> getCommodity(@RequestBody QueryProductCategoryCrmQO queryProductCrmQO);

    /**
     * 获取策略单分类接口
     */
    @PostMapping(value = "/api/get_commodity_category/information", produces = "application/json;charset=utf-8")
    CrmFeignModel<ProductCrmCategoryVO> getCommodityCategory(@RequestBody QueryProductCategoryCrmQO queryProductCrmQO);

    /**
     * 查询主体下的店铺和档口
     */
    @PostMapping(value = "/api/get_store_stall/information", produces = "application/json;charset=utf-8")
    CrmCommodityReturnVO<StoreInfoVO> getStoreStall(@RequestBody StoreInfoDTO storeInfoDTO);

    /**
     * 查询运营主体列表
     */
    @GetMapping(value = "/api/applets/list", produces = "application/json;charset=utf-8")
    CrmFeignModel<OperSubjectVO> listOperSubjectAndApplet();


    /**
     * 查询运营主体下分类
     */
    @PostMapping(value = "/api/get_commodity_category/information", produces = "application/json;charset=utf-8")
    CrmFeignModel<CategoryVO> getCommodityCategory(@RequestBody CategoryQO categoryQO);


    /**
     * 查询商品详情
     */
    @PostMapping(value = "/api/equity_center/get_commodity_details", produces = "application/json;charset=utf-8")
    CrmFeignModel<CommodityDetailsVO> getCommodityDetails(@RequestBody CommodityDetailsQO commodityDetailsQO);

    /**
     * 查询门店基础信息
     */
    @PostMapping(value = "/api/sms_store/get_store_data", produces = "application/json;charset=utf-8")
    CrmReturnVo getStoreById(@RequestBody StoreByIdQO storeByIdQO);

    /**
     * 小票打印
     *
     * @return 操作结果
     */
    @PostMapping(value = "/sms_print/api/print_topup", produces = "application/json;charset=utf-8")
    void getReceiptPrinting(@RequestBody ReceiptPrintingBaseDTO receiptPrintingBaseDTO);

    /**
     * 获取配置
     *
     * @return 配置
     */
    @PostMapping(value = "/api/get_pay_setting", produces = "application/json;charset=utf-8")
    CrmReturnVo<PaySettingBaseRes> getPaySetting(@RequestBody PaySettingDTO paySettingDTO);

    @PostMapping(value = "/api/get_commodity/information", produces = "application/json;charset=utf-8")
    CrmCommodityReturnVO<CommodityInfoDTO> getCommodity(@RequestBody CommodityInfoQO commodityInfoQO);

    @PostMapping(value = "/api/get_strategy/information", produces = "application/json;charset=utf-8")
    CrmCommodityReturnVO<StrategyInfoVO> getStrategy(@RequestBody StrategyInfoDTO strategyInfoDTO);

    @PostMapping(value = "/api/get_operating_subject_commodity/information", produces = "application/json;charset=utf-8")
    CrmCommodityReturnVO<ProductCrmVO> getCommodity(@RequestBody QueryProductCrmQO queryProductCrmQO);

    @PostMapping(value = "/api/edit/commodity_information", produces = "application/json;charset=utf-8")
    void editCommodityInformation(@RequestBody UpdateProductCrmQO updateProductCrmQO);

    @PostMapping(value = "/api/edit/commodity_status", produces = "application/json;charset=utf-8")
    Object editCommodityStatus(@RequestBody CrmShelvesQO crmShelvesQO);

    @PostMapping(value = "/api/delete/commodity_information", produces = "application/json;charset=utf-8")
    Object deleteCommodityInformation(@RequestBody CrmDeleteQO crmDeleteQO);

    /**
     * 查看会员详情
     *
     * @return 操作结果
     */
    @GetMapping(value = "/hsa-member/get_detail", produces = "application/json;charset=utf-8")
    Result<OperationMemberDetailVO> getOperationMemberDetail(@RequestParam("guid") String guid);

    /**
     * 查询门店
     *
     * @return 操作结果
     */
    @PostMapping(value = "/api/get_members_store_list")
    CrmFeignModel<StoreBaseInfo> queryStore(@RequestBody QueryStoreBasePage queryStoreBasePage);

    /**
     * 查询档口信息
     * @param queryStallBasePage 查询条件
     * @return 档口信息
     */
    @PostMapping(value = "/api/get_members_stall_list")
    CrmFeignModel<StallBaseInfo> queryBaseStall(@RequestBody QueryStallBasePage queryStallBasePage);

    @PostMapping(value = "/api/get_members_store_list")
    CrmFeignModel<StoreInfoVO> queryStoreV2(@RequestBody QueryStoreBasePage queryStoreBasePage);


    @PostMapping(value = "/api/get_members_stall_list")
    CrmFeignModel<StallBaseAdapterInfo> queryStallV2(@RequestBody QueryStallBasePage queryStallBasePage);
    /**
     * 查询门店
     *
     * @return 操作结果
     */
    @GetMapping(value = "/api/member/get_all_store")
    CrmFeignModel<StoreBaseInfo> queryAllStore();


    /**
     * 查询商品
     *
     * @return 操作结果
     */
    @PostMapping(value = "/order/members/order_list_codes")
    CrmReturnVo<List<ResOrderCommodity>> queryOrderCommodity(@RequestBody QueryOrderCommodity queryOrderCommodity);

    /**
     * 分类查询商品
     */
    @PostMapping(value = "/get/category/commodity")
    CrmOperatingSubjectReturnVO<List<ResCommodityBase>> getCategoryCommodity(@RequestBody QueryArrayShopBase query);

    /**
     * 策略单获取分类
     */
    @PostMapping(value = "/get/category/strategy")
    CrmOperatingSubjectReturnVO<List<ResCategoryBase>> getCategoryStrategy(@RequestBody QueryArrayShopBase query);

    /**
     * 批量商品id获取商品
     */
    @PostMapping(value = "/api/get_all/commodity", produces = "application/json;charset=utf-8")
    CrmOperatingSubjectReturnVO<List<ResCommodityBase>> queryCommodityList(@RequestBody QueryArrayShopBase query);

    /**
     * 查询订单
     *
     * @return 操作结果
     */
    @PostMapping(value = "/order/member/order_detail")
    CrmReturnVo<List<QueryOrderDTO>> queryOrder(@RequestBody QueryOrderCommodity queryOrderCommodity);

    /**
     * 创建订单
     */
    @PostMapping(value = "/api/create_member/order")
    JSONObject pushOrder(@RequestBody CrmOrderDTO crmOrderDTO);

    /**
     * 同步状态 | 同步库存
     */
    @PostMapping(value = "/api/update/sale_order/state", produces = "application/json;charset=utf-8")
    JSONObject pushOrderStock(@RequestBody CrmOrderStockDTO crmOrderStockDTO);

    /**
     * 绑定小程序
     *
     * @param login      手机号
     * @param company_id 企业id
     * @return
     */
    @GetMapping(value = "/api/sms_store/redirect_applet_mgr", produces = "application/json;charset=utf-8")
    CrmReturnVo bindApplet(@RequestParam("guid") String login, @RequestParam("guid") String company_id);

    @PostMapping(value = "/api/module/index", produces = "application/json;charset=utf-8")
    FeignModel<CrmCommodityReturnVo> getCommodityUrl(@RequestBody CrmCommodityReqDTO commodityReqDTO);

    @PostMapping(value = "/api/get_operating_subject", produces = "application/json;charset=utf-8")
    CrmOperatingSubjectReturnVO<CrmOperatingSubjectRespDTO> getOperatingSubject(@RequestBody CrmOperatingSubjectQueryDTO subjectReqDTO);

    @PostMapping(value = "/api/applets/get_appid", produces = "application/json;charset=utf-8")
    CrmReturnVo<AppIdRespDTO> getAppId(@RequestBody CrmAppIdQueryDTO crmAppIdReqDTO);

    @PostMapping(value = "/salestore/api/hall_food/get_strategy", produces = "application/json;charset=utf-8")
    CrmFeignModel<JSONObject> queryCrmProduct(@RequestBody JSONObject query);

    @PostMapping(value = "/salestore/api/orders/refund_agg_pay_order", produces = "application/json;charset=utf-8")
    CrmFeignModel<Void> refundAggPayOrder(@RequestBody CrmRefundPayDTO crmRefundPayDTO);

    /**
     * 查询订单详情
     *
     * @param crmOrderDetailQo 订单号
     * @return
     */
    @PostMapping(value = "/salestore/api/orders/applets/detail", produces = "application/json;charset=utf-8")
    CrmFeignVo<CrmOrderDetailVo> getOrderDetail(CrmOrderDetailQo crmOrderDetailQo);


    /**
     * 查询商品详情  性能更优
     */
    @PostMapping(value = "/salestore/api/member_center/get_commodity_details", produces = "application/json;charset=utf-8")
    CrmFeignVo<List<CommodityDetailsVO>> getNewCommodityDetails(@RequestBody CommodityDetailsQO commodityDetailsQO);

    @PostMapping(value = "/api/applets/pay_success", produces = "application/json;charset=utf-8")
    CrmOperatingSubjectReturnVO<ResAppletOrderCallBack> appletsPayCallBack(@RequestBody AppletOrderCallBack query);

    @PostMapping(value = "/get/strategy/commodity/store", produces = "application/json;charset=utf-8")
    CrmOperatingSubjectReturnVO<List<StoreBaseInfo>> getStoreByStrategyOrCommodity(@RequestBody AppletGrowthStoreQO query);

    @PostMapping(value = "/get/operating/strategy", produces = "application/json;charset=utf-8")
    CrmOperatingSubjectReturnVO<List<ResArrayStrategyBase>> getOperatingStrategy(@RequestBody QueryArrayShopBase queryStoreBasePage);

    @PostMapping(value = "/get/all/store_strategy/commodity", produces = "application/json;charset=utf-8")
    CrmReturnPairVo<Integer,List<ResCommodityBase>> pageStrategyCommodity(@RequestBody QueryCommodityBasePage commodityBasePage);

    @PostMapping(value = "/get/store/commodity", produces = "application/json;charset=utf-8")
    CrmFeignModel<ResGradeCommodityBase> listStoreCommodityPage(@RequestBody GradeCommodityBasePageQO qo);


    /**
     * 敏感字词检验
     */
    @PostMapping(value = "/salestore/api/setting/do_key_words", produces = "application/json;charset=utf-8")
    CrmFeignVo<CheckContentVO> checkKeyWords(@RequestBody CheckContentQO checkContentQO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<CrmFeign> {

        @Override
        public CrmFeign create(Throwable throwable) {
            return new CrmFeign() {

                @Override
                public CrmFeignModel<ProductCrmVO> getCommodity(QueryProductCategoryCrmQO queryProductCrmQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCommodity", JacksonUtils.writeValueAsString(queryProductCrmQO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignModel<ProductCrmCategoryVO> getCommodityCategory(QueryProductCategoryCrmQO queryProductCrmQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCommodityCategory", JacksonUtils.writeValueAsString(queryProductCrmQO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmCommodityReturnVO<StoreInfoVO> getStoreStall(StoreInfoDTO storeInfoDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getStoreStall", JacksonUtils.writeValueAsString(storeInfoDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignModel<OperSubjectVO> listOperSubjectAndApplet() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listOperSubjectAndApplet", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignModel<CategoryVO> getCommodityCategory(CategoryQO categoryQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCommodityCategory", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignModel<CommodityDetailsVO> getCommodityDetails(CommodityDetailsQO commodityDetailsQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCommodityDetails", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmReturnVo getStoreById(StoreByIdQO storeByIdQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "storeByIdQO", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void getReceiptPrinting(ReceiptPrintingBaseDTO receiptPrintingBaseDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getReceiptPrinting", receiptPrintingBaseDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmReturnVo<PaySettingBaseRes> getPaySetting(PaySettingDTO paySettingDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "aggPaySettingBaseDTO", paySettingDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmCommodityReturnVO<CommodityInfoDTO> getCommodity(CommodityInfoQO commodityInfoQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "commodityInfoQO", commodityInfoQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmCommodityReturnVO<StrategyInfoVO> getStrategy(StrategyInfoDTO strategyInfoDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "strategyInfoDTO", strategyInfoDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmCommodityReturnVO<ProductCrmVO> getCommodity(QueryProductCrmQO queryProductCrmQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCommodity", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void editCommodityInformation(UpdateProductCrmQO updateProductCrmQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "editCommodityInformation", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmCommodityReturnVO editCommodityStatus(CrmShelvesQO crmShelvesQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "editCommodityStatus", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Object deleteCommodityInformation(CrmDeleteQO crmDeleteQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "deleteCommodityInformation", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result getOperationMemberDetail(String guid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getOperationMemberDetail", guid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignModel<StoreBaseInfo> queryStore(QueryStoreBasePage queryStoreBasePage) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryStore", queryStoreBasePage, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignModel<StoreInfoVO> queryStoreV2(QueryStoreBasePage queryStoreBasePage) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryStoreV2", queryStoreBasePage, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignModel<StallBaseAdapterInfo> queryStallV2(QueryStallBasePage queryStallBasePage) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryStall", queryStallBasePage, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignModel<StoreBaseInfo> queryAllStore() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryAllStore", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmReturnVo<List<ResOrderCommodity>> queryOrderCommodity(QueryOrderCommodity queryOrderCommodity) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryOrderCommodity", queryOrderCommodity, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmOperatingSubjectReturnVO<List<ResCommodityBase>> getCategoryCommodity(QueryArrayShopBase query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCategoryCommodity", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmOperatingSubjectReturnVO<List<ResCategoryBase>> getCategoryStrategy(QueryArrayShopBase query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCategoryStrategy", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmOperatingSubjectReturnVO<List<ResCommodityBase>> queryCommodityList(QueryArrayShopBase query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryCommodityList", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmReturnVo<List<QueryOrderDTO>> queryOrder(QueryOrderCommodity queryOrderCommodity) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryOrder", queryOrderCommodity, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public JSONObject pushOrder(CrmOrderDTO crmOrderDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pushOrder", crmOrderDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public JSONObject pushOrderStock(CrmOrderStockDTO crmOrderStockDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pushOrderStock", crmOrderStockDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmReturnVo bindApplet(String login, String company_id) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "bindApplet", login + "," + company_id, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public FeignModel<CrmCommodityReturnVo> getCommodityUrl(CrmCommodityReqDTO commodityReqDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCommodityUrl", JacksonUtils.writeValueAsString(commodityReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmOperatingSubjectReturnVO<CrmOperatingSubjectRespDTO> getOperatingSubject(CrmOperatingSubjectQueryDTO subjectReqDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getOperatingSubject", JacksonUtils.writeValueAsString(subjectReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmReturnVo<AppIdRespDTO> getAppId(CrmAppIdQueryDTO crmAppIdReqDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAppId", JacksonUtils.writeValueAsString(crmAppIdReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignModel<JSONObject> queryCrmProduct(JSONObject query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryCrmProduct", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignModel<Void> refundAggPayOrder(CrmRefundPayDTO crmRefundPayDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "refundAggPayOrder", JacksonUtils.writeValueAsString(crmRefundPayDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignVo<CrmOrderDetailVo> getOrderDetail(CrmOrderDetailQo crmOrderDetailQo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getOrderDetail", JacksonUtils.writeValueAsString(crmOrderDetailQo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignVo<List<CommodityDetailsVO>> getNewCommodityDetails(CommodityDetailsQO commodityDetailsQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getNewCommodityDetails", JacksonUtils.writeValueAsString(commodityDetailsQO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignModel<StallBaseInfo> queryBaseStall(QueryStallBasePage queryStallBasePage) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryStall", JacksonUtils.writeValueAsString(queryStallBasePage),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmOperatingSubjectReturnVO<ResAppletOrderCallBack> appletsPayCallBack(AppletOrderCallBack query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "appletsPayCallBack", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmOperatingSubjectReturnVO<List<StoreBaseInfo>> getStoreByStrategyOrCommodity(AppletGrowthStoreQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getStoreByStrategyOrCommodity", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmOperatingSubjectReturnVO<List<ResArrayStrategyBase>> getOperatingStrategy(QueryArrayShopBase queryStoreBasePage) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getOperatingStrategy", JacksonUtils.writeValueAsString(queryStoreBasePage),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmReturnPairVo<Integer, List<ResCommodityBase>> pageStrategyCommodity(QueryCommodityBasePage commodityBasePage) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageStrategyCommodity", JacksonUtils.writeValueAsString(commodityBasePage),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignModel<ResGradeCommodityBase> listStoreCommodityPage(GradeCommodityBasePageQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listStoreCommodityPage", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CrmFeignVo<CheckContentVO> checkKeyWords(CheckContentQO checkContentQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "checkKeyWords", JacksonUtils.writeValueAsString(checkContentQO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
