package com.holderzone.member.common.enums.specials;

/**
 * <AUTHOR>
 * @date 2024/5/22
 * @description 模版类型枚举
 */
public enum TemplateTypeEnum {

    MEMBER(1, "会员"),

    ITEM(2, "商品"),

    ;


    private final int code;

    private final String des;

    TemplateTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return this.code;
    }

    public String getDes() {
        return this.des;
    }

}
