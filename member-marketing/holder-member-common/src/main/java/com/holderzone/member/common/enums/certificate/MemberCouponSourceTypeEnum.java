package com.holderzone.member.common.enums.certificate;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 * @date 2023/12/5
 * @description 会员持券领取方式枚举
 */
@Getter
@AllArgsConstructor
public enum MemberCouponSourceTypeEnum {

    INTEGRAL_EXCHANGE(0, "积分兑换"),

    ACCURATE_PUSH(1, "精准推送"),

    BIRTHDAY_PRESENT(2, "生日赠送"),

    RECHARGE_PRESENT(3, "充值赠送"),

    CONSUME_PRESENT(4, "消费赠送"),

    UPGRADE_PRESENT(5, "升级赠送(会员卡)"),

    ACTIVITY_PRESENT(6, "活动赠送"),

    VOLUME_CENTER(7, "领券中心"),

    VOLUME_RIGHTS(8, "开卡赠送(权益卡)"),

    CERTIFIED_POLITE(9, "认证有礼"),

    ALIPAY_APPLET(10, "支付宝小程序"),

    CONSUMPTION_GIFT(11, "消费有礼"),

    OTHER(-1, "其他");

    private final Integer type;

    private final String name;

    public static MemberCouponSourceTypeEnum valueOf(Integer type) {
        if (type == null) {
            return OTHER;
        }
        for (MemberCouponSourceTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return OTHER;
    }

}
