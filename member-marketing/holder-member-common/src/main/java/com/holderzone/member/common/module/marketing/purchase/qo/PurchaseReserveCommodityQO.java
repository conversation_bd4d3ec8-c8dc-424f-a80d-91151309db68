package com.holderzone.member.common.module.marketing.purchase.qo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 运行中限购活动
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PurchaseReserveCommodityQO extends PurchaseApplyCommodityQo implements Serializable {


    private static final long serialVersionUID = 3192018531796399080L;
    /**
     * 活动Guid
     */
    private List<String> purchaseList;

}
