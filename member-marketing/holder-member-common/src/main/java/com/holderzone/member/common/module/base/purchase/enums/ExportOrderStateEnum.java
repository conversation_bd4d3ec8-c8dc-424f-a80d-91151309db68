package com.holderzone.member.common.module.base.purchase.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 限购导出订单状态
 *
 * <AUTHOR>
 * @date 2023/12/01
 **/
@Getter
@AllArgsConstructor
public enum ExportOrderStateEnum {


    /**
     * 订单退款
     */
    CREATE(2, "订单退款"),

    /**
     * 已结账
     */
    PAY(1, "已结账");

    private final int code;

    private final String des;

    public static ExportOrderStateEnum get(Integer code) {
        for (ExportOrderStateEnum anEnum : ExportOrderStateEnum.values()) {
            if (Objects.equals(anEnum.code, code)) {
                return anEnum;
            }
        }
        return null;
    }
}
