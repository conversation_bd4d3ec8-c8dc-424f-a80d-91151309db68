package com.holderzone.member.common.module.settlement.rule.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountItemEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 结算规则已选优惠项
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementRuleDiscountDTO implements Serializable {

    private static final long serialVersionUID = 1425698029256667299L;
    /**
     * 优惠结算项
     */
    @ApiModelProperty(value = "结算优惠项guid,")
    private String guid;

    /**
     * 优惠guid
     */
    @ApiModelProperty(value = "优惠guid")
    private String discountGuid;

    /**
     * 优惠项大类：前端展示
     *
     * @see SettlementDiscountItemEnum
     */
    @ApiModelProperty(value = "优惠项： SettlementDiscountItemEnum")
    private Integer discountItem;

    /**
     * 优惠具体优惠项
     */
    @ApiModelProperty(value = "优惠具体优惠项： SettlementDiscountOptionEnum")
    private Integer discountOption;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer rank;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer discountNum;

    /**
     * 原数量map
     */
    @ApiModelProperty(value = "原数量map")
    private Map<String, Map<String, Integer>> settlementRuleMap;

    /**
     * 可叠加优惠项，子级
     */
    private List<SettlementRuleDiscountDTO> children;

    /**
     * 是否首次新增
     */
    private Integer isFirstAdd;
}
