package com.holderzone.member.common.enums.exception;

import com.holderzone.member.common.enums.ResponseBase;


/**
 * MemberMallTool异常枚举
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
public enum MemberMallToolExceptionEnum implements ResponseBase {

    INDUSTRY_NAME_EXISTS(5000, "行业名称已存在，请重新输入"),

    SYSTEM_REMARK_EXISTS(5001, "系统备注已存在，请重新输入"),

    SYSTEM_AUTH_EXISTS(5002, "品牌不可重复授权，请重新选择"),

    INDUSTRY_NOT_EXISTS(5003, "行业不存在，请重新选择"),

    SYSTEM_NOT_EXISTS(5004, "系统不存在，请重新选择"),

    SYSTEM_AUTH_ACCOUNT_PASSWORD_ERROR(5005, "账号或密码错误"),

    SYSTEM_AUTH_SAME_OPER_SUBJECT(5006, "不能授权相同品牌，请重新选择"),
    ;

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误描述
     */
    private final String des;

    MemberMallToolExceptionEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDes() {
        return des;
    }
}
