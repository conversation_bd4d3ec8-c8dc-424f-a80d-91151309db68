package com.holderzone.member.common.module.marketing.coupon.use.qo;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.enums.coupon.CouponTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class CouponActivityDTO  implements Serializable {


    private String guid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 活动主题名称
     */
    private String couponName;

    /**
     * 优惠券编码
     */
    private String couponCode;

    /**
     * 优惠券类型
     *
     * @see CouponTypeEnum
     */
    private Integer couponType;

    /**
     * 活动状态
     *
     * @see com.holderzone.member.common.enums.coupon.CouponActivityStateEnum
     */
    private Integer activityState;

    /**
     * 可兑换次数 (商品券存在)
     */
    private Integer exchangeTimes;

    /**
     * 兑换次数限制
     */
    private Integer exchangeLimit;

    /**
     * 使用门槛
     * 0 无门槛
     * 1 订单满多少可用
     */
    private Integer thresholdType;

    /**
     * 满足金额
     */
    private BigDecimal thresholdAmount;

    /**
     * 优惠力度
     */
    private BigDecimal discountAmount;

    /**
     * 优惠金额上限
     * null/0 表示不限制
     */
    private BigDecimal discountAmountLimit;

    /**
     * 活动备注
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String remark;


    /**
     * 使用有效期类型
     * 0 自定义固定时间（开启时需判断是否失效）
     * 1 领取后天/小时生效，有效期多少天/小时（开启时无需判断是否失效）
     * 2 领取后天/小时生效，有效期固定时间（开启时需判断是否失效）
     */
    private Integer effectiveType;

    /**
     * 领取后时间值
     */
    private Integer afterValue;

    /**
     * 领取后时间单位 0 小时 1 天
     *
     * @see com.holderzone.member.common.enums.gift.GiftEffectiveUnitEnum
     */
    private Integer afterUnit;

    /**
     * 有效期时间值
     */
    private Integer effectiveValue;

    /**
     * 领取后时间单位 0 小时 1 天
     *
     * @see com.holderzone.member.common.enums.gift.GiftEffectiveUnitEnum
     */
    private Integer effectiveUnit;


    /**
     * 优惠券有效开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponEffectiveStartTime;

    /**
     * 优惠券有效结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime couponEffectiveEndTime;

    /**
     * 用券时段限制 0：不限制 1：限制
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyDateLimited;

    /**
     * 限制时段类型 -1:自定义 0：日 1：周 2：月 3：年
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer applyTimeLimitedType;

    /**
     * 限制时段限制类型json
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyTimeLimitedJson;

    /**
     * 适用场景 0:全部业务 1：部分业务
     *
     * @see com.holderzone.member.common.enums.equities.ApplyBusinessTypeEnum
     */
    private Integer applyBusiness;

    /**
     * 适用场景json
     */
    private String applyBusinessJson;

    /**
     * 适用终端json
     */
    private String applyTerminalJson;

    /**
     * 打标标签guid
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String applyLabelGuidJson;

    /**
     * 是否适用于所有门店(1:全部门店；0:部分门店(外关联表))
     */
    private Integer applicableAllStore;

    /**
     * 0：全部商品适用 1：部分商品适用 2 不适用商品
     */
    private Integer applyCommodity;

    /**
     * 单笔订单限用数量
     * 单笔订单最多使用{singleOrderUsedLimit}张此优惠券
     * 当前优惠券限用数量需 ≤ 结算规则限制数量
     */
    private Integer singleOrderUsedLimit;

    /**
     * 共享互斥关系 0互斥 1共享
     */
    private Integer shareRelation;

    /**
     * 是否发布过
     */
    private Boolean isPublished;
}
