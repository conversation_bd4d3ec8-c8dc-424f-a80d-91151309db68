package com.holderzone.member.common.module.marketing.purchase.enums;

import com.holderzone.member.common.util.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 限购活动周期类型
 *
 * <AUTHOR>
 * @date 2023/11/27
 **/
@Getter
@AllArgsConstructor
public enum PurchaseRulePeriodEnum {

    /**
     * 每日/人
     */
    DAY(0, "每日/人"),

    /**
     * 每周/人
     */
    WEEK(1, "每周/人"),

    /**
     * 每月/人
     */
    MONTH(2, "每月/人");

    private final int code;

    private final String des;

    /**
     * 获取枚举对象
     *
     * @param code 枚举code
     * @return 对象
     */
    public static PurchaseRulePeriodEnum get(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (PurchaseRulePeriodEnum anEnum : PurchaseRulePeriodEnum.values()) {
            if (Objects.equals(anEnum.code, code)) {
                return anEnum;
            }
        }
        return null;
    }

    /**
     * 获取限购周期时间
     *
     * @return 时间段
     */
    public LocalDateTime[] getTime() {
        LocalDate now = LocalDate.now();
        // 每日
        if (code == DAY.getCode()) {
            return DateUtil.getDayTime(now);
        }
        // 每周
        if (code == WEEK.getCode()) {
            return DateUtil.getWeekDays(now);
        }
        // 每月
        return DateUtil.getMonthDays(now);

    }

}
