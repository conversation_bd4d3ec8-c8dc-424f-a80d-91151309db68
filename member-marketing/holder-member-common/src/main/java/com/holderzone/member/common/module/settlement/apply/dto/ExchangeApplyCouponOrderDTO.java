package com.holderzone.member.common.module.settlement.apply.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;


/**
 * 结算规则应用订单数据
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExchangeApplyCouponOrderDTO implements Serializable {

    private static final long serialVersionUID = 3212351245090146002L;

    /**
     * 后端使用
     * eg：会员xx已领优惠券1的id
     */
    private String discountOptionIds;

    /**
     * 使用次数(前端)
     */
    private Integer requiresTimes;
}
