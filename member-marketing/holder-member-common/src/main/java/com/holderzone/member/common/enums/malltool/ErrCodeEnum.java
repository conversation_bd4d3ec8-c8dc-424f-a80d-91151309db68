package com.holderzone.member.common.enums.malltool;

import java.util.Objects;

/**
 * 消息类型
 */
public enum ErrCodeEnum {

    ERR_ACCESS_TOKEN(40001, "获取 access_token 时 AppSecret 错误，或者 access_token 无效。请开发者认真比对 AppSecret 的正确性，或查看是否正在为恰当的公众号调用接口"),


    ERR_NUMBER(200012, "个人模板数已达上限，上限25个"),


    ERR_TID(200014, "模板 tid 参数错误"),


    ERR_KID_LIST(200020, "关键词列表 kidList 参数错误"),


    ERR_SCENE_DESC(200021, "场景描述 sceneDesc 参数错误");

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return desc;
    }

    ErrCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ErrCodeEnum getEnumByName(Integer code) {

        if (Objects.isNull(code)) {
            return null;
        }
        for (ErrCodeEnum pageTypeEnum : ErrCodeEnum.values()) {
            if (Objects.equals(pageTypeEnum.getCode(), code)) {
                return pageTypeEnum;
            }
        }
        return null;
    }
}
