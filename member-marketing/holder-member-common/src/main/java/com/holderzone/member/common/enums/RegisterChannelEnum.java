package com.holderzone.member.common.enums;

/**
 * @ProjectName: member-marketing
 * @ClassName: RegisterChannelEnum
 * @Author: pantao
 * @Description: 注册渠道枚举
 * @Date: 2021/8/12 16:22
 * @Version: 1.0
 */
public enum RegisterChannelEnum {
    POS_ADD(1, "POS机注册"),
    WX_SCAN_QR_ORDERED(21, "微网站(微信扫码点餐)"),
    WX_REGISTER(24, "微网站(微信扫码点餐)"),
    WX_C_BACK_REGISTER(25, "微网站(微信C端后台注册)"),

    HE_HUI_DUO(51, "微信小程序(和惠多)"),
    YI_HUI_TIAN_XIA(52, "微信小程序"),
    ZHUAN_CAN(53, "微信小程序"),

    // 以下为在用的注册渠道
    BACK_ADD(0, "管理后台"),
    MARKET_POS(2, "零售云-POS"),
    BACK_IMPORT(3, "后台导入"),
    /**
     * 私域商城
     */
    MALL_PC(70, "私域商城-PC"),
    MALL_H5(71, "私域商城-H5"),
    MALL_WECHAT_APPLET(72, "微信小程序"),
    /**
     * 餐饮云
     */
    REPAST_WECHAT_APPLET(80, "餐饮云-微信小程序"),
    REPAST_ALI_APPLET(81, "餐饮云-支付宝小程序"),
    REPAST_H5(82, "餐饮云-H5"),
    REPAST_AIO(83, "餐饮云-一体机"),
    REPAST_POS(84, "餐饮云-POS"),
    REPAST_PAD(85, "餐饮云-PAD"),
    REPAST_WECHAT_PUBLIC(86, "餐饮云-微信公众号"),
    REPAST_DIAN_PING(87, "餐饮云-大众点评"),
    ;
    private int code;

    private String des;

    RegisterChannelEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    /**
     * 根据code获取name
     *
     * @param code code
     * @return 操作结果
     */
    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
         }

        for (RegisterChannelEnum titleEnum : RegisterChannelEnum.values()) {
            if (titleEnum.getCode() == code) {
                return titleEnum.getDes();
            }
        }

        return null;
    }

}
