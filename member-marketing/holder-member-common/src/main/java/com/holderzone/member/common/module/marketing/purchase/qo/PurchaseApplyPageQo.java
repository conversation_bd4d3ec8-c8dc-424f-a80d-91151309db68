package com.holderzone.member.common.module.marketing.purchase.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.module.base.purchase.enums.ConsumptionOrderStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 限购活动列表查询
 *
 * <AUTHOR>
 * @date 2023/12/01
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseApplyPageQo extends PageDTO implements Serializable {

    private static final long serialVersionUID = 6480851653375152230L;
    /**
     * 主体
     */
    private String operSubjectGuid;

    /**
     * 根据活动ID/活动名称/下单用户/手机号模糊查询
     */
    private String keywords;


    /**
     * 订单开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 订单结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityEndTime;
    
    /**
     * 订单状态
     * @see ConsumptionOrderStateEnum
     */
    private Integer orderState;
}
