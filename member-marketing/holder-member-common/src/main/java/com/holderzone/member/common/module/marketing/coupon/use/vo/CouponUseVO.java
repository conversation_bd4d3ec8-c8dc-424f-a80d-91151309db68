package com.holderzone.member.common.module.marketing.coupon.use.vo;


import com.holderzone.member.common.vo.coupon.CouponGiveRuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 优惠劵核销明细
 * 规则
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class CouponUseVO extends CouponUseBaseVO {


    private static final long serialVersionUID = -7566442040476543108L;
    /**
     * 优惠劵规则
     */
    private CouponGiveRuleVO ruleVO;
}
