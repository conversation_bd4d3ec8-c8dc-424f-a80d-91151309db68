package com.holderzone.member.common.enums.integral;


/**
 * 单笔使用上限类型
 * 0 不限制 1 上限金额 2 上限比例
 *
 * <AUTHOR>
 */

public enum SingleOrderTypeEnum {

    /**
     * 不限制
     */
    UNRESTRICTED(0, "不限制"),

    /**
     * 上限金额
     */
    UPPER_LIMIT_AMOUNT(1, "上限金额"),

    /**
     * 上限比例
     */
    UPPER_LIMIT_RATIO(2, "上限比例"),
    ;
    private final int code;

    private final String des;

    SingleOrderTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

}
