package com.holderzone.member.common.external;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.util.Page;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.dto.crm.*;
import com.holderzone.member.common.dto.grade.StoreInfoDTO;
import com.holderzone.member.common.dto.order.CrmOrderDTO;
import com.holderzone.member.common.dto.order.CrmOrderStockDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.crm.CrmOrderDetailQo;
import com.holderzone.member.common.qo.crm.CrmOrderDetailVo;
import com.holderzone.member.common.qo.growth.AppletGrowthStoreQO;
import com.holderzone.member.common.qo.tool.StoreByIdQO;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import com.holderzone.member.common.vo.tool.OperSubjectVO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description 外部服务支撑接口
 */
public interface ExternalStoreService {

    /**
     * 查询门店列表
     * @param query 查询条件
     * @return 门店列表
     */
    List<StoreBaseInfo> listStore(QueryStoreBasePage query);


    /**
     * 查询门店列表分页
     * @param query 查询条件
     * @return 门店列表
     */
    Page<StoreBaseInfo> storePage (QueryStoreBasePage query);

    /**
     * 查询门店以及档口列表
     * @param query 查询条件
     * @return 门店以及档口列表
     */
    List<StoreBaseInfo> listStoreAndStall(QueryStoreBasePage query);

    /**
     * 查询小程序信息
     * @param crmAppIdReqDTO 请求参数
     * @return 小程序信息
     */
    AppIdRespDTO getAppId(CrmAppIdQueryDTO crmAppIdReqDTO);

    /**
     * 小程序支付回调系统
     * @param query 入参
     * @return 返回参数
     */
    ResAppletOrderCallBack appletOrderCallBack(AppletOrderCallBack query);

    /**
     * 小程序通过商品id或者策略单id查询门店
     * @param query 入参
     * @return 门店列表
     */
    List<StoreBaseInfo> getStoreByStrategyOrCommodity(AppletGrowthStoreQO query);


    /**
     * 查询支付配置 TODO 后续功能完善需删除
     * @param paySettingDTO 请求参数
     * @return 支付设置
     */
    PaySettingBaseRes getPaySetting(PaySettingDTO paySettingDTO);

    /**
     * 请求门店退款
     * @param crmRefundPayDTO 请求参数
     * @return 返回结果
     */
    int refundAggPayOrder(CrmRefundPayDTO crmRefundPayDTO);

    /**
     * 打印小票
     * @param receiptPrintingBaseDTO 请求参数
     */
    void getReceiptPrinting(ReceiptPrintingBaseDTO receiptPrintingBaseDTO);

    /**
     * 门店以及档口列表
     * @param storeInfoDTO 请求参数
     * @return 列表
     */
    List<StoreInfoVO> listStoreStall(StoreInfoDTO storeInfoDTO);

    /**
     * 根据小程序id查询门店对应的运营主体 TODO 后续功能完善需删除
     * @param subjectReqDTO 入参
     * @return 运营主体信息
     */
    CrmOperatingSubjectRespDTO getOperatingSubject(CrmOperatingSubjectQueryDTO subjectReqDTO);

    /**
     * 查询订单详情
     * @param crmOrderDetailQo 查询入参
     * @return 返回订单参数
     */
    CrmOrderDetailVo getOrderDetail(CrmOrderDetailQo crmOrderDetailQo);

    /**
     * 查询订单商品
     * @param queryOrderCommodity 查询入参
     * @return 订单商品列表
     */
    List<ResOrderCommodity> queryOrderCommodity(QueryOrderCommodity queryOrderCommodity);

    /**
     * @description: 查询主体下的店铺和档口
     * @author: li ao
     * @date: 2024/3/18 14:48
     * @param: query
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.vo.grade.StoreInfoVO>
     **/
    List<StoreInfoVO> getStoreStall(StoreInfoDTO query);

    /**
     * @description: 查询门店基础信息
     * @author: li ao
     * @date: 2024/3/18 14:56
     * @param: storageByIdQuery
     * @return: java.lang.Object
     **/
    Object getStoreById(StoreByIdQO storageByIdQuery);


    /**
     * @description: 查询主体下所有门店
     * @author: li ao
     * @date: 2024/3/18 15:15
     * @param: query
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.vo.grade.StoreInfoVO>
     **/
    List<StoreInfoVO> queryStoreV2(QueryStoreBasePage query);

    /**
     * @description: 查询门店
     * @author: li ao
     * @date: 2024/3/18 15:24
     * @param: queryStoreBasePage
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.dto.base.StoreBaseInfo>
     **/
    List<StoreBaseInfo> queryStore(QueryStoreBasePage queryStoreBasePage);


    /**
     * @description: 创建订单
     * @author: li ao
     * @date: 2024/3/18 16:19
     * @param: crmOrderDTO
     * @return: com.alibaba.fastjson.JSONObject
     **/
    JSONObject pushOrder(CrmOrderDTO crmOrderDTO);

    /**
     * @description: 同步状态 | 同步库存
     * @author: li ao
     * @date: 2024/3/18 16:20
     * @param: crmOrderStockDTO
     * @return: com.alibaba.fastjson.JSONObject
     **/
    JSONObject pushOrderStock(CrmOrderStockDTO crmOrderStockDTO);


    List<StoreBaseInfo> queryStore(String name);




    /**
     * @description: 查询运营主体列表
     * @author: li ao
     * @date: 2024/3/18 15:02
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.vo.tool.OperSubjectVO>
     **/
    List<OperSubjectVO> listOperSubjectAndApplet();


    /**
     * @description: 查询订单
     * @param: queryOrderCommodity
     * @return: com.holderzone.member.common.vo.feign.CrmReturnVo<java.util.List < com.holderzone.member.common.dto.base.QueryOrderDTO>>
     **/
    List<QueryOrderDTO> queryOrder(QueryOrderCommodity queryOrderCommodity);

    /**
     * 统计店铺数量
     * @return 店铺数量统计信息
     */
    StoreCountDTO countStores();
}
