package com.holderzone.member.common.external;

import com.holderzone.framework.util.Page;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.dto.crm.CrmCommodityReqDTO;
import com.holderzone.member.common.dto.excel.ItemUploadVO;
import com.holderzone.member.common.dto.grade.CommodityInfoDTO;
import com.holderzone.member.common.dto.grade.StrategyInfoDTO;
import com.holderzone.member.common.qo.base.CommodityBasePageQO;
import com.holderzone.member.common.qo.grade.CommodityInfoQO;
import com.holderzone.member.common.qo.grade.GradeCommodityBasePageQO;
import com.holderzone.member.common.qo.mall.QueryProductCategoryCrmQO;
import com.holderzone.member.common.qo.tool.CommodityDetailsQO;
import com.holderzone.member.common.vo.base.CommodityBaseTypeVO;
import com.holderzone.member.common.vo.base.CommodityBaseVO;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.grade.StrategyInfoVO;
import com.holderzone.member.common.vo.mall.ProductCrmCategoryVO;
import com.holderzone.member.common.vo.tool.CommodityDetailsVO;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description 外部服务支撑接口
 */
public interface ExternalItemService {

    /**
     * 根据门店、渠道查询策略单
     *
     * @param queryStoreBasePage 查询条件
     * @return 策略单列表
     */
    List<ResArrayStrategyBase> getOperatingStrategy(QueryArrayShopBase queryStoreBasePage);

    /**
     * 根据策略单id查询分类
     *
     * @param queryStoreBasePage 策略单id
     * @return 分类列表
     */
    List<ResCategoryBase> queryCategoryByStrategy(QueryArrayShopBase queryStoreBasePage);

    /**
     * 查询策略单商品分页
     *
     * @param commodityBasePage 商品分页查询入参
     * @return 分页数据
     */
    Pair<Integer, List<ResCommodityBase>> pageStrategyCommodity(QueryCommodityBasePage commodityBasePage);

    /**
     * 根据策略单和分类查询商品列表
     *
     * @param queryStoreBasePage 查询条件
     * @return 商品列表
     */
    List<ResCommodityBase> listCommodityBase(QueryArrayShopBase queryStoreBasePage);


    /**
     * 根据商品id查询商品信息
     *
     * @param queryStoreBasePage 查询条件
     * @return 商品列表
     */
    List<ResCommodityBase> listCommodityByDetail(QueryArrayShopBase queryStoreBasePage);


    /**
     * 查询门店商品分页
     *
     * @param pageQO 商品分页查询入参
     * @return 分页数据
     */
    Pair<Integer, List<ResGradeCommodityBase>> listStoreCommodityPage(GradeCommodityBasePageQO pageQO);

    /**
     * 查询商品分页
     */
    Page<CommodityBaseVO> pageCommodityBase(CommodityBasePageQO pageQO);


    List<CommodityBaseTypeVO> getStoreGoodsComboType();

    /**
     * 查询商品列表
     *
     * @param commodityInfoQO 请求参数
     * @return 商品列表
     */
    List<CommodityInfoDTO> listCommodity(CommodityInfoQO commodityInfoQO);

    /**
     * 查询商品列表分页参数
     *
     * @param commodityInfoQO 请求参数
     * @return 商品列表以及数量
     */
    CrmFeignModel<CommodityInfoDTO> listCommodityHasCount(CommodityInfoQO commodityInfoQO);

    /**
     * 策略单列表
     *
     * @param strategyInfoDTO 请求参数
     * @return 策略单列表
     */
    List<StrategyInfoVO> listStrategyInfo(StrategyInfoDTO strategyInfoDTO);

    /**
     * @description: 获取商品url
     * @author: li ao
     * @date: 2024/3/18 14:10
     * @param: reqDTO
     * @return: com.holderzone.member.common.vo.feign.FeignModel<com.holderzone.member.common.dto.crm.CrmCommodityReturnVo>
     **/
    String getCommodityUrl(CrmCommodityReqDTO reqDTO);

    /**
     * @description: 批量查询商品详情
     * @author: li ao
     * @date: 2024/3/18 14:18
     * @param: detailsQO
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.vo.tool.CommodityDetailsVO>
     **/
    List<CommodityDetailsVO> getCommodityDetails(CommodityDetailsQO detailsQO);

    /**
     * @description: 批量查询商品详情
     * @author: li ao
     * @date: 2024/3/18 14:18
     * @param: detailsQO
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.vo.tool.CommodityDetailsVO>
     **/
    List<CommodityDetailsVO> getNewCommodityDetails(CommodityDetailsQO detailsQO);


    /**
     * @description: 获取策略单分类接口
     * @author: li ao
     * @date: 2024/3/18 15:11
     * @param: categoryCrmQO
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.vo.mall.ProductCrmCategoryVO>
     **/
    List<ProductCrmCategoryVO> getCommodityCategory(QueryProductCategoryCrmQO categoryCrmQO);

    ItemUploadVO itemUploadExcelUrl(String fileUrl, Integer activityType);
}
