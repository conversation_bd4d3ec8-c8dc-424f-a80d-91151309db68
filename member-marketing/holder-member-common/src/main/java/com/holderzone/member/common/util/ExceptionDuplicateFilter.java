package com.holderzone.member.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 异常去重过滤器
 * 防止相同异常在短时间内重复打印堆栈信息
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Component
public class ExceptionDuplicateFilter {

    /**
     * 异常指纹缓存，key为异常指纹，value为异常信息
     */
    private final ConcurrentHashMap<String, ExceptionRecord> exceptionCache = new ConcurrentHashMap<>();
    
    /**
     * 默认时间窗口：5分钟
     */
    private static final long DEFAULT_TIME_WINDOW_MS = 5 * 60 * 1000L;
    
    /**
     * 清理任务执行器
     */
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "exception-cleanup");
        t.setDaemon(true);
        return t;
    });

    public ExceptionDuplicateFilter() {
        // 每分钟清理一次过期的异常记录
        cleanupExecutor.scheduleAtFixedRate(this::cleanupExpiredRecords, 1, 1, TimeUnit.MINUTES);
    }

    /**
     * 检查异常是否应该记录完整堆栈
     * 
     * @param exception 异常对象
     * @return true-应该记录完整堆栈，false-只记录简要信息
     */
    public boolean shouldLogFullStack(Throwable exception) {
        return shouldLogFullStack(exception, DEFAULT_TIME_WINDOW_MS);
    }

    /**
     * 检查异常是否应该记录完整堆栈
     * 
     * @param exception 异常对象
     * @param timeWindowMs 时间窗口（毫秒）
     * @return true-应该记录完整堆栈，false-只记录简要信息
     */
    public boolean shouldLogFullStack(Throwable exception, long timeWindowMs) {
        String fingerprint = generateExceptionFingerprint(exception);
        long currentTime = System.currentTimeMillis();
        
        ExceptionRecord record = exceptionCache.get(fingerprint);
        
        if (record == null) {
            // 首次出现的异常，记录并返回true
            exceptionCache.put(fingerprint, new ExceptionRecord(currentTime, 1, exception.getMessage()));
            return true;
        }
        
        // 检查是否在时间窗口内
        if (currentTime - record.getFirstOccurrence() < timeWindowMs) {
            // 在时间窗口内，增加计数但不记录完整堆栈
            record.incrementCount();
            record.setLastOccurrence(currentTime);
            return false;
        } else {
            // 超出时间窗口，重新记录
            exceptionCache.put(fingerprint, new ExceptionRecord(currentTime, 1, exception.getMessage()));
            return true;
        }
    }

    /**
     * 获取异常的重复次数
     * 
     * @param exception 异常对象
     * @return 重复次数
     */
    public int getExceptionCount(Throwable exception) {
        String fingerprint = generateExceptionFingerprint(exception);
        ExceptionRecord record = exceptionCache.get(fingerprint);
        return record != null ? record.getCount() : 0;
    }

    /**
     * 生成异常指纹
     * 基于异常类型、消息和关键堆栈信息生成唯一标识
     * 
     * @param exception 异常对象
     * @return 异常指纹
     */
    private String generateExceptionFingerprint(Throwable exception) {
        StringBuilder sb = new StringBuilder();
        
        // 异常类型
        sb.append(exception.getClass().getName());
        
        // 异常消息（去除可能的动态内容）
        String message = exception.getMessage();
        if (message != null) {
            // 简单的消息标准化，去除数字和特殊字符
            String normalizedMessage = message.replaceAll("\\d+", "X")
                                             .replaceAll("['\"].*?['\"]", "\"X\"")
                                             .replaceAll("\\b\\w{8}-\\w{4}-\\w{4}-\\w{4}-\\w{12}\\b", "UUID");
            sb.append(":").append(normalizedMessage);
        }
        
        // 关键堆栈信息（取前3层非系统调用栈）
        StackTraceElement[] stackTrace = exception.getStackTrace();
        int count = 0;
        for (StackTraceElement element : stackTrace) {
            String className = element.getClassName();
            // 跳过系统类和代理类
            if (!className.startsWith("java.") && 
                !className.startsWith("javax.") && 
                !className.startsWith("sun.") && 
                !className.contains("$$") &&
                count < 3) {
                sb.append("|").append(className).append(".").append(element.getMethodName());
                count++;
            }
        }
        
        // 生成MD5哈希
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(sb.toString().getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            // 如果MD5不可用，使用hashCode
            return String.valueOf(sb.toString().hashCode());
        }
    }

    /**
     * 清理过期的异常记录
     */
    private void cleanupExpiredRecords() {
        long currentTime = System.currentTimeMillis();
        long expireTime = 10 * 60 * 1000L; // 10分钟过期
        
        exceptionCache.entrySet().removeIf(entry -> {
            ExceptionRecord record = entry.getValue();
            return currentTime - record.getLastOccurrence() > expireTime;
        });
        
        if (log.isDebugEnabled()) {
            log.debug("异常缓存清理完成，当前缓存大小: {}", exceptionCache.size());
        }
    }

    /**
     * 异常记录内部类
     */
    private static class ExceptionRecord {
        private final long firstOccurrence;
        private long lastOccurrence;
        private int count;
        private final String message;

        public ExceptionRecord(long firstOccurrence, int count, String message) {
            this.firstOccurrence = firstOccurrence;
            this.lastOccurrence = firstOccurrence;
            this.count = count;
            this.message = message;
        }

        public long getFirstOccurrence() {
            return firstOccurrence;
        }

        public long getLastOccurrence() {
            return lastOccurrence;
        }

        public void setLastOccurrence(long lastOccurrence) {
            this.lastOccurrence = lastOccurrence;
        }

        public int getCount() {
            return count;
        }

        public void incrementCount() {
            this.count++;
        }

        public String getMessage() {
            return message;
        }
    }

    /**
     * 销毁方法
     */
    public void destroy() {
        cleanupExecutor.shutdown();
        try {
            if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            cleanupExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
