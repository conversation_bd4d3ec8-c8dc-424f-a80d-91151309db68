package com.holderzone.member.common.enums.equities;

/**
 * @program: member-marketing
 * @description: 金额满足计算范围类型
 * @author: pan tao
 * @create: 2021-12-21 10:23
 */
public enum AmountSatisfyScopeTypeEnum {

    /**
     * 全部计算
     */
    ALL_SCOPE(0,"全部计算"),

    /**
     * 部分计算
     */
    APPOINT_SCOPE(1,"部分计算"),

    ;

    /**
     * 编号
     */
    private int code;

    /**
     * 描述
     */
    private String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    AmountSatisfyScopeTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }


}
