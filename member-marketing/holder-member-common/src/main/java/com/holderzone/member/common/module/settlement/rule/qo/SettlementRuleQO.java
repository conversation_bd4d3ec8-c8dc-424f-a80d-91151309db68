package com.holderzone.member.common.module.settlement.rule.qo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.member.common.dto.page.PageDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 结算规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class SettlementRuleQO extends PageDTO implements Serializable {


    private static final long serialVersionUID = -799465842308557371L;

    /**
     * 关键字
     * 应用业务/应用门店编号/应用门店名称模糊搜索
     */
    private String keywords;
    /**
     * 主体
     */
    @JsonIgnore
    private String operSubjectGuid;

    /**
     * 是否包含所有业务
     */
    @JsonIgnore
    private String applyBusiness;

    /**
     * 是否包含门店
     */
    @JsonIgnore
    private List<String> storeGuids;

}
