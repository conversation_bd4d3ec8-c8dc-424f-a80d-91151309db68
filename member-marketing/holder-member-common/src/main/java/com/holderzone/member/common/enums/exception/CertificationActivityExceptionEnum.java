package com.holderzone.member.common.enums.exception;

import com.holderzone.member.common.enums.ResponseBase;

/**
 * <AUTHOR>
 */

public enum CertificationActivityExceptionEnum implements ResponseBase {

    /**
     * 认证有礼活动未查询到
     */
    FOLLOW_ACTIVITY_NOT_FOUND(1, "认证有礼活动未查询到"),

    /**
     * 认证有礼活动未发布
     */
    ACTIVITY_NOT_PUBLISH(2, "认证活动未发布,不能更改状态"),

    /**
     * 活动已结束
     */
    ACTIVITY_HSA_ENDED(3, "活动已结束，不能修改状态"),

    ;

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误描述
     */
    private final String des;

    CertificationActivityExceptionEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getDes() {
        return this.des;
    }
}
