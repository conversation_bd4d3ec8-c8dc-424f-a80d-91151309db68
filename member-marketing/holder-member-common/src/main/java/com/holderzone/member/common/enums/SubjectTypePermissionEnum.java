package com.holderzone.member.common.enums;

import org.springframework.util.StringUtils;

public enum SubjectTypePermissionEnum {

    //岗位
    JOB_PERMISSION(0,"job"),
    //角色
    ROLE_PERMISSION(1,"role");

    //权限类型
    private final int code;

    private final String des;

    SubjectTypePermissionEnum(int code, String des){
        this.code = code;
        this.des = des;
    }

    public String getMessageByType(int code){
        SubjectTypePermissionEnum[] permissionEnums = SubjectTypePermissionEnum.values();
        for (SubjectTypePermissionEnum permissionEnum : permissionEnums) {
            if(permissionEnum.getCode() == code){
                return permissionEnum.getDes();
            }
        }
        return "";
    }

    public int getTypeByMessage(String message){
        if(StringUtils.isEmpty(message)){
            return -1;
        }
        SubjectTypePermissionEnum[] permissionEnums = SubjectTypePermissionEnum.values();
        for (SubjectTypePermissionEnum permissionEnum : permissionEnums) {
            if(permissionEnum.getDes().equals(message)){
                return permissionEnum.getCode();
            }
        }
        return -1;
    }

    public String getDes(){
        return des;
    }

    public int getCode(){
        return code;
    }
}
