package com.holderzone.member.common.module.marketing.coupon.use.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.holderzone.member.common.config.ExportTimeConverter;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 优惠券核销明细到处类型
 * 处理枚举、时间等
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CouponUseExportVO implements Serializable {


    private static final long serialVersionUID = 1893642681949294050L;

    @ExcelProperty(value = "券ID", index = 0)
    private String couponCode;

    @ExcelProperty(value = "券名称", index = 1)
    private String couponName;

    @ExcelProperty(value = "券类型", index = 2)
    private String couponTypeStr;

    @ExcelProperty(value = "优惠力度", index = 3)
    private String discountDynamic;

    @ExcelProperty(value = "券码", index = 4)
    private String code;

    @ExcelProperty(value = "核销用户", index = 5)
    private String userName;

    @ExcelProperty(value = "手机号", index = 6)
    private String memberPhone;

    @ExcelProperty(value = "发放场景", index = 7)
    private String couponPackageTypeStr;

    @ExcelProperty(value = "优惠券抵扣金额", index = 8)
    private BigDecimal discountAmount;

    @ExcelProperty(value = "带动消费", index = 9)
    private BigDecimal orderPaidAmount;

    @ExcelProperty(value = "核销门店", index = 10)
    private String storeName;

    @ExcelProperty(value = "核销时间", index = 11, converter = ExportTimeConverter.class)
    private LocalDateTime couponUseTime;

    @ExcelProperty(value = "核销渠道", index = 12)
    private String sourceStr;

    @ExcelProperty(value = "订单编号", index = 13)
    private String orderNumber;

    @ExcelProperty(value = "操作人", index = 14)
    private String operatorAccountName;
}
