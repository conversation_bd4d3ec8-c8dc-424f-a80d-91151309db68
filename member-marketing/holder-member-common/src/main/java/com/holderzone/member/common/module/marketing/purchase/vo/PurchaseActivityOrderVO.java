package com.holderzone.member.common.module.marketing.purchase.vo;

import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseRulePeriodEnum;
import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseRuleTypeEnum;
import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseStateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 限量抢购活动
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Data
@Accessors(chain = true)
public class PurchaseActivityOrderVO{

    @ApiModelProperty(value = "活动名称")
    private String name;

    /**
     * 活动状态
     *
     * @see PurchaseStateEnum
     */
    @ApiModelProperty(value = "活动状态")
    private Integer state;


    @ApiModelProperty(value = "活动打标签")
    private String applyLabelGuidJson;

    /**
     * 限购规则
     *
     * @see PurchaseRuleTypeEnum
     */
    @ApiModelProperty(value = "限购规则:0每人限购 1每单限购 2周期限购")
    private Integer purchaseRuleType;

    /**
     * 限购周期
     *
     * @see PurchaseRulePeriodEnum
     */
    @ApiModelProperty(value = "周期限购：0 每日/人 ，1每周/人，2每月/人")
    private Integer purchaseRulePeriod;

    @ApiModelProperty(value = "限购场景")
    private String applyBusinessJson;

    /**
     * 应用门店类型
     *
     * @see com.holderzone.member.common.enums.ChoinceEnum
     */
    @ApiModelProperty(value = "应用门店类型：0全部 1部分")
    private Integer applyStoreType;

    @ApiModelProperty(value = "应用门店")
    private String applyStoreJson;


    /**
     * 发布过：1是 0否(默认)
     * 用于详情发布后，只能编辑部分字段
     */
    @ApiModelProperty(value = "未发布过")
    private Integer published;

    @ApiModelProperty(value = "活动开始时间")
    private LocalDateTime activityStartTime;

    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime activityEndTime;

}
