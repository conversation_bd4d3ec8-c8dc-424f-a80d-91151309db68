package com.holderzone.member.common.entity.activity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ApplyRecordCommodity {



    /**
     * 商品id
     */
    private String commodityId;

    /**
     * 商品编码
     */
    private String commodityCode;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 优惠限购
     * 为空表示不限制
     */
    private BigDecimal totalNumber;
}
