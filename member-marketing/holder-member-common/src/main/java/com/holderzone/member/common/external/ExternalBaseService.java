package com.holderzone.member.common.external;

import com.holderzone.member.common.dto.PageModel;
import com.holderzone.member.common.dto.holder.RoleAndPostIdDTO;
import com.holderzone.member.common.dto.ipaas.AddAndUpdateIPaasDTO;
import com.holderzone.member.common.dto.mall.TeamStoreInfoModel;
import com.holderzone.member.common.dto.mall.TeamStoreInfoPageParamModel;
import com.holderzone.member.common.dto.permission.MemberSystemPermissionDTO;
import com.holderzone.member.common.dto.permission.OperationPermissionQO;
import com.holderzone.member.common.dto.permission.RolePermissionMapModel;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.dto.user.OperSubjectInfo;
import com.holderzone.member.common.qo.permission.OperSubjectUserPermissionQO;
import com.holderzone.member.common.vo.base.BusinessDataModel;
import com.holderzone.member.common.vo.base.OperSubjectInfoVO;
import com.holderzone.member.common.vo.equities.ApplyTypeVO;
import com.holderzone.member.common.vo.ipass.StoreListVO;
import com.holderzone.member.common.vo.ipass.SubjectListVO;
import com.holderzone.member.common.vo.ipass.SubjectVO;
import com.holderzone.member.common.vo.member.MemberSystemPermissionVO;
import com.holderzone.member.common.vo.permission.HsaOperSubjectPermissionVO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description 外部服务支撑接口
 */
public interface ExternalBaseService {

    /**
     * 根据企业guid查询用户信息
     * @param enterpriseGuid 企业guid
     * @return 用户信息
     */
    HeaderUserInfo queryUserInformation(String enterpriseGuid);

    /**
     * 根据企业guid和token查询用户信息
     * @param enterpriseGuid 企业guid
     * @param token token
     * @return 用户信息
     */
    HeaderUserInfo queryUserInformation(String enterpriseGuid, String token);

    /**
     * 获取基础数据
     * @return
     */
    OperSubjectInfoVO getOperatingSubjectInfo();

    /**
     * 根据企业查询主体信息
     * @param enterpriseGuid 企业guid
     * @return 主体信息列表
     */
    List<HsaOperSubjectPermissionVO> queryOperatingSubjectByEnterpriseGuid(String enterpriseGuid);

    /**
     * 校验B端用户账户密码有效性
     * @param account 账户
     * @param password 密码
     * @return 是否
     */
    Boolean validateUser(String account, String password);

    /**
     * 根据主体查询业务数据
     * @param operSubjectGuid 主体guid
     * @return 业务数据
     */
    BusinessDataModel queryBusinessData(String operSubjectGuid);

    /**
     * 根据主体和token查询业务数据
     * @param operSubjectGuid 主体guid
     * @param token token
     * @return 业务数据
     */
    BusinessDataModel queryBusinessData(String operSubjectGuid, String token);

    /**
     * 根据企业查询主体列表
     * @param enterpriseGuid 企业guid
     * @return 主体列表信息
     */
    List<OperSubjectInfo> queryOperatingSubject(String enterpriseGuid);


    List<OperSubjectInfo> queryOperatingSubject();

    /**
     * 根据企业查询业务系统地址
     * @param enterpriseGuid 企业guid
     * @return 地址
     */
    String findBusinessDataAddress(String enterpriseGuid);

    /**
     * 查询所有主体信息
     * @return 主体id列表
     */
    List<String> listAllOperationSubjectId();

    /**
     * 判断用户是否有对应功能权限
     * @param request 请求参数
     * @param token token
     * @return 是否有权限
     */
    Boolean getHasPermissionName(OperationPermissionQO request, String token);

    /**
     * 查询用户权限列表
     * @param operationPermissionRequest 请求参数
     * @param token 用户token
     * @return 权限列表
     */
    List<MemberSystemPermissionDTO> listSystemPermission(OperationPermissionQO operationPermissionRequest, String token);


    /**
     * 查询用户权限标识列表
     *
     * @return 权限列表
     */
    List<String> listIdentificationNames();

    /**
     * 查询用户运营主体权限列表
     *
     * @return 权限列表
     */
    List<SubjectVO> listIdentificationSubjects(String identificationName);


    /**
     * 查询用户角色
     * @param userPermissionQO 查询参数
     * @param token 用户token
     * @return 角色列表
     */
    List<String> listUserRoleIds(OperSubjectUserPermissionQO userPermissionQO, String token);

    /**
     * 查询用户角色权限
     * @param operationPermissionRequest 请求参数
     * @param token 用户token
     * @return 返回参数
     */
    RolePermissionMapModel findUserRoleMapPermission(OperationPermissionQO operationPermissionRequest, String token);

    /**
     * 获取系统权限列表
     *
     * @param operationPermissionQO 请参参数
     * @return 操作结果
     */
    RolePermissionMapModel findUserRoleMapPermission(OperationPermissionQO operationPermissionQO);

    /**
     * 根据账户获取临时token
     * @param account 账户
     * @return token
     */
    String getTemporaryTokenByAccount(String account);

    /**
     * 发送验证码
     * @param type 0 短信 1 邮箱
     * @param info 数据
     * @return
     */
    int requestVerification(Integer type, String info);

    /**
     * 查询验证码
     * @param code 验证码
     * @param info 数据
     * @return
     */
    int validateVerification(String code, String info);

    /**
     * 通过企业查询运营主体
     *
     * @param teamId 企业id
     * @return 运营主体
     */
    List<OperSubjectInfo> queryOperatingSubjectByTeam(String teamId);

    /**
     * 获取系统权限列表
     *
     * @param operationPermissionRequest 请参参数
     * @return 操作结果
     */
    List<MemberSystemPermissionDTO> getSystemPermissionList(OperationPermissionQO operationPermissionRequest);

    /**
     * @description: 查询用户角色
     * @author: li ao
     * @date: 2024/3/19 10:57
     * @param: enterpriseGuid
     * @param: tel
     * @return: com.holderzone.member.common.dto.holder.RoleAndPostIdDTO
     **/
    RoleAndPostIdDTO findUserRoleAndPost(String enterpriseGuid, String tel);

    /**
     * @description: 查询商店分页信息
     * @author: li ao
     * @date: 2024/3/19 10:57
     * @param: enterpriseGuid
     **/
    PageModel<TeamStoreInfoModel> findStorePageInfo(TeamStoreInfoPageParamModel teamStoreInfoPageParamModel);

    /**
     * 查询用户下权限范围
     *
     * @return
     */
    List<String> queryUserPermission(Long teamId, String account, Integer permissionType, Long typeId, String token);

    /**
     * holder通过账号获取临时token
     */
    String getTemporaryToken(String phoneNum);

    /**
     * @description: 查询企业下所有门店信息
     * @author: li ao
     * @date: 2024/3/20 15:25
     * @param: teamInfoId 企业id
     * @return: java.util.List<com.holderzone.member.common.vo.iPaas.StoreListVO>
     **/
    List<StoreListVO> getStoreByTeam(Long teamInfoId);

    /**
     * @description: 查询门店详情
     * @author: li ao
     * @date: 2024/3/20 15:31
     * @param: id 门店ID
     * @return: com.holderzone.member.common.vo.iPaas.StoreListVO
     **/
    StoreListVO getStoreDetail(Long id);

    /**
     * @description: 新增/编辑门店基础信息
     * @author: li ao
     * @date: 2024/3/20 15:36
     * @param: addAndUpdateIPaas 新增/编辑门店基础信息参数
     **/
    void addAndUpdateStore(AddAndUpdateIPaasDTO addAndUpdateIPaas);

    /**
     * @description: 查询用户详情
     * @author: li ao
     * @date: 2024/3/20 15:53
     * @param: account 用户账号
     * @return: com.holderzone.member.common.vo.iPaas.UserInfoVO
     **/
    HeaderUserInfo queryUserInfoDetail(String account);

    /**
     * 获取用户权限
     * @return MemberSystemPermissionVO memberSystemPermissionVO
     */
    MemberSystemPermissionVO getAccountPermission();


    /**
     * 获取适用业务
     * @return 适用业务
     */
    List<ApplyTypeVO> getApplyBusiness();

    /**
     * 获取适用业务
     * @return 适用业务
     */
    List<ApplyTypeVO> getAllApplyBusiness();

    /**
     * 获取适用终端
     * @return 适用渠道
     */
    List<ApplyTypeVO> getApplyTerminal();

    /**
     * 获取适用终端
     *
     * @return 适用渠道
     */
    List<ApplyTypeVO> getAllApplyTerminal();


    /**
     * 根据业务类型获取业务名称
     * @return 适用业务
     */
    String getBusinessName(Integer code);

    /**
     * 渠道转换枚举
     * @return
     */
    String getSourceTypeEnum(int code);

    String getOrderTypeEnum(int code);

    String getOrderSourceEnum(int code);

    /**
     * 查询主体详情
     * @param id
     * @return
     */
    SubjectListVO findSubjectDetail(Integer id);
}
