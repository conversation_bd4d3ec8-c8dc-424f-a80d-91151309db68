package com.holderzone.member.common.external;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.common.enums.PlatformEnum;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description
 */
@Component
public class ExternalItemFactory {

    private final ExternalItemService canteenItemServiceImpl;

    private final ExternalItemService saasItemServiceImpl;

    private final ExternalItemService marketItemServiceImpl;

    private final ExternalItemService mallItemServiceImpl;

    public ExternalItemFactory(@Qualifier("canteenItemServiceImpl") ExternalItemService canteenItemServiceImpl,
                               @Qualifier("saasItemServiceImpl") ExternalItemService saasItemServiceImpl,
                               @Qualifier("marketItemServiceImpl") ExternalItemService marketItemServiceImpl,
                               @Qualifier("mallItemServiceImpl") ExternalItemService mallItemServiceImpl) {
        this.canteenItemServiceImpl = canteenItemServiceImpl;
        this.saasItemServiceImpl = saasItemServiceImpl;
        this.marketItemServiceImpl = marketItemServiceImpl;
        this.mallItemServiceImpl = mallItemServiceImpl;
    }

    public ExternalItemService build(PlatformEnum platformEnum) {
        switch (platformEnum) {
            case HOLDER_CANTEEN:
                return canteenItemServiceImpl;
            case SAAS:
                return saasItemServiceImpl;
            case PASS_RETAIL:
                return marketItemServiceImpl;
            case MALL:
                return mallItemServiceImpl;
            default:
                throw new BusinessException("平台有误");
        }
    }
}
