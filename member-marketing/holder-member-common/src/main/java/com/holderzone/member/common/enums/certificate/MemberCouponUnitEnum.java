package com.holderzone.member.common.enums.certificate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 老会员优惠券单位枚举
 */
@Getter
@AllArgsConstructor
public enum MemberCouponUnitEnum {

    UNIT_DAY(0, "天"),

    UNIT_HOUR(1, "小时"),

    UNIT_MINUTE(2, "分钟"),

    UNIT_NOW(3, "立即"),

    UNIT_SECOND(4, "秒"),

    COLLECTION_OTHER(-1, "其他");

    private final Integer type;

    private final String name;

}
