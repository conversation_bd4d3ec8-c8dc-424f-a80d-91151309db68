package com.holderzone.member.common.enums.commodity;

/**
 * @description: 商品分享标签枚举类
 * <AUTHOR>
 */
public enum ShareFormatEnum {

    /**
     * 商品售价+商品名称
     */
    PRODUCT_NAME_PRICE(0,"商品售价+商品名称"),

    PRODUCT_NAME(1,"商品名称")
    ;

    private final int code;

    private final String des;

    ShareFormatEnum(int code,String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return this.code;
    }

    public String getDes() {
        return this.des;
    }

}
