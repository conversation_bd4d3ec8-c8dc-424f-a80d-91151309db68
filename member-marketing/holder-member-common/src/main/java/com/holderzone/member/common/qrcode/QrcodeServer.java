package com.holderzone.member.common.qrcode;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.holderzone.member.common.exception.QrcodeException;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ImageObserver;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * author: pantao
 */
@Component
public class QrcodeServer {
    private final Logger logger = LoggerFactory.getLogger(QrcodeServer.class);
    @Autowired
    private RedisTemplate redisTemplate;

    public QrcodeServer() {
        //default
    }

    public QRCode generate(String content, int width, int height) {
        try {
            QRCode qrcode = new QRCode();
            qrcode.setWidth(Integer.valueOf(0 == width ? 300 : width));
            qrcode.setHeight(Integer.valueOf(0 == height ? 300 : height));
            qrcode.setContent(content);
            return qrcode;
        } catch (QrcodeException var8) {
            this.logger.warn("生成二维码失败:{}", var8);
            throw new QrcodeException("生成二维码失败:{}", var8);
        } catch (Exception var9) {
            this.logger.error("生成二维码失败:{}", var9);
            throw new QrcodeException("生成二维码失败:{}", var9);
        }
    }

    public void display(String key, HttpServletResponse response) {
        ServletOutputStream stream = null;

        try {
            if (StringUtil.isNullOrEmpty(key)) {
                throw new QrcodeException("无效的二维码请求地址");
            }

            Object content = this.redisTemplate.opsForValue().get(key);
            if (null == content) {
                throw new QrcodeException("无效的二维码请求地址,可能该二维码已过期");
            }

            QRCode qrCode = (QRCode) content;
            if (null == qrCode) {
                throw new QrcodeException("无效的二维码请求地址,可能该二维码已过期");
            }

            // 设置二维码生成参数，减少边距
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.MARGIN, 1);

            QRCodeWriter writer = new QRCodeWriter();
            BitMatrix m = writer.encode(qrCode.getContent(), BarcodeFormat.QR_CODE, qrCode.getWidth().intValue(), qrCode.getHeight().intValue(), hints);
            stream = response.getOutputStream();
            MatrixToImageWriter.writeToStream(m, "png", stream);
        } catch (QrcodeException var16) {
            this.logger.warn("获取二维码失败:{}", var16);
            throw new QrcodeException("获取二维码失败:{}", var16);
        } catch (Exception var17) {
            this.logger.error("获取二维码失败:{}", var17);
            throw new QrcodeException("获取二维码失败:{}", var17);
        } finally {
            if (stream != null) {
                try {
                    stream.flush();
                    stream.close();
                } catch (IOException var15) {
                    ;
                }
            }

        }

    }

    public String display(QRCode qrCode) {
        ByteArrayOutputStream outputStream = null;

        String var8;
        try {
            if (null == qrCode) {
                throw new QrcodeException("无效的二维码请求地址,可能该二维码已过期");
            }

            // 设置二维码生成参数，减少边距
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.MARGIN, 1);

            QRCodeWriter writer = new QRCodeWriter();
            BitMatrix m = writer.encode(qrCode.getContent(), BarcodeFormat.QR_CODE, qrCode.getWidth().intValue(), qrCode.getHeight().intValue(), hints);
            BufferedImage image = MatrixToImageWriter.toBufferedImage(m);
            outputStream = new ByteArrayOutputStream();
            ImageIO.write(image, "png", outputStream);
            var8 = Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (QrcodeException var18) {
            this.logger.warn("获取二维码失败:{}", var18);
            throw new QrcodeException("获取二维码失败:{}", var18);
        } catch (Exception var19) {
            this.logger.error("获取二维码失败:{}", var19);
            throw new QrcodeException("获取二维码失败:{}", var19);
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (IOException var17) {
                    ;
                }
            }

        }

        return var8;
    }

    public void createBarcode(String content, int widthPix, int heightPix, HttpServletResponse response) {
        if (!StringUtil.isNullOrEmpty(content)) {
            Map<EncodeHintType, Object> hints = new HashMap();
            hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            MultiFormatWriter writer = new MultiFormatWriter();
            ServletOutputStream stream = null;

            try {
                BitMatrix bitMatrix = writer.encode(content, BarcodeFormat.CODE_128, widthPix, heightPix, hints);
                stream = response.getOutputStream();
                MatrixToImageWriter.writeToStream(bitMatrix, "png", stream);
            } catch (WriterException var17) {
                this.logger.warn("获取条形码失败:{}", var17);
                throw new QrcodeException("获取条形码失败:{}", var17);
            } catch (IOException var18) {
                this.logger.warn("获取条形码失败:{}", var18);
                throw new QrcodeException("获取条形码失败:{}", var18);
            } finally {
                if (stream != null) {
                    try {
                        stream.flush();
                        stream.close();
                    } catch (IOException var16) {
                        ;
                    }
                }

            }

        }
    }



    public String generateBarcode(String content, int width, int height) {
        try {
            return createBarcode(content, width, height);
        } catch (QrcodeException var8) {
            this.logger.warn("生成二维码失败:{}", var8);
            throw new QrcodeException("生成二维码失败:{}", var8);
        } catch (Exception var9) {
            this.logger.error("生成二维码失败:{}", var9);
            throw new QrcodeException("生成二维码失败:{}", var9);
        }
    }

    public String createBarcode(String content, int widthPix, int heightPix) {
        if (StringUtil.isNullOrEmpty(content)) {
            return "";
        } else {
            Map<EncodeHintType, Object> hints = new HashMap();
            hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            MultiFormatWriter writer = new MultiFormatWriter();
            ByteArrayOutputStream stream = null;

            String var9;
            try {
                BitMatrix bitMatrix = writer.encode(content, BarcodeFormat.CODE_128, widthPix, heightPix, hints);
                BufferedImage image = MatrixToImageWriter.toBufferedImage(bitMatrix);
                stream = new ByteArrayOutputStream();
                ImageIO.write(image, "png", stream);
                var9 = Base64.getEncoder().encodeToString(stream.toByteArray());
            } catch (WriterException var19) {
                this.logger.warn("获取条形码失败:{}", var19);
                throw new QrcodeException("获取条形码失败:{}", var19);
            } catch (IOException var20) {
                this.logger.warn("获取条形码失败:{}", var20);
                throw new QrcodeException("获取条形码失败:{}", var20);
            } finally {
                if (stream != null) {
                    try {
                        stream.flush();
                        stream.close();
                    } catch (IOException var18) {
                        ;
                    }
                }

            }

            return var9;
        }
    }

    public void createBarcode(String content, String words, int widthPix, int heightPix, int wordHeightPix, HttpServletResponse response) {
        if (!StringUtil.isNullOrEmpty(content)) {
            if (!StringUtil.isNullOrEmpty(words)) {
                ServletOutputStream stream = null;

                try {
                    stream = response.getOutputStream();
                    BufferedImage bufferedImage = this.insertWords(this.getBarCode(content, widthPix, heightPix), words, widthPix, heightPix, wordHeightPix);
                    ImageIO.write(bufferedImage, "png", stream);
                } catch (IOException var16) {
                    this.logger.warn("获取条形码失败:{}", var16);
                    throw new QrcodeException("获取条形码失败:{}", var16);
                } finally {
                    if (stream != null) {
                        try {
                            stream.flush();
                            stream.close();
                        } catch (IOException var15) {
                            ;
                        }
                    }

                }

            }
        }
    }

    public String createBarcode(String content, String words, int widthPix, int heightPix, int wordHeightPix) {
        if (StringUtil.isNullOrEmpty(content)) {
            return "";
        } else if (StringUtil.isNullOrEmpty(words)) {
            return "";
        } else {
            ByteArrayOutputStream stream = null;

            String var8;
            try {
                BufferedImage bufferedImage = this.insertWords(this.getBarCode(content, widthPix, heightPix), words, widthPix, heightPix, wordHeightPix);
                stream = new ByteArrayOutputStream();
                ImageIO.write(bufferedImage, "png", stream);
                var8 = Base64.getEncoder().encodeToString(stream.toByteArray());
            } catch (IOException var17) {
                this.logger.warn("获取条形码失败:{}", var17);
                throw new QrcodeException("获取条形码失败:{}", var17);
            } finally {
                if (stream != null) {
                    try {
                        stream.flush();
                        stream.close();
                    } catch (IOException var16) {
                        ;
                    }
                }

            }

            return var8;
        }
    }

    private BufferedImage getBarCode(String content, int widthPix, int heightPix) {
        try {
            Map<EncodeHintType, Object> hints = new HashMap();
            hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
            MultiFormatWriter writer = new MultiFormatWriter();
            BitMatrix bitMatrix = writer.encode(content, BarcodeFormat.CODE_128, widthPix, heightPix, hints);
            return MatrixToImageWriter.toBufferedImage(bitMatrix);
        } catch (WriterException var7) {
            var7.printStackTrace();
            return null;
        }
    }

    public BufferedImage insertWords(BufferedImage image, String words, int widthPix, int heightPix, int wordHeightPix) {
        if (StringUtils.isNotEmpty(words)) {
            BufferedImage outImage = new BufferedImage(widthPix, wordHeightPix, 1);
            Graphics2D g2d = outImage.createGraphics();
            this.setGraphics2D(g2d);
            setColorWhite(g2d);
            g2d.drawImage(image, 0, 0, image.getWidth(), image.getHeight(), (ImageObserver) null);
            Color color = new Color(0, 0, 0);
            g2d.setColor(color);
            g2d.setFont(new Font("微软雅黑", 0, 18));
            int strWidth = g2d.getFontMetrics().stringWidth(words);
            int wordStartX = (widthPix - strWidth) / 2;
            int wordStartY = heightPix + 20;
            g2d.drawString(words, wordStartX, wordStartY);
            g2d.dispose();
            outImage.flush();
            return outImage;
        } else {
            return null;
        }
    }

    private void setGraphics2D(Graphics2D g2d) {
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_DEFAULT);
        Stroke s = new BasicStroke(1.0F, 1, 0);
        g2d.setStroke(s);
    }

    private static void setColorWhite(Graphics2D g2d) {
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, 600, 600);
        g2d.setColor(Color.BLACK);
    }
}
