package com.holderzone.member.common.module.marketing.purchase.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseRulePeriodEnum;
import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseRuleTypeEnum;
import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseStateEnum;
import com.holderzone.member.common.vo.member.LabelSiftVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 限量抢购活动详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Data
@Accessors(chain = true)
public class PurchaseDetailVo implements Serializable {


    private static final long serialVersionUID = 4023658097706126004L;
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * guid
     */
    @ApiModelProperty(value = "guid")
    private String guid;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String name;

    /**
     * 活动状态
     *
     * @see PurchaseStateEnum
     */
    @ApiModelProperty(value = "活动状态")
    private Integer state;

    /**
     * 发布过：1是 0否(默认)
     * 用于详情发布后 ，只能编辑部分字段
     */
    @ApiModelProperty(value = "未发布过")
    private Integer published;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityStartTime;


    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityEndTime;

    /**
     * 活动打标签
     */
    @ApiModelProperty(value = "活动打标签")
    private List<LabelSiftVO> applyLabelList;

    /**
     * 限购规则
     *
     * @see PurchaseRuleTypeEnum
     */
    @ApiModelProperty(value = "限购规则:0每人限购 1每单限购 2周期限购")
    private Integer purchaseRuleType;

    /**
     * 限购周期
     *
     * @see PurchaseRulePeriodEnum
     */
    @ApiModelProperty(value = "周期限购：0 每日/人 ，1每周/人，2每月/人")
    private Integer purchaseRulePeriod;

    /**
     * 限购场景
     */
    @ApiModelProperty(value = "限购场景")
    private List<String> applyBusinessList;

    /**
     * 应用门店类型
     *
     * @see com.holderzone.member.common.enums.ChoinceEnum
     */
    @ApiModelProperty(value = "应用门店类型：0全部 1部分")
    private Integer applyStoreType;

    /**
     * 门店列表
     */
    @ApiModelProperty(value = "门店列表")
    private List<PurchaseStoreVo> applyStoreList;

    /**
     * 商品列表
     */
    @ApiModelProperty(value = "商品列表")
    private List<PurchaseCommodityVO> applyCommodityList;


}
