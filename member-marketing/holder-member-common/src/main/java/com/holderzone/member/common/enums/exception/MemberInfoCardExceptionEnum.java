package com.holderzone.member.common.enums.exception;

import com.holderzone.member.common.enums.ResponseBase;

/**
 * <AUTHOR>
 * @description 卡操作枚举
 * @date 2021/9/1
 */
public enum MemberInfoCardExceptionEnum implements ResponseBase {

    // 数据操作错误定义
    BATCH_OPEN_ELECTRONIC_CARD_FAIL(50000, "会员账户批量开通电子卡失败"),

    OPEN_ELECTRONIC_CARD_FAIL(50001, "会员账户开通电子卡失败"),

    CARD_CURRENTLY_UNAVAILABLE(50002, "暂无可开通的会员卡"),

    CARD_OPEN_FINISH(50003, "会员卡已结束开通"),

    CARD_OPEN_TYPE_ERROR(50004, "会员卡开卡方式错误"),

    CARD_OPEN_INFO_UPDATED(50005, "会员卡信息已更新，请刷新页面"),

    CARD_OPEN_MEMBER_DISABLED(50006, "您的账户已禁用，不可开通"),

    CARD_OPEN_FAIL(50007, "开通失败"),

    CARD_OPEN_PAY_MONEY_ERROR(50008, "付款金额有误"),

    CARD_DISABLED(50009, "会员卡已禁用，请联系管理员"),

    CARD_NOT_ACTIVATED(50010, "会员卡未激活，请先激活使用"),

    CARD_EXPIRED(50011, "会员卡已过期"),

    CARD_NOT_EXISTS(50012,"卡片错误")
    ;

    /** 错误码 */
    private final int code;

    /** 错误描述 */
    private final String des;

    MemberInfoCardExceptionEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDes() {
        return des;
    }
}
