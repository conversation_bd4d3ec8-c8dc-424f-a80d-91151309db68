package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.crm.CrmRefundPayDTO;
import com.holderzone.member.common.qo.market.StoreQueryQO;
import com.holderzone.member.common.vo.feign.*;
import com.holderzone.member.common.vo.market.StorePageListOut;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 零售 feign
 *
 * <AUTHOR>
 * @date 2024/04/09
 **/
@Component
@FeignClient(name = FilterConstant.MARKET, fallbackFactory = MarketFeign.ServiceFallBack.class, url = "${feign.market}")
public interface MarketFeign {

    /**
     * 查询零售门店
     **/
    @PostMapping("/api/v1/store/search")
    MarketFeignModel<StorePageListOut> getStoreAllPage(@RequestParam("page") Integer page,
                                                       @RequestParam("pagesize") Integer pagesize,
                                                       @RequestBody StoreQueryQO storeQueryQO);


    @PostMapping(value = "/api/v1/order/member-recharge/refund", produces = "application/json;charset=utf-8")
    MarketFeignModel<Integer> refundAggPayOrder(@RequestBody CrmRefundPayDTO crmRefundPayDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MarketFeign> {


        @Override
        public MarketFeign create(Throwable throwable) {
            return new MarketFeign() {


                @Override
                public MarketFeignModel<StorePageListOut> getStoreAllPage(Integer page, Integer pagesize, StoreQueryQO storeQueryQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getStoreAllPage", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MarketFeignModel<Integer> refundAggPayOrder(CrmRefundPayDTO crmRefundPayDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "refundAggPayOrder", crmRefundPayDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };

        }
    }
}
