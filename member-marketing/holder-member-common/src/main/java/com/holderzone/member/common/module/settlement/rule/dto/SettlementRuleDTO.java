package com.holderzone.member.common.module.settlement.rule.dto;

import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.ChoinceEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MemberMarketingException;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 结算规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class SettlementRuleDTO implements Serializable {


    private static final long serialVersionUID = 1909398196857599797L;
    /**
     * 基础规则
     */
    private SettlementRuleBaseDTO baseRule;

    /**
     * 可叠加优惠项
     */
    private List<SettlementRuleDiscountDTO> appendDiscounts;

    /**
     * 不可叠加优惠项
     */
    private List<SettlementRuleDiscountDTO> disAppendDiscounts;

    /**
     * 应用门店
     */
    private List<SettlementRuleStoreDTO> applicableStore;

    public void validate() {
        //参数校验
        if (baseRule == null) {
            throw new MemberMarketingException(MemberAccountExceptionEnum.ERROR_VERIFY);
        }
        baseRule.validate();
        if (ChoinceEnum.PART.getCode() == baseRule.getApplicableAllStore() && CollectionUtils.isEmpty(applicableStore)) {
            throw new MemberMarketingException("应用门店不能为空!");
        }
        //更新标识
        this.baseRule.setIsUpdate(StringUtils.isEmpty(this.baseRule.getGuid()));
        this.baseRule.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        //优惠券上限校验
        if (baseRule.getCouponLimit() == BooleanEnum.FALSE.getCode()) {
            return;
        }
        if (!ObjectUtils.isEmpty(baseRule.couponLimitNum()) && (baseRule.couponLimitNum() < 0 || baseRule.couponLimitNum() > 99)) {
            throw new MemberMarketingException("优惠券叠加上限，请设置1~99");
        }
    }

    /**
     * 场景 或 门店为空
     */
    public boolean businessStoreIsNull() {
        if (baseRule == null) {
            return false;
        }
        //场景为空
        final boolean businessIsNull = baseRule.getApplyBusiness() == ChoinceEnum.NONE.getCode();
        //门店为空
        final boolean storeIsNull = baseRule.getApplicableAllStore() == ChoinceEnum.NONE.getCode();
        return businessIsNull || storeIsNull;
    }
}
