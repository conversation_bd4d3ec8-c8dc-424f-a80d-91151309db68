package com.holderzone.member.common.module.base.purchase.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.constant.StringConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 订单商品
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PurchaseOrderCommodityDto implements Serializable {


    private static final long serialVersionUID = 6949773258484561372L;

    /**
     * 限购活动id
     */
    @ApiModelProperty(value = "限购活动id")
    private String purchaseId;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;
    /**
     * 商品id
     */
    @ApiModelProperty("商品id")
    private String commodityId;

    /**
     * 商品编码
     * todo 相同编码的要合并
     */
    @ApiModelProperty("商品编码")
    private String commodityCode;

    /**
     * 商品数量
     */
    @ApiModelProperty("商品数量")
    private Integer commodityNum;


    /**
     * 订单时间
     */
    @ApiModelProperty("订单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reserveTime;

    public String getPurchaseId() {
        return StringUtils.isBlank(purchaseId) ? StringConstant.NO_DATA_ID : purchaseId;
    }
}
