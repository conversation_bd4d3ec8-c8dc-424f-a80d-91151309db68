package com.holderzone.member.common.entity.coupon;



import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.base.HsaBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class HsaMemberCouponPackageLink extends HsaBaseEntity implements Serializable {

    private static final long serialVersionUID = 3499389469406227355L;
    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动Guid
     */
    private String activityGuid;

    /**
     * 活动Code
     */
    private String activityCode;

    /**
     * 券包信息
     */
    private String couponPackageJson;

    /**
     * 会员名称
     */
    private String userName;

    /**
     * memberGuid
     */
    private String memberGuid;

    /**
     * memberPhone
     */
    private String memberPhone;

    /**
     * 发放渠道
     */
    private Integer source;

    /**
     * 门店Guid
     */
    private Integer storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 发放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reachTime;

    /**
     * 发放状态 1 成功 0失败
     */
    private Integer state;

    /**
     * 失败原因
     */
    private String reason;

}
