package com.holderzone.member.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 前端路由地址配置
 */
@Component
@Data
@ConfigurationProperties(prefix = "frontend.route")
public class FrontendRouteConfig {

	/**
	 * 类型到前端路由的映射，例如：{"coupon":"/marketing/coupon","memberCenter":"/member/center"}
	 */
	private Map<String, String> typeToUrl;
} 