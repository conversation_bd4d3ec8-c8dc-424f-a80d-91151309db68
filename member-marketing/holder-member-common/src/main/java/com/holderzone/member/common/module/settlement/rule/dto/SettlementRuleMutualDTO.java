package com.holderzone.member.common.module.settlement.rule.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.ChoinceEnum;
import com.holderzone.member.common.exception.MemberMarketingException;
import com.holderzone.member.common.module.settlement.constant.SettlementConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 结算规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Accessors(chain = true)
public class SettlementRuleMutualDTO implements Serializable {


    private List<SettlementDiscountSynDTO> discountList;

    /**
     * 主体，必传
     */
    private String operSubjectGuid;


}
