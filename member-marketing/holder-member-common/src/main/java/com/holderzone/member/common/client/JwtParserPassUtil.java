package com.holderzone.member.common.client;


import cn.hutool.json.JSONObject;
import cn.hutool.jwt.JWT;
import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.dto.user.IPaasTokenRequestBO;
import com.holderzone.member.common.exception.FileIllegalException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class JwtParserPassUtil {

    public IPaasTokenRequestBO acquirePayloadPass(String token) {
        try {
            final JWT jwt =  JWT.of(token);
            JSONObject payLoads = jwt.getPayloads();
            return JSON.parseObject(payLoads.toString(),IPaasTokenRequestBO.class);
        } catch (Exception e) {
            throw new FileIllegalException("编码错误", e);
        }
    }

    public String acquirePassPreferredUsernameToken(String token) {
        IPaasTokenRequestBO paasTokenRequestBO = acquirePayloadPass(token);
        return paasTokenRequestBO.getPreferred_username();
    }

}
