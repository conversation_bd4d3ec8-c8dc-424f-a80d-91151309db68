package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.vo.feign.CloudFeignModel;
import com.holderzone.resource.common.dto.enterprise.MultiMemberDTO;
import com.holderzone.resource.common.dto.enterprise.MultiMemberQueryDTO;
import com.holderzone.resource.common.dto.enterprise.OrganizationDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 云平台
 *
 * <AUTHOR>
 * @date 2023/11/13
 **/
@Component
@FeignClient(name = FilterConstant.CLOUD, fallbackFactory = CloudFeign.ServiceFallBack.class, url = "${feign.cloud}")
public interface CloudFeign {

    @GetMapping("/multi/member")
    @ApiOperation("多会员体系列表")
    CloudFeignModel<MultiMemberDTO> list(@RequestParam("enterpriseGuid") String enterpriseGuid);

    /**
     * 查询运营主体下的门店列表
     *
     * @param queryDTO 关联企业guid，运营主体guid
     * @return 门店列表
     */
    @ApiOperation(value = "查询运营主体下的门店列表", notes = "必传 关联企业guid，运营主体guid")
    @PostMapping("/organization/store_list_multiMemberGuid")
    CloudFeignModel<List<OrganizationDTO>> getStoreByMultiMemberGuid(@RequestBody MultiMemberQueryDTO queryDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<CloudFeign> {

        @Override
        public CloudFeign create(Throwable throwable) {
            return new CloudFeign() {

                @Override
                public CloudFeignModel<MultiMemberDTO> list(String enterpriseGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "list", enterpriseGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public CloudFeignModel<List<OrganizationDTO>> getStoreByMultiMemberGuid(MultiMemberQueryDTO queryDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getStoreByMultiMemberGuid", JacksonUtils.writeValueAsString(queryDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
