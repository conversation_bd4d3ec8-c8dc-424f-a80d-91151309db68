package com.holderzone.member.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/8/13
 */
public final class FilterConstant {

    private FilterConstant(){}

    public static final String TOKEN = "token";
    public static final String LOGIN_TOKEN = "loginToken";
    public static final String USER_INFO = "userInfo";
    public static final String ENTERPRISE_GUID = "enterpriseGuid";
    public static final String MERCHANT_AUTH = "merchantAuth";

    public static final String COMPANY_ID ="company_id";

    public static final String MALL_ENTERPRISE_GUID ="enterprise-guid";

    public static final String MALL_PROVIDER_ID = "provider-id";

    public static final String MALL_X_API_SECRET = "X-API-SECRET";

    public static final String USER_ID ="user_id";

    public static final String ACCOUNT ="account";
    /**
     * odoo 企业id
     */
    public static final String ODOO_ENTERPRISE_GUID = "companyId";
    public static final String STORE_GUID = "storeGuid";
    public static final String ALLIANCEID = "allianceid";

    public static final String SYSTEM = "system";
    public static final String IS_CHECK_TOKEN = "isCheckToken";
    public static final String RESOURCE_LINK_GUID = "resourceLinkGuid";
    public static final String TRACE_ID = "traceId";
    public static final String SOURCE = "source";
    public static final String TYPE_SOURCE = "typeSource";
    public static final String LOGIN_TYPE = "loginType";
    public static final String TERMINAL_CODE = "terminalCode";
    public static final String MENU_GUID = "menuGuid";
    public static final String SOURCE_CODE = "sourceCode";
    public static final String IS_ALLIANCE = "isAlliance";
    public static final String OPER_SUBJECT_GUID = "operSubjectGuid";
    public static final String MULTI_MEMBER_STATUS = "multiMemberStatus";
    public static final String STATUS = "status";

    public static final String HAS_RETURN_VALUE = "hasReturnValue";
    public static final String ERROR = "error";
    public static final Integer MEMBER_APPLET = 53;
    public static final Integer MEMBER_BACKSTAGE = 0;
    public static final String ERROR_AUTH_OUT = "令牌失效";
    public static final String ERROR_TOKEN_AUTH_FAIL = "认证异常";
    public static final String ERROR_SOURCE_AUTH_OUT = "来源异常";
    public static final String ERROR_SUBJECT_FAIL = "运营主体获取失败";

    public static final String ERROR_SUBJECT_MEMBER_STOP = "会员已停用，请联系管理员";

    //0库存 1销量 2序号 3:上新 4:价格排序
    public static final Integer PRODUCT_SORT_STOCK = 0;
    public static final Integer PRODUCT_SORT_SALES = 1;
    public static final Integer PRODUCT_SORT_SERIAL = 2;
    public static final Integer PRODUCT_SORT_NEWEST = 3;
    public static final Integer PRODUCT_SORT_PRICE = 4;

    public static final Integer PRODUCT_SORT_ASC = 0;
    public static final Integer PRODUCT_SORT_DES = 1;

    public static final String MARKETING_URL = "/marketing/";
    public static final String FEIGN_HOLDER = "holder";
    public static final String I_PAAS = "iPaas";

    public static final String SALES = "sales";
    public static final String FEIGN_SAAS = "saas-store";
    /**
     * 所有odoo服务统一：食堂、销售管理、
     */
    public static final String FEIGN_ODOO = "crm-base";
    public static final String ZHUAN_CAN = "zhuan-can";
    public static final String CLOUD = "cloud";

    public static final String MARKET = "market";

    public static final String PAY_UAT = "pay-uat";
    public static final String MEMBER_GATEWAY = "member-gateway";
    public static final String FEIGN_MEMBER = "member-base";
    public static final String MEMBER_PARTNER_PLATFORM = "member-partner-platform";
    public static final String MEMBER_PARTNER_APPLET = "member-partner-applet";
    public static final String SYSTEM_TYPE = "system";
    public static final String CONTENT_TYPE = "ContentType";
    public static final String APPLICATION_JSON = "application/json";
    public static final String FEIGN_MARKETING = "member-marketing";
    public static final String FEIGN_SETTLEMENT = "member-settlement";
    public static final String BAI_DU_YUN_TOKEN = "baiduyun-token";
    public static final String XXL_JOB_VALUE = "xxl_job_marketing";
    public static final String XXL_JOB_TYPE = "xxl_job_type";
    public static final String MEMBER_MALL = "member-mall";

    public static final String MEMBER_MALL_TOOL = "member-mall-tool";
    public static final String MEMBER_COMMODITY = "member-commodity";
    public static final String MALL_FEIGN_HOLDER = "mall-holder";

    /**
     * 系统白名单，startWith ，不用全路径匹配
     */
    public static final List<String> SYSTEM_URL = Arrays.asList(
            "/swagger-ui",
            "/swagger-resources",
            "/webjars/",
            "/csrf",
            "/actuator/shutdown",
            "/error",
            "/hsa_follow/order_rotary_table_call_back",
            "/hsa_follow/red_packet_settle_accounts",
            "/hsa_follow/red_packet_order_settle_call_back",
            "/certified_activity/query_activity_by_coupons_id",
            "/ter-card/getMemberUploadPage",
            "/ter-card/getUploadCardSecretPage",
            "/mall_order/find_mall_order_product",
            "/mall_order/update_order_state",
            "/member_grade_pay_record/agg_pay_callback",
            "/business/get/",
            "/mall_order/update_order_state",
            "/msg/initMessagesConfig",
            "/msg/getMessagesConfigByName",
            "/wx_open/receive_ticket",
            "/wx_open/callback_auth",
            "/msg/initMessagesBySubjectGuid",
            "/hsa_oper_subject_permission_label/synOperSubjectPermission",
            "/hsa_oper_subject_permission_label/initOperSubjectLabelPermission",
            "/guide_activity/applet/relation/store",
            "/wx_cp/callback/customer",
            "/wx_cp/callback/event"
    );

    /**
     * 私域商城
     */
    public static final String S2B2C_MALL = "s2b2c-mall";
}
