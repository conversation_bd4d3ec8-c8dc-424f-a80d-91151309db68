package com.holderzone.member.common.external.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.*;
import com.holderzone.member.common.dto.crm.*;
import com.holderzone.member.common.dto.grade.StoreInfoDTO;
import com.holderzone.member.common.dto.holder.RoleAndPostIdDTO;
import com.holderzone.member.common.dto.member.PermissionModelDTO;
import com.holderzone.member.common.dto.order.CrmOrderDTO;
import com.holderzone.member.common.dto.order.CrmOrderStockDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.external.ExternalStoreService;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.feign.HolderFeign;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.qo.crm.CrmOrderDetailQo;
import com.holderzone.member.common.qo.crm.CrmOrderDetailVo;
import com.holderzone.member.common.qo.growth.AppletGrowthStoreQO;
import com.holderzone.member.common.qo.tool.StoreByIdQO;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.feign.CrmReturnVo;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import com.holderzone.member.common.vo.tool.OperSubjectVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description 食堂门店
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CanteenStoreServiceImpl implements ExternalStoreService {

    private final CrmFeign crmFeign;

    private final HolderFeign holderFeign;

    private final MemberMallToolFeign memberMallToolFeign;

    @Override
    public List<StoreBaseInfo> listStore(QueryStoreBasePage query) {
        return crmFeign.queryStore(query).getDataList();
    }

    @Override
    public Page<StoreBaseInfo> storePage (QueryStoreBasePage query) {
        return new Page<>();
    }

    @Override
    public List<StoreBaseInfo> listStoreAndStall(QueryStoreBasePage query) {
        List<StoreBaseInfo> storeList = crmFeign.queryStore(query).getDataList();
        if (CollUtil.isEmpty(storeList)) {
            return Lists.newArrayList();
        }
        List<String> storeId = storeList.stream().map(StoreBaseInfo::getId).collect(Collectors.toList());
        List<StallBaseInfo> stallList = crmFeign.queryBaseStall(new QueryStallBasePage().setStoreId(storeId)).getDataList();
        //查询门店信息
        if (CollUtil.isNotEmpty(stallList)) {
            Map<String, List<StallBaseInfo>> stallBaseInfoMap = stallList
                    .stream()
                    .collect(Collectors.groupingBy(StallBaseInfo::getStoreId));
            storeList.forEach(storeBaseInfo -> {
                if (stallBaseInfoMap.containsKey(storeBaseInfo.getId())) {
                    storeBaseInfo.setStallBaseInfoList(stallBaseInfoMap.get(storeBaseInfo.getId()));
                }
            });
        }
        return storeList;
    }

    @Override
    public AppIdRespDTO getAppId(CrmAppIdQueryDTO crmAppIdReqDTO) {
        AppIdRespDTO appIdRespDTO = new AppIdRespDTO();
        CrmReturnVo<AppIdRespDTO> crmFeignAppId = crmFeign.getAppId(crmAppIdReqDTO);
        log.info("getAppId请求crm返回数据：{}", crmFeignAppId);
        FeignModel<AppIdRespDTO> result = crmFeignAppId.getResult();
        AppIdRespDTO data = result.getData();
        if (data == null) {
            return appIdRespDTO;
        }
        appIdRespDTO.setAppId(data.getAppId());
        appIdRespDTO.setAppsecret(data.getAppsecret());
        appIdRespDTO.setAppLogo(data.getAppLogo());
        appIdRespDTO.setAppName(data.getAppName());
        return appIdRespDTO;
    }

    @Override
    public ResAppletOrderCallBack appletOrderCallBack(AppletOrderCallBack query) {
        return crmFeign.appletsPayCallBack(query).getResult();
    }

    @Override
    public List<StoreBaseInfo> getStoreByStrategyOrCommodity(AppletGrowthStoreQO query) {
        return crmFeign.getStoreByStrategyOrCommodity(query).getResult();
    }

    @Override
    public PaySettingBaseRes getPaySetting(PaySettingDTO paySettingDTO) {
        return crmFeign.getPaySetting(paySettingDTO).getResult().getData();
    }

    @Override
    public int refundAggPayOrder(CrmRefundPayDTO crmRefundPayDTO) {
        return crmFeign.refundAggPayOrder(crmRefundPayDTO).getReturnCode();
    }

    @Override
    public void getReceiptPrinting(ReceiptPrintingBaseDTO receiptPrintingBaseDTO) {
        crmFeign.getReceiptPrinting(receiptPrintingBaseDTO);
    }

    @Override
    public List<StoreInfoVO> listStoreStall(StoreInfoDTO storeInfoDTO) {
        return crmFeign.getStoreStall(storeInfoDTO).getResult().getDataList();
    }

    @Override
    public CrmOperatingSubjectRespDTO getOperatingSubject(CrmOperatingSubjectQueryDTO subjectReqDTO) {
        return crmFeign.getOperatingSubject(subjectReqDTO).getResult();
    }

    @Override
    public CrmOrderDetailVo getOrderDetail(CrmOrderDetailQo crmOrderDetailQo) {
        return crmFeign.getOrderDetail(crmOrderDetailQo).getData();
    }

    @Override
    public List<ResOrderCommodity> queryOrderCommodity(QueryOrderCommodity queryOrderCommodity) {
        return crmFeign.queryOrderCommodity(queryOrderCommodity).getResult().getDataList();
    }

    /**
     * @description: 查询主体下的店铺和档口
     * @author: li ao
     * @date: 2024/3/18 14:48
     * @param: query
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.vo.grade.StoreInfoVO>
     **/
    @Override
    public List<StoreInfoVO> getStoreStall(StoreInfoDTO query) {
        return crmFeign.getStoreStall(query).getResult().getDataList();
    }

    /**
     * @description: 查询门店基础信息
     * @author: li ao
     * @date: 2024/3/18 14:56
     * @param: storageByIdQuery
     * @return: java.lang.Object
     **/
    @Override
    public Object getStoreById(StoreByIdQO storageByIdQuery) {
        return crmFeign.getStoreById(storageByIdQuery).getResult().getDataList();
    }


    /**
     * @description: 查询主体下所有门店
     * @author: li ao
     * @date: 2024/3/18 15:15
     * @param: query
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.vo.grade.StoreInfoVO>
     **/
    @Override
    public List<StoreInfoVO> queryStoreV2(QueryStoreBasePage query) {
        return crmFeign.queryStoreV2(query).getDataList();
    }

    /**
     * @description: 查询门店
     * @author: li ao
     * @date: 2024/3/18 15:24
     * @param: queryStoreBasePage
     * @return: com.holderzone.member.common.vo.feign.CrmFeignModel<com.holderzone.member.common.dto.base.StoreBaseInfo>
     **/
    @Override
    public List<StoreBaseInfo> queryStore(QueryStoreBasePage queryStoreBasePage) {
        return crmFeign.queryStore(queryStoreBasePage).getDataList();
    }

    /**
     * @description: 创建订单
     * @author: li ao
     * @date: 2024/3/18 16:19
     * @param: crmOrderDTO
     * @return: com.alibaba.fastjson.JSONObject
     **/
    @Override
    public JSONObject pushOrder(CrmOrderDTO crmOrderDTO) {
        return crmFeign.pushOrder(crmOrderDTO);
    }


    /**
     * @description: 同步状态 | 同步库存
     * @author: li ao
     * @date: 2024/3/18 16:20
     * @param: crmOrderStockDTO
     * @return: com.alibaba.fastjson.JSONObject
     **/
    @Override
    public JSONObject pushOrderStock(CrmOrderStockDTO crmOrderStockDTO) {
        return crmFeign.pushOrderStock(crmOrderStockDTO);
    }

    @Override
    public List<StoreBaseInfo> queryStore(String name) {
        return new ArrayList<>();
    }

    @Override
    public List<OperSubjectVO> listOperSubjectAndApplet() {
        List<OperSubjectVO> operSubjectVOList = new ArrayList<>();
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        CrmFeignModel<OperSubjectVO> subjectAndAppletResult = crmFeign.listOperSubjectAndApplet();
        log.info("[listOperSubjectAndApplet]查询运营主体列表,subjectAndAppletResult={}",
                JacksonUtils.writeValueAsString(subjectAndAppletResult));
        Map<String, OperSubjectVO> operSubjectVOMap = new HashMap<>();
        if (CollUtil.isNotEmpty(subjectAndAppletResult.getDataList())) {
            operSubjectVOMap = subjectAndAppletResult.getDataList().stream()
                    .collect(Collectors.toMap(OperSubjectVO::getOperation_id, Function.identity(), (entity1, entity2) -> entity1));
        }


        // 查询当前用户岗位角色id
        FeignModel<RoleAndPostIdDTO> feignModel = holderFeign.findUserRoleAndPost(headerUserInfo.getEnterpriseGuid(),
                headerUserInfo.getTel());
        log.info("[listOperSubjectAndApplet]查询运营主体列表,RoleAndPostIdDTO={}",
                JacksonUtils.writeValueAsString(feignModel));
        if (Objects.isNull(feignModel) || Objects.isNull(feignModel.getData())) {
            return operSubjectVOList;
        }
        RoleAndPostIdDTO roleAndPostIdDTO = feignModel.getData();
        List<PermissionModelDTO> permissionList = memberMallToolFeign.toOperSubjectPermission(roleAndPostIdDTO);
        log.info("[listOperSubjectAndApplet]查询运营主体权限,roleAndPostIdDTO={},permissionList={}",
                JacksonUtils.writeValueAsString(roleAndPostIdDTO), JacksonUtils.writeValueAsString(permissionList));
        if (CollectionUtils.isEmpty(permissionList)) {
            return operSubjectVOList;
        }
        List<PermissionModelDTO> idList = permissionList.stream()
                .distinct()
                .collect(Collectors.toList());

        for (PermissionModelDTO permissionModelDTO : idList) {
            OperSubjectVO operSubjectVO = new OperSubjectVO();

            operSubjectVO.setOperation_id(permissionModelDTO.getId());
            operSubjectVO.setOperation_name(permissionModelDTO.getPermissionName());

            if (CollUtil.isNotEmpty(operSubjectVOMap) && operSubjectVOMap.containsKey(permissionModelDTO.getId())) {
                OperSubjectVO subjectVO = operSubjectVOMap.get(permissionModelDTO.getId());

                operSubjectVO.setApp_secret(subjectVO.getApp_secret());
                operSubjectVO.setAppId(subjectVO.getAppId());
                operSubjectVO.setAuthorized_app(subjectVO.getAuthorized_app());
            }
            operSubjectVOList.add(operSubjectVO);
        }
        return operSubjectVOList;
    }

    @Override
    public List<QueryOrderDTO> queryOrder(QueryOrderCommodity queryOrderCommodity) {
        return crmFeign.queryOrder(queryOrderCommodity).getResult().getDataList();
    }

    @Override
    public StoreCountDTO countStores () {
        return null;
    }
}
