package com.holderzone.member.common.enums.exception;


import lombok.Getter;

@Getter
public enum OperationLogExceptionEnum {


    LOG_PARAMS_NULL(30000, "日志传递参数不能为空"),

    BUSINESS_GUID_NULL(30001, "业务guid不能为空"),

    ORIGIN_DATA_NULL(30002, "原始数据不能为空"),

    CHANGE_DATA_NULL(30003, "修改数据不能为空"),

    CLASS_TYPE_NULL(30004, "数据类型不能为空"),

    LOG_TYPE_NULL(30005, "业务类型不能为空"),

    OPERATION_ACCOUNT_NULL(30006, "操作用户不能为空"),
    ;

    public int code;
    public String des;

    OperationLogExceptionEnum(final int code, final String des) {
        this.code = code;
        this.des = des;
    }


}
