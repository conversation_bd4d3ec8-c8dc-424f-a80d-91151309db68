package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.pay.*;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.vo.show.SettlementApplyOrderShowVO;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementDiscountDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 结算中心
 *
 * @program: member-settlement
 * @description: ${description}
 * @author: liujie
 * @create: 2023-9-26 10:39:05
 */
@Component
@FeignClient(name = FilterConstant.FEIGN_SETTLEMENT, fallbackFactory = MemberSettlementFeign.ServiceFallBack.class)
public interface MemberSettlementFeign {

    @PostMapping(value = "/callback/initialize_subject_data")
    Result<Void> initializeSubjectData(@RequestBody List<String> operSubjectGuids);

    /**
     * 同步优惠项
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/discount/sync")
    Result<Void> syncSettlementDiscount(@RequestBody SettlementDiscountDTO dto);

    /**
     * 结算中心聚合支付预下单接口
     */
    @PostMapping("/agg/pay")
    AggPayRespDTO pay(@RequestBody SaasAggPayDTO saasAggPayDTO);

    /**
     * 结算中心聚合支付轮询接口
     */
    @PostMapping("/agg/polling")
    AggPayPollingRespDTO polling(@RequestBody SaasPollingDTO saasPollingDTO);

    /**
     * 确认订单页：计算并展示优惠列表
     *
     * @param dto 结算优惠入参
     * @return 优惠结果对象
     */
    @PostMapping(value = "/discount/apply/list")
    Result<SettlementApplyOrderShowVO> list(@RequestBody @Validated SettlementApplyOrderDTO dto);

    /**
     * 确认订单页：计算并展示优惠列表
     *
     * @param dto 结算优惠入参
     * @return 优惠结果对象
     */
    @PostMapping(value = "/discount/apply/unableRuleList")
    Result<SettlementApplyOrderShowVO> unableRuleList(@RequestBody @Validated SettlementApplyOrderDTO dto);

    /**
     * 下单前计算优惠
     *
     * @param dto 订单入参
     * @return 优惠结果对象
     */
    @PostMapping(value = "/discount/apply/calculate")
    Result<SettlementApplyOrderShowVO> calculate(@RequestBody @Validated SettlementApplyOrderDTO dto);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MemberSettlementFeign> {

        @Override
        public MemberSettlementFeign create(Throwable throwable) {
            return new MemberSettlementFeign() {

                @Override
                public Result<Void> initializeSubjectData(List<String> operSubjectGuids) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "initializeSubjectData",
                            JacksonUtils.writeValueAsString(operSubjectGuids), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> syncSettlementDiscount(SettlementDiscountDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "syncSettlementDiscount",
                            JacksonUtils.writeValueAsString(dto), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pay",
                            JacksonUtils.writeValueAsString(saasAggPayDTO), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public AggPayPollingRespDTO polling(SaasPollingDTO saasPollingDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "polling",
                            JacksonUtils.writeValueAsString(saasPollingDTO), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<SettlementApplyOrderShowVO> list(SettlementApplyOrderDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "list",
                            JacksonUtils.writeValueAsString(dto), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<SettlementApplyOrderShowVO> unableRuleList(SettlementApplyOrderDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "unableRuleList",
                            JacksonUtils.writeValueAsString(dto), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<SettlementApplyOrderShowVO> calculate(SettlementApplyOrderDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "calculate",
                            JacksonUtils.writeValueAsString(dto), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
