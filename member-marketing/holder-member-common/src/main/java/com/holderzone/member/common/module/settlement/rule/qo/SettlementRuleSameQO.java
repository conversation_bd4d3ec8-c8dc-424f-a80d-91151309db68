package com.holderzone.member.common.module.settlement.rule.qo;

import com.holderzone.member.common.enums.ChoinceEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 结算规则相同场景、门店
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Accessors(chain = true)
public class SettlementRuleSameQO implements Serializable {


    private static final long serialVersionUID = 6706896045180030503L;

    /**
     * 规则guid
     */
    private String guid;
    /**
     * 规则名称
     */
    private String name;
    /**
     * 主体
     */
    private String operSubjectGuid;

    /**
     * 业务：0全部 1部分
     */
    private Integer applyBusiness;
    /**
     * 业务
     */
    private String applyBusinessStr;

    /**
     * 业务数组
     */
    private List<String> applyBusinessList;

    /**
     * 门店列表
     */
    private List<String> storeGuids;

    /**
     * 门店：0全部 1部分
     *
     * @see com.holderzone.member.common.enums.ChoinceEnum
     */
    private Integer applicableAllStore;

    /**
     * 业务、场景无数据
     *
     * @return
     */
    public boolean validate() {
        return (this.applyBusiness != ChoinceEnum.ALL.getCode() && CollectionUtils.isEmpty(applyBusinessList)) ||
                (this.applicableAllStore != ChoinceEnum.ALL.getCode() && CollectionUtils.isEmpty(storeGuids));
    }
}
