package com.holderzone.member.common.enums;


import com.beust.jcommander.internal.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 * 门店权限标识转换
 */
@AllArgsConstructor
@Getter
public enum SaasIdentificationTransferEnum {

    REDPACKET_ACTIVITY_LIST("随行红包.活动列表", "member.redpacket.activity_list"),
    REDPACKET_ACTIVITY_CREATE("随行红包.新建活动", "member.redpacket.activity_create"),
    REDPACKET_ACTIVITY_PUBLISH("随行红包.发布活动", "member.redpacket.activity_publish"),
    REDPACKET_ACTIVITY_PAUSE("随行红包.暂停活动", "member.redpacket.activity_pause"),
    REDPACKET_ACTIVITY_ENABLE("随行红包.启用活动", "member.redpacket.activity_enable"),
    REDPACKET_ACTIVITY_EDIT("随行红包.编辑活动", "member.redpacket.activity_edit"),
    REDPACKET_ACTIVITY_VIEW("随行红包.查看活动详情", "member.redpacket.activity_view"),
    REDPACKET_ACTIVITY_DEMO("随行红包.试玩游戏", "member.redpacket.activity_demo"),
    REDPACKET_ACTIVITY_STATS_VIEW("随行红包.查看活动统计", "member.redpacket.activity_stats_view"),
    REDPACKET_ACTIVITY_DELETE("随行红包.删除活动", "member.redpacket.activity_delete"),
    REDPACKET_ACTIVITY_STATS_EXPORT("随行红包.导出统计数据", "member.redpacket.activity_stats_export"),
    REDPACKET_ACTIVITY_STATS_DETAIL("随行红包.查看统计数据详情", "member.redpacket.activity_stats_detail"),

    AUTH_GIFT_ACTIVITY_LIST("认证有礼.活动列表", "member.auth_gift.activity_list"),
    AUTH_GIFT_ACTIVITY_CREATE("认证有礼.新建活动", "member.auth_gift.activity_create"),
    AUTH_GIFT_ACTIVITY_PUBLISH("认证有礼.发布活动", "member.auth_gift.activity_publish"),
    AUTH_GIFT_ACTIVITY_PAUSE("认证有礼.暂停活动", "member.auth_gift.activity_pause"),
    AUTH_GIFT_ACTIVITY_ENABLE("认证有礼.启用活动", "member.auth_gift.activity_enable"),
    AUTH_GIFT_ACTIVITY_EDIT("认证有礼.编辑活动", "member.auth_gift.activity_edit"),
    AUTH_GIFT_ACTIVITY_VIEW("认证有礼.查看活动详情", "member.auth_gift.activity_view"),
    AUTH_GIFT_ACTIVITY_STATS_VIEW("认证有礼.查看活动统计", "member.auth_gift.activity_stats_view"),
    AUTH_GIFT_ACTIVITY_DELETE("认证有礼.删除活动", "member.auth_gift.activity_delete"),
    AUTH_GIFT_ACTIVITY_SHARE("认证有礼.分享活动", "member.auth_gift.activity_share"),
    AUTH_GIFT_APPLICATION_DETAIL_VIEW("认证有礼.查看申请详情", "member.auth_gift.application_detail_view"),
    AUTH_GIFT_REVIEW_APPROVE("认证有礼.审核通过", "member.auth_gift.review_approve"),
    AUTH_GIFT_REVIEW_REJECT("认证有礼.审核驳回", "member.auth_gift.review_reject"),

    NTH_DISCOUNT_ACTIVITY_LIST("第N份优惠.活动列表", "member.nth_discount.activity_list"),
    NTH_DISCOUNT_ACTIVITY_CREATE("第N份优惠.新建活动", "member.nth_discount.activity_create"),
    NTH_DISCOUNT_ACTIVITY_PUBLISH("第N份优惠.发布活动", "member.nth_discount.activity_publish"),
    NTH_DISCOUNT_ACTIVITY_PAUSE("第N份优惠.暂停活动", "member.nth_discount.activity_pause"),
    NTH_DISCOUNT_ACTIVITY_START("第N份优惠.开启活动", "member.nth_discount.activity_start"),
    NTH_DISCOUNT_ACTIVITY_EDIT("第N份优惠.编辑活动", "member.nth_discount.activity_edit"),
    NTH_DISCOUNT_ACTIVITY_COPY("第N份优惠.复制活动", "member.nth_discount.activity_copy"),
    NTH_DISCOUNT_ACTIVITY_DETAIL_VIEW("第N份优惠.查看活动详情", "member.nth_discount.activity_detail_view"),
    NTH_DISCOUNT_ACTIVITY_DELETE("第N份优惠.删除活动", "member.nth_discount.activity_delete"),
    NTH_DISCOUNT_ACTIVITY_STATS_VIEW("第N份优惠.查看活动统计", "member.nth_discount.activity_stats_view"),
    NTH_DISCOUNT_ACTIVITY_STATS_EXPORT("第N份优惠.导出统计数据", "member.nth_discount.activity_stats_export"),

    LIMITED_TIME_SALE_ACTIVITY_LIST("限时特价.活动列表", "member.limited_time_sale.activity_list"),
    LIMITED_TIME_SALE_ACTIVITY_CREATE("限时特价.新建活动", "member.limited_time_sale.activity_create"),
    LIMITED_TIME_SALE_ACTIVITY_PUBLISH("限时特价.发布活动", "member.limited_time_sale.activity_publish"),
    LIMITED_TIME_SALE_ACTIVITY_PAUSE("限时特价.暂停活动", "member.limited_time_sale.activity_pause"),
    LIMITED_TIME_SALE_ACTIVITY_ENABLE("限时特价.开启活动", "member.limited_time_sale.activity_enable"),
    LIMITED_TIME_SALE_ACTIVITY_EDIT("限时特价.编辑活动", "member.limited_time_sale.activity_edit"),
    LIMITED_TIME_SALE_ACTIVITY_COPY("限时特价.复制活动", "member.limited_time_sale.activity_copy"),
    LIMITED_TIME_SALE_ACTIVITY_VIEW("限时特价.查看活动详情", "member.limited_time_sale.activity_view"),
    LIMITED_TIME_SALE_ACTIVITY_DELETE("限时特价.删除活动", "member.limited_time_sale.activity_delete"),
    LIMITED_TIME_SALE_ACTIVITY_STATS_VIEW("限时特价.查看活动统计", "member.limited_time_sale.activity_stats_view"),
    LIMITED_TIME_SALE_ACTIVITY_STATS_EXPORT("限时特价.导出统计数据", "member.limited_time_sale.activity_stats_export"),

    CONSUME_GIFT_ACTIVITY_LIST("消费有礼.活动列表", "member.consume_gift.activity_list"),
    CONSUME_GIFT_ACTIVITY_CREATE("消费有礼.新建活动", "member.consume_gift.activity_create"),
    CONSUME_GIFT_ACTIVITY_PUBLISH("消费有礼.发布活动", "member.consume_gift.activity_publish"),
    CONSUME_GIFT_ACTIVITY_PAUSE("消费有礼.暂停活动", "member.consume_gift.activity_pause"),
    CONSUME_GIFT_ACTIVITY_ENABLE("消费有礼.开启活动", "member.consume_gift.activity_enable"),
    CONSUME_GIFT_ACTIVITY_EDIT("消费有礼.编辑活动", "member.consume_gift.activity_edit"),
    CONSUME_GIFT_ACTIVITY_COPY("消费有礼.复制活动", "member.consume_gift.activity_copy"),
    CONSUME_GIFT_ACTIVITY_VIEW("消费有礼.查看活动详情", "member.consume_gift.activity_view"),
    CONSUME_GIFT_ACTIVITY_ORDERS_VIEW("消费有礼.查看活动订单", "member.consume_gift.activity_orders_view"),
    CONSUME_GIFT_ACTIVITY_DELETE("消费有礼.删除活动", "member.consume_gift.activity_delete"),
    CONSUME_GIFT_ACTIVITY_STATS_EXPORT("消费有礼.导出统计数据", "member.consume_gift.activity_stats_export"),
    CONSUME_GIFT_ACTIVITY_REISSUE("消费有礼.补发功能", "member.consume_gift.activity_reissue"),

    CUSTOMER_LEAD_ACTIVITY_LIST("导客单.活动列表", "member.customer_lead.activity_list"),
    CUSTOMER_LEAD_ACTIVITY_CREATE("导客单.新建活动", "member.customer_lead.activity_create"),
    CUSTOMER_LEAD_ACTIVITY_PUBLISH("导客单.发布活动", "member.customer_lead.activity_publish"),
    CUSTOMER_LEAD_ACTIVITY_PAUSE("导客单.暂停活动", "member.customer_lead.activity_pause"),
    CUSTOMER_LEAD_ACTIVITY_ENABLE("导客单.开启活动", "member.customer_lead.activity_enable"),
    CUSTOMER_LEAD_ACTIVITY_EDIT("导客单.编辑活动", "member.customer_lead.activity_edit"),
    CUSTOMER_LEAD_ACTIVITY_COPY("导客单.复制活动", "member.customer_lead.activity_copy"),
    CUSTOMER_LEAD_ACTIVITY_PROMOTE("消费有礼.推广", "member.customer_lead.activity_promote"),
    CUSTOMER_LEAD_ACTIVITY_VIEW("导客单.查看活动详情", "member.customer_lead.activity_view"),
    CUSTOMER_LEAD_ACTIVITY_DELETE("导客单.删除活动", "member.customer_lead.activity_delete"),

    WORK_WEIXIN_CONFIG_VIEW("企微配置.查看", "member.work_weixin.config_view"),
    WORK_WEIXIN_CONFIG_EDIT("企微配置.编辑", "member.work_weixin.config_edit"),

    PORTRAYAL_CONFIG_VIEW("就餐会员画像.查看", "member.portrayal.config_view"),
    PORTRAYAL_CONFIG_EDIT("就餐会员画像.编辑", "member.portrayal.config_edit"),


    ;

    /**
     * 原始值
     */
    private final String original;

    /**
     * 最终值
     */
    private final String last;

    public static List<String> getLastByOriginal(List<String> originals) {
        if (CollectionUtils.isEmpty(originals)) {
            return Lists.newArrayList();
        }
        List<String> lasts = Lists.newArrayList();
        for (SaasIdentificationTransferEnum value : values()) {
            if (originals.contains(value.getOriginal())) {
                lasts.add(value.getLast());
            }
        }
        return lasts;
    }

}
