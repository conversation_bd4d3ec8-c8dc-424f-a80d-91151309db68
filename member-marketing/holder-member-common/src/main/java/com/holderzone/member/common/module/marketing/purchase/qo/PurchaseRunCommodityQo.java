package com.holderzone.member.common.module.marketing.purchase.qo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 运行中限购活动
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PurchaseRunCommodityQo implements Serializable {


    private static final long serialVersionUID = -7666579026009597006L;
    /**
     * 主体
     */
    @JsonIgnore
    private String operSubjectGuid;

    /**
     * 业务（订单类型） :见同步表
     *
     * @see com.holderzone.member.common.enums.growth.ApplyBusinessEnum
     */
    @ApiModelProperty("业务类型")
    private String applyBusinessStr;

    /**
     * 消费门店GUID
     */
    @ApiModelProperty(value = "消费门店GUID")
    private String applyStoreStr;

    /**
     * 下单用户
     */
    @ApiModelProperty(value = "下单用户")
    private String memberGuid;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    @NotBlank(message = "渠道必传！")
    private String channel;

    /**
     * 指定商品编码
     */
    @ApiModelProperty(value = "指定商品编码")
    private List<String> commodityCodes;
}
