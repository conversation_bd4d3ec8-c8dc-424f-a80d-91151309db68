package com.holderzone.member.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 成功or失败
 */
@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
@Getter
@AllArgsConstructor
public enum SucessFailEnum {
    FAIL(0, "失败"),
    SUCCESS(1, "成功");

    private int code;

    /**
     * 信息
     */
    private String des;

    /**
     * 根据code获取name
     *
     * @param code code
     * @return 操作结果
     */
    public static String getDes(Integer code) {
        if(Objects.equals(code, SUCCESS.code)){
            return SUCCESS.getDes();
        }
        return FAIL.getDes();
    }
}
