package com.holderzone.member.common.module.marketing.purchase.qo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 运行中限购活动
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PurchaseMemberCommodityQo extends PurchaseApplyCommodityQo implements Serializable {


    private static final long serialVersionUID = 3192018531796399080L;
    /**
     * 周期性限购查询
     */
    private List<InnerPeriodQo> periodQos;

    /**
     * 活动guid
     */
    private List<String> guidList;

    /**
     * 周期性限购查询
     */
    @Data
    @EqualsAndHashCode(callSuper = false)
    @Accessors(chain = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InnerPeriodQo implements Serializable{

        private static final long serialVersionUID = -2813071120040054731L;
        /**
         * 活动开始时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startTime;

        /**
         * 活动结束时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;

        /**
         * 指定商品编码
         */
        @ApiModelProperty(value = "指定商品编码")
        private List<String> commodityCodes;
    }
}
