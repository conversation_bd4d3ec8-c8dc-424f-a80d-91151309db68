package com.holderzone.member.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信配置参数
 * @date 2021/8/19 9:47
 */
@Component
@Data
@ConfigurationProperties(prefix = "wechat")
public class WeChatConfig {

    private String appId;

    private String appSecret;

    private String appUrl;

    /**
     * 接口调用凭据地址
     */
    private String accessTokenUrl;

    /**
     * 微信小程序二维码地址
     */
    private String wxQRcodeUrl;
}