package com.holderzone.member.common.constant;

/**
 * 微信api
 */
public class WechatApiConstant {

    private WechatApiConstant() {

    }

    /**
     * 小程序获取access_token的url地址
     */
    public static final String MINI_PROGRAM_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";

    /**
     * 获取用户基本信息
     */
    public static final String GET_USER_INFO_URL = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN";

    /**
     * 小程序发送消息通知
     */
    public static final String APPLET_SEND_MESSAGE_URL = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s";

    /**
     * 公众号发送消息通知
     */
    public static final String PUBLIC_SEND_MESSAGE_URL = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s";


}
