package com.holderzone.member.common.helper;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.config.AliPayConfig;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.ali.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 支付宝 请求封装
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AliPayRequestHelper {

    private final AliPayConfig aliPayConfig;

    /**
     * 获取 支付宝请求客户端
     */
    public AlipayClient getAlipayClient(String appId, String privateKey, String aliPublicKey) {
        return new DefaultAlipayClient(
                aliPayConfig.getAliTokenUrl(),
                appId,
                privateKey,
                "json",
                StringConstant.GBK,
                aliPublicKey,
                StringConstant.RSA2);
    }


    /**
     * 用户code授权换取token
     */
    public AlipaySystemOauthTokenResponse oauthTokenRequest(AlipayClient alipayClient, String code) throws AlipayApiException {
        AlipaySystemOauthTokenRequest oauthTokenRequest = new AlipaySystemOauthTokenRequest();
        oauthTokenRequest.setGrantType(StringConstant.AUTHORIZATION_CODE);
        oauthTokenRequest.setCode(code);
        AlipaySystemOauthTokenResponse oauthTokenResponse = alipayClient.execute(oauthTokenRequest);
        log.info("支付宝小程序登录授权返回参数:{}", JacksonUtils.writeValueAsString(oauthTokenResponse));
        return oauthTokenResponse;
    }

    /**
     * 学生身份单次核验接口
     */
    public AlipayCommerceEducateStudentIdentityVerifyResponse identityVerifyRequest(AlipayClient alipayClient,
                                                                                    String bizToken, String accessToken)
            throws AlipayApiException {
        AlipayCommerceEducateStudentIdentityVerifyRequest identityVerifyRequest = new AlipayCommerceEducateStudentIdentityVerifyRequest();
        JSONObject biz = new JSONObject();
        biz.put("biz_token", bizToken);
        identityVerifyRequest.setBizContent(JacksonUtils.writeValueAsString(biz));
        AlipayCommerceEducateStudentIdentityVerifyResponse identityVerifyResponse = alipayClient.execute(identityVerifyRequest, accessToken);
        log.info("支付宝学生身份单次核验接口返回参数:{}", JacksonUtils.writeValueAsString(identityVerifyResponse));
        return identityVerifyResponse;
    }


    /**
     * 卡券模板创建接口
     */
    public AlipayPassTemplateAddResponse passTemplateAddRequest(AlipayClient alipayClient,
                                                                AlipayPassTemplateAddDTO passTemplateAddDTO) {
        try {
            AlipayPassTemplateAddRequest passTemplateAddRequest = new AlipayPassTemplateAddRequest();
            passTemplateAddRequest.setBizContent(JacksonUtils.writeValueAsString(passTemplateAddDTO));
            AlipayPassTemplateAddResponse passTemplateAddResponse = alipayClient.execute(passTemplateAddRequest);
            log.info("支付宝卡券模板创建接口返回参数:{}", JacksonUtils.writeValueAsString(passTemplateAddResponse));
            String body = passTemplateAddResponse.getBody();
            if (StringUtils.isBlank(body)) {
                log.error("创建支付宝卡包券模板异常, 返回body为空");
                return null;
            }
            Map<String, Object> responseMap = JacksonUtils.toMap(body);
            Object alipayPassTemplateAddResponse = responseMap.get("alipay_pass_template_add_response");
            if (Objects.isNull(alipayPassTemplateAddResponse)) {
                log.error("创建支付宝卡包券模板异常: 返回参数为空");
                return null;
            }
            AlipayPassTemplateAddResponse response = JacksonUtils.toObject(AlipayPassTemplateAddResponse.class,
                    JacksonUtils.writeValueAsString(alipayPassTemplateAddResponse));
            if (StringUtils.isBlank(response.getSuccess())) {
                log.error("创建支付宝卡包券模板异常: 返回success为空");
                return null;
            }
            response.setSuccess(response.getSuccess().toUpperCase());
            return response;
        } catch (AlipayApiException e) {
            log.error("创建支付宝卡包券模板异常:{}", e.getMessage());
            return null;
        }
    }

    /**
     * 卡券模板更新接口
     */
    public AlipayPassTemplateUpdateResponse passTemplateUpdateRequest(AlipayClient alipayClient,
                                                                      AlipayPassTemplateUpdateDTO passTemplateUpdateDTO)
            throws AlipayApiException {
        AlipayPassTemplateUpdateRequest passTemplateUpdateRequest = new AlipayPassTemplateUpdateRequest();
        passTemplateUpdateRequest.setBizContent(JacksonUtils.writeValueAsString(passTemplateUpdateDTO));
        AlipayPassTemplateUpdateResponse passTemplateUpdateResponse = alipayClient.execute(passTemplateUpdateRequest);
        log.info("支付宝卡券模板更新接口返回参数:{}", JacksonUtils.writeValueAsString(passTemplateUpdateResponse));
        return passTemplateUpdateResponse;
    }

    /**
     * 卡券实例发放接口
     */
    public AlipayPassInstanceAddResponse passInstanceAddBatchRequest(AlipayClient alipayClient,
                                                                     AlipayPassInstanceBatchAddDTO passInstanceBatchAddDTO) {
        try {
            AlipayUserPassInstancebatchAddRequest passInstanceBatchAddRequest = new AlipayUserPassInstancebatchAddRequest();
            passInstanceBatchAddRequest.setBizContent(JacksonUtils.writeValueAsString(passInstanceBatchAddDTO));
            AlipayUserPassInstancebatchAddResponse passInstanceBatchAddResponse = alipayClient.execute(passInstanceBatchAddRequest);
            log.info("支付宝券包批量发放返回参数:{}", JacksonUtils.writeValueAsString(passInstanceBatchAddResponse));
            String body = passInstanceBatchAddResponse.getBody();
            if (StringUtils.isBlank(body)) {
                log.error("券包批量发放异常, 返回body为空");
                return null;
            }
            Map<String, Object> responseMap = JacksonUtils.toMap(body);
            Object alipayPassInstanceBatchAddResponse = responseMap.get("alipay_user_pass_instancebatch_add_response");
            if (Objects.isNull(alipayPassInstanceBatchAddResponse)) {
                log.error("券包批量发放异常: 返回参数为空");
                return null;
            }
            AlipayPassInstanceAddResponse response =
                    JacksonUtils.toObject(AlipayPassInstanceAddResponse.class, JacksonUtils.writeValueAsString(alipayPassInstanceBatchAddResponse));
            if (StringUtils.isBlank(response.getSuccess())) {
                log.error("券包批量发放异常: 返回success为空");
                return null;
            }
            response.setSuccess(response.getSuccess().toUpperCase());
            return response;
        } catch (AlipayApiException e) {
            log.error("支付宝券包批量发放异常:{}", e.getMessage());
            return null;
        }
    }

    /**
     * 卡券实例发放接口
     */
    public AlipayPassInstanceAddResponse passInstanceAddRequest(AlipayClient alipayClient,
                                                                AlipayPassInstanceAddDTO passInstanceAddDTO) {
        try {
            AlipayPassInstanceAddRequest passInstanceAddRequest = new AlipayPassInstanceAddRequest();
            passInstanceAddRequest.setBizContent(JacksonUtils.writeValueAsString(passInstanceAddDTO));
            AlipayPassInstanceAddResponse passInstanceAddResponse = alipayClient.execute(passInstanceAddRequest);
            log.info("支付宝卡券实例发放接口返回参数:{}", JacksonUtils.writeValueAsString(passInstanceAddResponse));
            return passInstanceAddResponse;
        } catch (AlipayApiException e) {
            log.error("支付宝卡券实例发放异常:{}", e.getMessage());
            return null;
        }
    }

    /**
     * 卡券实例更新接口
     */
    public AlipayPassInstanceUpdateResponse passInstanceUpdateRequest(AlipayClient alipayClient,
                                                                      AlipayPassInstanceUpdateDTO passInstanceUpdateDTO) {
        try {
            AlipayPassInstanceUpdateRequest passInstanceUpdateRequest = new AlipayPassInstanceUpdateRequest();
            passInstanceUpdateRequest.setBizContent(JacksonUtils.writeValueAsString(passInstanceUpdateDTO));
            AlipayPassInstanceUpdateResponse passInstanceUpdateResponse = alipayClient.execute(passInstanceUpdateRequest);
            log.info("支付宝卡券实例更新接口返回参数:{}", JacksonUtils.writeValueAsString(passInstanceUpdateResponse));
            return passInstanceUpdateResponse;
        } catch (AlipayApiException e) {
            log.error("支付宝卡券实例更新异常:{}", e.getMessage());
            return null;
        }
    }

}
