package com.holderzone.member.common.module.marketing.purchase.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseRulePeriodEnum;
import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseRuleTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 运行中限购活动
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PurchaseRunCommodityVO implements Serializable {


    private static final long serialVersionUID = -7666579026009597006L;
    /**
     * 活动id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 活动guid
     */
    @ApiModelProperty(value = "活动guid")
    private String guid;

    /**
     * 限购规则
     *
     * @see PurchaseRuleTypeEnum
     */
    @ApiModelProperty(value = "限购规则:0每人限购 1每单限购 2周期限购")
    private Integer purchaseRuleType;

    /**
     * 限购周期
     *
     * @see PurchaseRulePeriodEnum
     */
    @ApiModelProperty(value = "周期限购：0 每日/人 ，1每周/人，2每月/人")
    private Integer purchaseRulePeriod;

    /**
     * 商品编码
     */
    private String commodityCode;


    /**
     * 限购数量
     */
    private Integer limitNumber;

    /**
     * 活动开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityStartTime;

    /**
     * 活动结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityEndTime;
}
