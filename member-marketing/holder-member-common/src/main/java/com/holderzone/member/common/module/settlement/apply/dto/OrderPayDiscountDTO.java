package com.holderzone.member.common.module.settlement.apply.dto;

import com.holderzone.member.common.enums.order.OrderSourceEnum;
import com.holderzone.member.common.enums.order.OrderStateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 下单锁定优惠
 */
@Data
public class OrderPayDiscountDTO implements Serializable {


    private static final long serialVersionUID = -5239525768641874058L;

    /**
     * 主体
     */
    @ApiModelProperty("主体guid")
    private String operSubjectGuid;

    /**
     * 会员 guid
     */
    @ApiModelProperty("会员guid")
    private String memberInfoGuid;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 订单guid
     */
    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 订单时间
     */
    @ApiModelProperty(value = "订单时间")
    private LocalDateTime orderTime;

    /**
     * 订单状态
     * @see OrderStateEnum
     */
    @ApiModelProperty(value = "订单状态")
    private Integer orderState;

    /**
     * 订单类型
     * @see OrderSourceEnum
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderFee;

    /**
     * 订单优惠金额
     */
    @ApiModelProperty(value = "订单优惠金额")
    private BigDecimal orderDiscountFee;

    /**
     * 订单实收金额
     */
    @ApiModelProperty(value = "订单实收金额")
    private BigDecimal orderActuallyFee;

    /**
     * 订单来源
     */
    @ApiModelProperty(value = "订单来源")
    private Integer orderSource;

    //============= 操作人信息

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String userName;

    /**
     * 用户名
     */
    @ApiModelProperty("手机号")
    private String phoneNum;

    /**
     * 当时积分
     */
    @ApiModelProperty("会员积分")
    private Integer memberIntegral;


    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    private LocalDateTime payTime;
}
