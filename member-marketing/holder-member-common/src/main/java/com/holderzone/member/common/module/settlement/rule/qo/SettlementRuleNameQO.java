package com.holderzone.member.common.module.settlement.rule.qo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <p>
 * 结算规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class SettlementRuleNameQO implements Serializable {

    /**
     * 关键字
     * 应用业务/应用门店编号/应用门店名称模糊搜索
     */
    private String guid;
    /**
     * 主体
     */
    @NotEmpty(message = "规则名称不能为空")
    private String name;
}
