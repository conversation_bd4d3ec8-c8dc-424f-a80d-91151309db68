package com.holderzone.member.common.module.settlement.apply.vo.detail;

import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyDiscountDetailVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SettlementApplyDiscountDetailOfNthVO extends SettlementApplyDiscountDetailVO {

    private static final long serialVersionUID = -3135496341752617040L;


    /**
     * 份数
     */
    private Integer nth;

    /**
     * 折扣力度
     */
    private String discountStrength;
}
