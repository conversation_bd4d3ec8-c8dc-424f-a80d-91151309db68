package com.holderzone.member.common.module.marketing.coupon.use.qo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 作废
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CouponMarkInvalidQO implements Serializable {


    private static final long serialVersionUID = 6735855600320094458L;
    /**
     * 主体
     */
    @JsonIgnore
    private String operSubjectGuid;

    /**
     * 券guid
     */
    @NotBlank(message = "券guid不能为空")
    private String couponGuid;

}
