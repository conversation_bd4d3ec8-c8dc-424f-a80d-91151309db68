package com.holderzone.member.common.module.marketing.purchase.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseRulePeriodEnum;
import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseRuleTypeEnum;
import com.holderzone.member.common.module.marketing.purchase.vo.PurchaseCommodityVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 限量抢购活动参数
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-27
 */
@Data
@Accessors(chain = true)
public class PurchaseDTO implements Serializable {


    private static final long serialVersionUID = -5741892731226150842L;
    /**
     * guid
     */
    @ApiModelProperty(value = "guid")
    private String guid;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    @Length(max = 20, min = 1, message = "活动名称长度必须在1-20之间")
    private String name;
    
    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @NotNull(message = "活动开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityStartTime;


    @ApiModelProperty(value = "活动结束时间")
    @NotNull(message = "活动结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activityEndTime;

    @ApiModelProperty(value = "活动打标签")
    private List<String> applyLabelGuidList;

    /**
     * 限购规则
     *
     * @see PurchaseRuleTypeEnum
     */
    @ApiModelProperty(value = "限购规则:0每人限购 1每单限购 2周期限购")
    @Min(value = 0, message = "限购规则类型不能为空")
    private Integer purchaseRuleType;

    /**
     * 限购周期
     *
     * @see PurchaseRulePeriodEnum
     */
    @ApiModelProperty(value = "周期限购：0 每日/人 ，1每周/人，2每月/人")
    private Integer purchaseRulePeriod;

    /**
     * 限购场景
     */
    @Size(min = 1, message = "限购场景不能为空")
    @NotNull(message = "限购场景不能为空")
    private List<String> applyBusinessList;

    /**
     * 应用门店类型
     *
     * @see com.holderzone.member.common.enums.ChoinceEnum
     */
    @ApiModelProperty(value = "应用门店类型：0全部 1部分")
    @Min(value = 0, message = "应用门店类型不能为空")
    private Integer applyStoreType;

    /**
     * 门店列表
     */
    @ApiModelProperty(value = "门店列表")
    private List<String> applyStoreList;

    /**
     * 商品列表
     */
    @ApiModelProperty(value = "商品列表")
    @NotNull(message = "商品列表不能为空")
    @Size(min = 1, message = "商品列表不能为空")
    private List<PurchaseCommodityVO> applyCommodityList;


}
