package com.holderzone.member.common.enums.portrayal;

/**
 * <AUTHOR>
 * @date 2024/12/20
 * @description 应用设置枚举
 */
public enum ApplySettingEnum {

    REPAST_AIO_DIN(1, "餐饮云一体机端（正餐）"),

    REPAST_POS_DIN(2, "餐饮云POS端（正餐）"),
    ;

    private int code;
    private String desc;

    ApplySettingEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getName(int code) {
        for (FieldTypeEnum applySettingEnum : FieldTypeEnum.values()) {
            if (applySettingEnum.getCode() == code) {
                return applySettingEnum.getDesc();
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
