package com.holderzone.member.common.module.marketing.purchase.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 限购活动规则类型
 *
 * <AUTHOR>
 * @date 2023/11/27
 **/
@Getter
@AllArgsConstructor
public enum PurchaseRuleTypeEnum {

    /**
     * 每人限购
     */
    EVERY_ONE(0, "每人限购"),

    /**
     * 每单限购
     */
    EVERY_ORDER(1, "每单限购"),

    /**
     * 周期限购
     * @see PurchaseRulePeriodEnum
     */
    PERIOD(2, "周期限购");

    private final int code;

    private final String des;

    public static PurchaseRuleTypeEnum get(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (PurchaseRuleTypeEnum anEnum : PurchaseRuleTypeEnum.values()) {
            if (Objects.equals(anEnum.code, code)) {
                return anEnum;
            }
        }
        return null;
    }

}
