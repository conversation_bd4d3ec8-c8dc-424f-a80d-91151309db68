package com.holderzone.member.common.enums.malltool;

import java.util.Objects;

/**
 * 消息类型
 */
public enum MsgTypeEnum {

    MSG_TRADE(1, "交易提醒"),

    MSG_BALANCE(2, "账户余额提醒"),

    MSG_INTEGRATE(3, "积分提醒"),

    MSG_MEMBER(4, "会员信息提醒"),

    MSG_ACTIVITY(5, "活动通知")
    ;

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return desc;
    }

    MsgTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MsgTypeEnum getEnumByName(String name) {

        if (Objects.isNull(name)) {
            return null;
        }
        for (MsgTypeEnum pageTypeEnum : MsgTypeEnum.values()) {
            if (Objects.equals(pageTypeEnum.name(), name)) {
                return pageTypeEnum;
            }
        }
        return null;
    }
}
