package com.holderzone.member.common.assembler;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.Page;
import com.holderzone.member.common.dto.base.ResCommodityBase;
import com.holderzone.member.common.dto.member.ItemWebRespDTO;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.vo.base.CommodityBaseVO;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/22
 * @description saas商品转换类
 */
public class SaasItemAssembler {

    private SaasItemAssembler() {

    }


    public static List<ResCommodityBase> webRespDTOList2ResCommodityBaseList(List<ItemWebRespDTO> webRespDTOList) {
        if (CollectionUtils.isEmpty(webRespDTOList)) {
            return Lists.newArrayList();
        }
        List<ResCommodityBase> resCommodityBaseList = Lists.newArrayList();
        webRespDTOList.forEach(webRespDTO -> {
            ResCommodityBase commodityBase = new ResCommodityBase();
            commodityBase.setCommodityId(webRespDTO.getItemGuid());
            commodityBase.setName(webRespDTO.getName());
            commodityBase.setCommodityCode(webRespDTO.getItemGuid());
            commodityBase.setCommodityComboType(String.valueOf(webRespDTO.getItemType()));
            commodityBase.setBasePrice(getCommodityPrice(webRespDTO));
            commodityBase.setSystem(SystemEnum.REPAST.name());
            resCommodityBaseList.add(commodityBase);
        });
        return resCommodityBaseList;
    }

    public static List<CommodityBaseVO> itemWebRespDTO2CommodityBaseList(List<ItemWebRespDTO> pageData) {
        List<CommodityBaseVO> commodityBaseList = new ArrayList<>();

        pageData.forEach(itemWebRespDTO -> {
            CommodityBaseVO commodityBase = new CommodityBaseVO();
            commodityBase.setCommodityId(itemWebRespDTO.getItemGuid());
            commodityBase.setCommodityCode(itemWebRespDTO.getItemGuid());
            commodityBase.setCommodityName(itemWebRespDTO.getName());
            commodityBase.setCommodityType(itemWebRespDTO.getItemType());
            commodityBase.setCommodityPrice(getCommodityPrice(itemWebRespDTO));
            commodityBaseList.add(commodityBase);
        });

        return commodityBaseList;
    }

    public static Page<CommodityBaseVO> itemWebRespDTOPage2CommodityBaseDTOPage(Page<ItemWebRespDTO> itemWebRespDTOPage) {
        List<ItemWebRespDTO> pageData = itemWebRespDTOPage.getData();


        if (CollectionUtils.isEmpty(pageData)) {
            return new Page<>();
        }
        Page<CommodityBaseVO> commodityBasePage = new Page<>();
        commodityBasePage.setCurrentPage(itemWebRespDTOPage.getCurrentPage());
        commodityBasePage.setPageSize(itemWebRespDTOPage.getPageSize());
        commodityBasePage.setTotalCount(itemWebRespDTOPage.getTotalCount());
        List<CommodityBaseVO> commodityBaseList = SaasItemAssembler.itemWebRespDTO2CommodityBaseList(pageData);
        commodityBasePage.setData(commodityBaseList);
        return commodityBasePage;
    }

    private static String getCommodityPrice(ItemWebRespDTO itemWebRespDTO) {
        BigDecimal minSalePrice = itemWebRespDTO.getMinSalePrice();
        BigDecimal maxSalePrice = itemWebRespDTO.getMaxSalePrice();
        if (Objects.isNull(minSalePrice) && Objects.isNull(maxSalePrice)) {
            return null;
        }
        if (minSalePrice.compareTo(maxSalePrice) == 0) {
            return minSalePrice.setScale(2, RoundingMode.HALF_UP).toPlainString();
        }
        return minSalePrice.setScale(2, RoundingMode.HALF_UP).toPlainString()
                + "-"
                + maxSalePrice.setScale(2, RoundingMode.HALF_UP).toPlainString();
    }
}
