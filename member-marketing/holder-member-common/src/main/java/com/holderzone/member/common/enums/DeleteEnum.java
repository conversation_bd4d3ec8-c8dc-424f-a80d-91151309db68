package com.holderzone.member.common.enums;

/**
 * @ProjectName: member-marketing
 * @ClassName: DeleteEnum
 * @Author: pantao
 * @Description: 是否删除枚举
 * @Date: 2021/8/16 17:52
 * @Version: 1.0
 */
public enum DeleteEnum {

    NOT_DELETE(0,"未删除"),

    DELETE (1,"已删除");

    private final int code;

    private final String des;

    DeleteEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }
}
