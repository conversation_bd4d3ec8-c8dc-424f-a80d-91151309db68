package com.holderzone.member.common.enums.exception;

import com.holderzone.member.common.enums.ResponseBase;

/**
 * <AUTHOR>
 * @description 结算中心异常枚举
 * @date 2023-8-29
 */
public enum MemberSettlementExceptionEnum implements ResponseBase {

    // 数据操作错误定义

    SETTLEMENT_OPTION_ERROR(600002, "结算台暂不支持该优惠类型"),
    ;

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误描述
     */
    private final String des;

    MemberSettlementExceptionEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDes() {
        return des;
    }
}
