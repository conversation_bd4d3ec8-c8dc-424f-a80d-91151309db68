package com.holderzone.member.common.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.ItemNum;
import com.holderzone.member.common.dto.activity.OfflineActivityMemberRelationDTO;
import com.holderzone.member.common.dto.activity.OfflineActivityMemberUploadResultVO;
import com.holderzone.member.common.dto.base.InitSubjectDataDTO;
import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.dto.card.MemberCardOpenDTO;
import com.holderzone.member.common.dto.coupon.MemberCouponStoreListDTO;
import com.holderzone.member.common.dto.event.GrantMemberCouponPackageEvent;
import com.holderzone.member.common.dto.excel.MemberUploadExcelVO;
import com.holderzone.member.common.dto.grade.GradeCardQueryDTO;
import com.holderzone.member.common.dto.member.*;
import com.holderzone.member.common.dto.order.MemberOrderDiscountDTO;
import com.holderzone.member.common.dto.order.ReserveConsumptionDTO;
import com.holderzone.member.common.dto.order.SettlementUnLockedDiscountDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.partner.MemberCircleReqDTO;
import com.holderzone.member.common.dto.partner.MemberCircleRspDTO;
import com.holderzone.member.common.dto.partner.PartnerMemberBaseDTO;
import com.holderzone.member.common.dto.partner.PartnerReserveMemberInfoDTO;
import com.holderzone.member.common.dto.permission.OperSubjectPermissionTypeDTO;
import com.holderzone.member.common.dto.permission.SubjectPermissionDTO;
import com.holderzone.member.common.dto.portrayal.MemberPortrayalDetailsDTO;
import com.holderzone.member.common.dto.redeem.RequestRedeemApplyDTO;
import com.holderzone.member.common.dto.route.FrontendRouteBuildDTO;
import com.holderzone.member.common.dto.subsidy.SubsidyActivityDTO;
import com.holderzone.member.common.dto.subsidy.SubsidyActivityDetailRecordDTO;
import com.holderzone.member.common.dto.subsidy.SubsidyActivityRecordDTO;
import com.holderzone.member.common.dto.user.TokenRequestBO;
import com.holderzone.member.common.entity.activity.ApplyRecordCommodity;
import com.holderzone.member.common.module.base.store.dto.SyncStoreDTO;
import com.holderzone.member.common.module.base.store.dto.SyncStoreQo;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkInvalidQO;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponMarkUseQO;
import com.holderzone.member.common.module.marketing.coupon.use.qo.CouponUseQO;
import com.holderzone.member.common.module.marketing.coupon.use.vo.CouponUseCountVO;
import com.holderzone.member.common.module.marketing.coupon.use.vo.CouponUseVO;
import com.holderzone.member.common.module.marketing.purchase.qo.PurchaseApplyOrderQO;
import com.holderzone.member.common.module.marketing.purchase.qo.PurchaseApplyPageQo;
import com.holderzone.member.common.module.marketing.purchase.qo.PurchaseMemberCommodityQo;
import com.holderzone.member.common.module.marketing.purchase.qo.PurchaseReserveCommodityQO;
import com.holderzone.member.common.module.marketing.purchase.vo.PurchaseApplyCommodityVo;
import com.holderzone.member.common.module.marketing.purchase.vo.PurchaseApplyCountVo;
import com.holderzone.member.common.module.marketing.purchase.vo.PurchaseApplyPageVo;
import com.holderzone.member.common.module.marketing.purchase.vo.PurchaseReserveCommodityVO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderCalculateDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementOrderLockDTO;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyOrderVO;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementSynMarketingDTO;
import com.holderzone.member.common.qo.CardRechargeGiftQO;
import com.holderzone.member.common.qo.activity.FullReductionFoldActivityRunQO;
import com.holderzone.member.common.qo.activity.MemberSubsidyActivityVO;
import com.holderzone.member.common.qo.activity.OfflineActivityMemberRelationQO;
import com.holderzone.member.common.qo.activity.SubsidyReissueByRecord;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.qo.coupon.*;
import com.holderzone.member.common.qo.equities.*;
import com.holderzone.member.common.qo.gift.ActivityDetailQO;
import com.holderzone.member.common.qo.gift.QueryGiftAmountRecordPage;
import com.holderzone.member.common.qo.grade.GradeCommodityBasePageQO;
import com.holderzone.member.common.qo.grade.MemberGradePurchaseHistoryQO;
import com.holderzone.member.common.qo.growth.AppletGrowthDetailPageQO;
import com.holderzone.member.common.qo.growth.AppletGrowthQO;
import com.holderzone.member.common.qo.integral.*;
import com.holderzone.member.common.qo.member.*;
import com.holderzone.member.common.qo.permission.CheckRolePermissionQO;
import com.holderzone.member.common.qo.permission.HsaOperSubjectLabelQO;
import com.holderzone.member.common.qo.specials.LimitSpecialsCommodityRecordQO;
import com.holderzone.member.common.qo.system.AppletBaseInfoQO;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.qo.tool.MessagesSendbatchQO;
import com.holderzone.member.common.vo.activity.FullReductionFoldActivityRunVO;
import com.holderzone.member.common.vo.base.setting.AccountSettingRespVO;
import com.holderzone.member.common.vo.card.*;
import com.holderzone.member.common.vo.coupon.*;
import com.holderzone.member.common.vo.equities.*;
import com.holderzone.member.common.vo.excel.GiftAmountRecordVO;
import com.holderzone.member.common.vo.excel.PurchaseOrderRecordVO;
import com.holderzone.member.common.vo.gift.ActivityDetailVO;
import com.holderzone.member.common.vo.gift.CalculateRechargeGiftRecordVO;
import com.holderzone.member.common.vo.gift.RechargeGiftAmountRecordVO;
import com.holderzone.member.common.vo.gift.RechargeSuccessGiftDetailVO;
import com.holderzone.member.common.vo.grade.*;
import com.holderzone.member.common.vo.growth.GrowthCommodityPageVO;
import com.holderzone.member.common.vo.integral.AppletIntegralDetailVO;
import com.holderzone.member.common.vo.integral.CalculateOrderIntegralDeductVO;
import com.holderzone.member.common.vo.integral.open.AppletOpenIntegralVO;
import com.holderzone.member.common.vo.integral.open.CalculateSignIntegralVO;
import com.holderzone.member.common.vo.integral.open.IntegralTaskInfoVO;
import com.holderzone.member.common.vo.ipass.SubjectVO;
import com.holderzone.member.common.vo.member.*;
import com.holderzone.member.common.vo.order.OrderConsumptionVO;

import com.holderzone.member.common.vo.system.AppletBaseInfoVO;
import feign.Response;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;



/**
 * 新会员管理
 *
 * <AUTHOR>
 * @date 2022/2/11
 **/
@Component
@FeignClient(name = FilterConstant.FEIGN_MEMBER, fallbackFactory = MemberBaseFeign.ServiceFallBack.class)
public interface MemberBaseFeign {

    @PostMapping(value = "/recharge_gift/get_post_recharge_success_gift_detail")
    Result<RechargeSuccessGiftDetailVO> getRechargeSuccessGiftDetail(@RequestBody CardRechargeGiftQO cardRechargeGiftQO);

    @PostMapping("/applets/get_base_info")
    Result<AppletBaseInfoVO> getBaseInfo(@RequestBody AppletBaseInfoQO baseInfoQO);

    /**
     * 会员卡支付 前置校验
     */
    @PostMapping(value = "/ter-card/preCheckMemberCardPayNew", produces = "application/json;charset=utf-8")
    String preCheckMemberCardPay(@RequestBody CheckMemberCardPayQO checkMemberCardPayQO);

    /**
     * 订单退款 优惠活动记录回调
     */
    @ApiOperation("订单退款 优惠活动记录回调")
    @PostMapping(value = "/ter-card/bark_order_discount_callback", produces = "application/json;charset=utf-8")
    void barkOrderDiscountCallback(@RequestBody BarkOrderDiscountCallbackQO barkOrderDiscountCallbackQO);

    /**
     *  订单完成 优惠活动记录回调
     */
    @ApiOperation("优惠活动记录回调")
    @PostMapping(value = "/ter-card/order_discount_callback", produces = "application/json;charset=utf-8")
    void afterOrderDiscountCallback(@RequestBody AfterOrderDiscountCallbackQO afterOrderDiscountCallbackQO);

    /**
     * 下单后锁定优惠
     *
     * @param dto 订单参数
     * @return 优惠列表
     */
    @PostMapping(value = "/member/discount/locked")
    Result<Boolean> lockedDiscount(@RequestBody @Validated SettlementOrderLockDTO dto);

    /**
     * 取消订单、退款：释放优惠
     * 食堂调用的是 ： /accumulation_discount_release_key
     * @param dto 订单参数
     * @return 优惠列表
     */
    @PostMapping(value = "/member/discount/unLocked")
    Result<Boolean> lockedDiscount(@RequestBody @Validated SettlementUnLockedDiscountDTO dto);

    @PostMapping("/redeem/apply/doRedeem")
    boolean redeem(@RequestBody RequestRedeemApplyDTO dto);

    @PostMapping("/integral_rule/update_relation_rule")
    void updateRelationRule(@RequestBody SettlementSynMarketingDTO discountOptionSynDTO);

    @PostMapping("/member_grade/update_member_discount_relation_rule")
    void updateMemberDiscountRelationRule(@RequestBody SettlementSynMarketingDTO discountOptionSynDTO);

    @PostMapping("/full/reduction/fold/query_settle_commodity_run")
    List<FullReductionFoldActivityRunVO> querySettleCommodityByRun(@RequestBody FullReductionFoldActivityRunQO query);

    /**
     * 保存或更新记录
     * @param recordDTO 请求参数 DTO
     * @return 保存或更新后的记录
     */
    @PostMapping("/member/everyday/record/saveOrUpdate")
    Result<String> saveOrUpdate(@RequestBody MemberEverydayRecordQO recordDTO);


    @PostMapping("/member/everyday/record/calculateIntegralCheck")
    Result<Integer> calculateIntegralCheck(@RequestBody MemberEverydayRecordQO recordDTO);

    /**
     * 计算签到任务 七天赠送积分
     */
    @GetMapping("/integral/get/integral/calculate/task/day")
    Result<CalculateSignIntegralVO> calculateSignInTaskSevenDayIntegral();

    /**
     * 获取限时特价 商品锁定记录
     */
    @PostMapping(value = "/member/discount/getLimitSpecialsCommodityRecord")
    Map<String, List<ApplyRecordCommodity>> getLimitSpecialsCommodityRecord(@RequestBody LimitSpecialsCommodityRecordQO limitSpecialsCommodityRecordQO);

    @GetMapping("/queryOrderStatusByOrderNumber")
    OrderConsumptionVO queryOrderStatusByOrderNumber(@RequestParam(value = "orderNumber") String orderNumber);

    @PostMapping("/purchase/order/apply/order/count")
    Map<String, Long> applyOrderCount(@RequestBody PurchaseApplyOrderQO qo);

    @PostMapping("/purchase/order/apply/page")
    PageResult<PurchaseApplyPageVo> pageApply(@RequestBody PurchaseApplyPageQo qo);

    @PostMapping("/purchase/order/apply/count")
    PurchaseApplyCountVo applyCount(@RequestBody PurchaseApplyPageQo qo);

    /**
     * 获取积分任务详情
     */
    @PostMapping("/integral/get/task_info")
    Result<IntegralTaskInfoVO> getTaskInfo(@RequestBody AppletGrowthQO qo);

    /**
     * 查询积分明细
     */
    @PostMapping("/integral/get/integral/detail")
    Result<PageResult<AppletIntegralDetailVO>> getAppletIntegralDetail(AppletGrowthDetailPageQO qo);

    /**
     * 小程序查询积分统计
     *
     * @param qo （积分）传递参数
     * @return 查询结果
     */
    @PostMapping("/integral/get/integral/total/detail")
    Result<AppletOpenIntegralVO> getIntegralTotalDetail(@RequestBody AppletGrowthQO qo);

    /**
     * 查询会员本地门店（crm同步到会员库中）
     *
     * @param qo
     * @return
     */
    @PostMapping(value = "/store/page")
    Result<PageResult<SyncStoreDTO>> pageSyncStore(@RequestBody @Validated SyncStoreQo qo);


    /**
     * 适配消息调用
     *
     * @param cardGrantEquitiesDTO cardGrantEquitiesDTO
     */
    @PostMapping("/equities_center/grantFeignCardRights")
    @ApiOperation(value = "适配消息调用")
    void grantFeignCardRights(@RequestBody CardEquitiesQO cardGrantEquitiesDTO);

    /**
     * 会员卡权益发送
     */
    @PostMapping("/equities_center/giveCardRightsJob")
    @ApiOperation(value = "会员卡权益发送")
    void giveCardRightsJob(GrantMemberCouponPackageEvent giveCardRightsJob);

    /**
     * 过期权益推送
     *
     * @param getEquipesPreviewVO
     */
    @PostMapping("/equities_center/sendExpireCardEquities")
    @ApiOperation(value = "过期权益推送")
    void sendExpireCardEquities(@RequestBody SendCardEquitiesQO getEquipesPreviewVO);

    /**
     * 获取门店基础数据
     */
    @PostMapping(value = "/hsa-member/get_operation_member_info_list_by_guids")
    List<MemberInfoVO> getOperationMemberInfoList(@RequestBody MemberListQO query);


    @PostMapping(value = "/applets/coupon/sendMemberCouponNotice", produces = "application/json;charset=utf-8")
    void sendMemberCouponNotice(@RequestBody List<MemberCouponPackageVO> memberCouponLinks);

    @GetMapping(value = "/applets/coupon/sendMemberCouponExpireNotice", produces = "application/json;charset=utf-8")
    void sendMemberCouponExpireNotice();

    @PostMapping(value = "/applets/coupon/xxl_job/autoExpire")
    void couponAutoExpire();

    @PostMapping("/equities_center/get_apply_module")
    @ApiOperation(value = "获取适用模块")
    Result<ApplyModuleVO> getApplyModule();

    /**
     * 批量会员关联批量标签
     *
     * @param qo qo
     * @return
     */
    @ApiOperation("批量会员关联批量标签")
    @PostMapping("/label/addMemberInfoLabel")
    Result addMemberInfoLabel(@RequestBody AddMemberLabelCorrelationQO qo);

    /**
     * 批量操作自动关联或者取关
     *
     */
    @ApiOperation("批量操作自动关联或者取关")
    @PostMapping("/label/updateCorrelationStatus")
    Result updateCorrelationStatus(@RequestBody UpdateLabelCorrelationStatusQO qo);

    /**
     * 定时发放任务
     */
    @GetMapping(value = "/hsa-member-coupon-package-link/xxlJobCouponPackageGrant", produces = "application/json;charset=utf-8")
    void xxlJobCouponPackageGrant();

    /**
     * 查询会员标签列表
     *
     * @return 查询结果
     */
    @PostMapping("/label/listOperGuid")
    List<MemberLabelListVO> listLabelOperGuid(HsaOperSubjectLabelQO operatorLabelQO);

    /**
     * 获取等级权益商品基础数据
     *
     * @param qo qo
     */
    @ApiOperation("获取等级权益商品基础数据")
    @PostMapping(value = "/hsa-crm/queryGradeCommodityNew", produces = "application/json;charset=utf-8")
    GrowthCommodityPageVO queryGradeCommodityNew(@RequestBody GradeCommodityBasePageQO qo);

    /**
     * 批量调整会员成长值
     */
    @PostMapping("/hsa-member/update_member_growth")
    Result updateMemberGrowth(@RequestBody RequestMemberGrowthValue request);

    @PostMapping(value = "/recharge_gift/fixedEffectiveGiftAmount")
    void fixedEffectiveGiftAmount();

    @PostMapping(value = "/recharge_gift/getActivityOrderNum")
    Map<String, Integer> getActivityOrderNum(@RequestBody List<String> activityGuidList);

    @PostMapping(value = "/recharge_gift/queryRechargeGiftAmountRecordPage")
    PageResult<RechargeGiftAmountRecordVO> queryRechargeGiftAmountRecordPage(@RequestBody QueryGiftAmountRecordPage query);

    @PostMapping(value = "/recharge_gift/calculateRechargeAmountRecord")
    Result<CalculateRechargeGiftRecordVO> calculateRechargeAmountRecord(@RequestBody QueryGiftAmountRecordPage query);

    @PostMapping(value = "/recharge_gift/exportRechargeGiftAmountRecord")
    GiftAmountRecordVO exportRechargeGiftAmountRecord(@RequestBody QueryGiftAmountRecordPage query);

    @PostMapping(value = "/recharge_gift/getRechargeGiftActivityBase")
    ActivityDetailVO getRechargeGiftActivityBase(@RequestBody ActivityDetailQO activityDetailQO);


    /**
     * 会员等级列表
     */
    @ApiOperation("会员等级列表")
    @PostMapping("/member_grade/query_member_grade_list")
    Result<QueryMemberGradeVO> queryGrowthValueTaskDetail(@RequestParam(value = "roleType", required = false) String roleType,
                                                          @RequestParam(value = "gradeType", required = false) Integer gradeType);

    /**
     * 根据类型获取指定租户的会员列表
     */
    @ApiOperation("根据类型获取指定租户的会员列表")
    @PostMapping("/member_grade/query_member_grade_info_list")
    Result<List<HsaMemberGradeInfoVO>> queryMemberGradeInfoList(@RequestParam(value = "roleType", required = false) String roleType,
                                                                @RequestParam(value = "gradeType", required = false) Integer gradeType);

    @ApiOperation("根据会员等级id获取等级信息")
    @GetMapping("/member_grade/get")
    Result<HsaMemberGradeInfoVO> getMemberGradeInfoByGuid(@RequestParam(value = "gradeGuid") String gradeGuid);


    /**
     * 新增会员等级购买流水
     */
    @PostMapping("/member_grade_purchase/add")
    Result<Void> addMemberGradePurchaseHistory(@RequestBody MemberGradePurchaseHistoryQO qo);

    /**
     * 根据查询条件获取会员等级卡列表
     * @param gradeCardQueryDTO 查询条件
     * @return 会员等级卡列表
     */
    @PostMapping("/member_grade_card/list")
    Result<List<MemberGradeCardVO>> getGradeCardList(@RequestBody GradeCardQueryDTO gradeCardQueryDTO);


    /**
     * 更新会员等级卡信息
     * @param gradeCardQueryDTO 更新信息
     */
    @PostMapping("/member_grade_card/update")
    Result<Void> updateGradeCard(@RequestBody GradeCardQueryDTO gradeCardQueryDTO);

    @PostMapping("/member_grade_card/add")
    Result<Void> addGradeCard(@RequestBody GradeCardQueryDTO gradeCardQueryDTO);

    /**
     * 保存积分抵扣订单记录
     *
     * @param deductDetailVO
     */
    @PostMapping(value = "/applets/occupy_order_deduct_detail")
    Result<Boolean> saveIntegralDeductDetail(@RequestBody OrderIntegralDeductDetailQO deductDetailVO);


    /**
     * 释放积分抵扣订单明细
     *
     * @param releaseOrderDetailVO
     */
    @PostMapping(value = "/applets/release_order_deduct_detail")
    void releaseIntegralDeductDetail(@RequestBody ReleaseOrderDeductDetailQO releaseOrderDetailVO);

    /**
     * 导出限购订单记录
     *
     * @param qo
     * @return
     */
    @PostMapping("/purchase/order/get/purchase/export")
    PurchaseOrderRecordVO exportPurchaseOrderRecord(@RequestBody PurchaseApplyPageQo qo);

    /**
     * 订单生成回调处理
     *
     * @param qo BindingReleaseKeyQO
     */
    @ApiOperation("订单生成回调处理")
    @PostMapping(value = "/applets/card/order_generate_callback_processing", produces = "application/json;charset=utf-8")
    void orderGenerateCallbackProcessing(@RequestBody List<OrderGenerateCallbackQO> qo);


    /**
     * 释放权益并删除折扣记录
     *
     * @param accumulationReleaseKeyQO
     */
    @ApiOperation("释放权益并删除折扣记录")
    @PostMapping(value = "/applets/card/accumulation_discount_release", produces = "application/json;charset=utf-8")
    void accumulationDiscountRelease(@RequestBody AccumulationReleaseKeyQO accumulationReleaseKeyQO);


    /**
     * 获取会员等级折扣商品
     *
     * @param qo MemberPriceApplyCommodityQO
     * @return MemberPriceApplyCommodityVO
     */
    @ApiOperation("获取会员等级折扣商品")
    @PostMapping(value = "/applets/card/get_member_price_commodity", produces = "application/json;charset=utf-8")
    Result<MemberPriceApplyCommodityVO> getMemberPriceApplyCommodity(@RequestBody MemberPriceApplyCommodityQO qo);

    /**
     * 计算会员折扣
     */
    @ApiOperation("获取会员等级折扣商品")
    @PostMapping(value = "/member/discount/calculateMemberDiscount")
    Result<SettlementApplyOrderVO> calculateMemberDiscount(@RequestBody SettlementApplyOrderDTO settlementApplyOrderDTO);

    /**
     * 查询订单优惠记录列表
     *
     * @param dto 订单锁定参数
     * @return 订单优惠记录列表
     */
    @PostMapping(value = "/member/discount/listOrderDiscount")
    Result<List<MemberOrderDiscountDTO>> listOrderDiscount(@RequestBody @Validated SettlementOrderLockDTO dto);


    /**
     * 获取最新等级图标
     *
     * @return MemberNewGradeDTO
     */
    @PostMapping("/applets/grade/get_grade_icon")
    List<MemberNewGradeDTO> getNewCurrentGradeIconList(@RequestBody List<String> memberGuid);


    /**
     * 获取门店基础数据
     *
     * @param keyword request model
     * @return Result
     */
    @GetMapping(value = "/hsa-base/getQueryStoreInfo", produces = "application/json;charset=utf-8")
    Result<List<StoreBaseInfo>> queryStoreInfo(@RequestParam(value = "keyword") String keyword);

    /**
     * 会员实体卡支付
     *
     * @param request
     * @return
     */
    @ApiOperation("会员实体卡支付")
    @PostMapping(value = "/ter-card/card_pay_order", produces = "application/json;charset=utf-8")
    Result<ConsumptionRespVO> payOrder(@RequestBody RequestConfirmPayVO request);

    /**
     * 消费权益回调
     *
     * @param terOrderCallbackQO memberConsumptionGuid
     */
    @ApiOperation("消费权益回调")
    @PostMapping(value = "/ter-card/order_rights_callback", produces = "application/json;charset=utf-8")
    void payOrderRightsCallback(@RequestBody TerOrderCallbackQO terOrderCallbackQO);

    /**
     * 查询标签列表
     *
     * @param labelGuidList 标签guidList
     * @return 查询标签列表
     */
    @PostMapping(value = "/label/listLabelSet", produces = "application/json;charset=utf-8")
    List<LabelSiftVO> listLabelSet(@RequestBody List<String> labelGuidList);

    /**
     * 标签刷新-异步执行
     *
     * @param memberGuid       会员guid
     * @param hsaLabelSettings 标签
     * @param assignLabel      1指定标签 0自动标签
     * @param sourceType       来源
     * @param triggerType      触发类型 LabelTriggerTypeEnum
     * @param operSubjectGuid  主体
     */
    @ApiOperation("标签刷新")
    @GetMapping("label/feignRefreshLabel")
    void refreshLabel(@RequestParam(value = "memberGuid") List<String> memberGuid,
                      @RequestParam(value = "hsaLabelSettings") List<String> hsaLabelSettings,
                      @RequestParam(value = "assignLabel") int assignLabel,
                      @RequestParam(value = "sourceType") Integer sourceType,
                      @RequestParam(value = "triggerType") Integer triggerType,
                      @RequestHeader("operSubjectGuid") String operSubjectGuid);

    /**
     * 分页查询
     *
     * @param query query
     * @return 操作结果
     */
    @ApiOperation("分页查询会员卡")
    @PostMapping(value = "/hsa-card/getCardInfoPage", produces = "application/json;charset=utf-8")
    Result<Page<QueryCardInfoPageVO>> getCardInfoPage(@RequestBody QueryCardInfoPageQO query);

    /**
     * 查询卡基础信息
     *
     * @param cardGuid cardGuid
     * @return CardInfoQO
     */
    @ApiOperation("查询卡基础信息")
    @GetMapping(value = "/hsa-card/getCardInfoByGuid", produces = "application/json;charset=utf-8")
    Result<CardBaseInfoVO> getCardInfoByGuid(@RequestParam(value = "cardGuid") String cardGuid);

    /**
     * 查询会员列表
     *
     * @param memberListQo 查询会员列表QO
     * @return 查询结果
     */
    @PostMapping("/hsa-member/list_member_info")
    Result<PageResult> listMemberInfo(@RequestBody @Validated MemberListQO memberListQo);

    /**
     * 查询标签列表
     *
     * @param memberLabelListQo 查询标签列表请求qo
     * @return 查询结果
     */
    @PostMapping("/label/list_member_label")
    Result<PageResult> listMemberLabel(@RequestBody MemberLabelListQO memberLabelListQo);

    /**
     * 新增、编辑标签
     *
     * @param requestOperationLabelQO 标签操作请求和返回参数
     */
    @PostMapping("/label/save_or_update_automatic_label")
    Result saveOrUpdateAutomaticLabel(@RequestBody RequestOperationLabel requestOperationLabelQO);

    /**
     * 查询标签数
     *
     * @param labelGuid 标签guid
     * @return 操作结果
     */
    @PostMapping("/label/getMemberNum")
    Result<Integer> countLabelMember(@RequestBody List<String> labelGuid);

    /**
     * 会员导入模板下载地址
     *
     * @return 操作结果
     */
    @GetMapping(value = "/card_operation/download_excel_url", produces = "application/json;charset=utf-8")
    Result<String> downloadExcelUrl();

    /**
     * 新建卡选择会员导入
     *
     * @param fileUrl  文件url
     * @param cardGuid cardGuid
     * @return 操作结果
     */
    @GetMapping(value = "/hsa-card/memberCardUploadExcelUrl", produces = "application/json;charset=utf-8")
    Result<MemberUploadExcelVO> memberUploadExcelUrl(@RequestParam(value = "fileUrl") String fileUrl,
                                                     @RequestParam(value = "cardGuid") String cardGuid);

    /**
     * 获取所有运营主体权限信息
     *
     * @return 操作结果
     */
    @GetMapping("/member_permission/get_all_subject_permission")
    List<SubjectPermissionDTO> getAllSubjectPermission();

    /**
     * 获取所有运营主体类型权限信息
     *
     * @return 操作结果
     */
    @GetMapping("/subject_permission_type/get_all_subject_type_permission")
    List<OperSubjectPermissionTypeDTO> getAllSubjectTypePermission();

    @PostMapping("/subsidy_activity/list_activity_page")
    Result subsidyActivityPageList(@RequestBody RequestSubsidyActivityQO requestSubsidyActivityQO);

    @PostMapping("/subsidy_activity/subsidy_activity_code")
    List<String> subsidyActivityPageListNew(@RequestBody RequestSubsidyActivityQO requestSubsidyActivityQO);

    @PostMapping("/subsidy_activity/save_or_update")
    Result saveOrUpdate(@RequestBody MemberSubsidyActivityVO subsidyActivityVO);

    @GetMapping("/subsidy_activity/get_detail/{guid}")
    Result getDetailByGuid(@PathVariable("guid") String guid);

    @DeleteMapping("/subsidy_activity/delete/{guid}")
    Result deleteByGuid(@PathVariable("guid") String guid);

    @PutMapping("/subsidy_activity/update_activity_status")
    Result updateSubsidyActivityStatus(@RequestParam("guid") String guid, @RequestParam("status") Integer status);

    @GetMapping("/subsidy_activity/init_subsidy_data")
    Result initSubsidyData();

    @PostMapping("/subsidy_activity/get_subsidy_activity_by_state")
    Result getSubsidyActivityByState(@RequestBody List<Integer> status);

    @GetMapping("/subsidy_activity/get_enable_subsidy_activity")
    Result getEnableSubsidyActivity();

    @PostMapping("/subsidy_activity/batch_update_status_value")
    void batchUpdateStatusValue(@RequestBody List<SubsidyActivityDTO> subsidyActivityDTOList);

    @PostMapping("/subsidy_activity/batch_update_status")
    Result batchUpdateStatus(@RequestBody List<SubsidyActivityDTO> hsaSubsidyActivities);

    @PostMapping("/subsidy_activity/get_subsidy_by_guid")
    Result getSubsidyByGuid(@RequestBody List<String> guids);


    @PostMapping("/subsidy_record_detail/list_detail_page")
    Result subsidyRecordDetailPageList(@RequestBody RequestSubsidyRecordDetailQO request);

    @PostMapping("/subsidy_record_detail/export")
    Result<List<SubsidyRecordDetailVO>> subsidyRecordDetailExport(@RequestBody RequestSubsidyRecordDetailQO request);

    @PostMapping("/subsidy_record_detail/send_subsidy")
    Result subsidyReissueByRecord(@RequestBody SubsidyReissueByRecord request);

    @PostMapping("/subsidy_record_detail/subsidy_statistical")
    Result subsidyRecordStatistical(@RequestBody @Validated RequestSubsidyRecordDetailQO request);

    @GetMapping("/subsidy_record_detail/get_recyclable_subsidy_activity")
    Result getRecyclableSubsidyActivity();

    @PostMapping("/subsidy_record_detail/save_batch_subsidy_detail")
    Result saveBatchSubsidyDetail(@RequestBody List<SubsidyActivityDetailRecordDTO> subsidyActivityDetailRecords);

    @PostMapping("/subsidy_record_detail/get_subsidy_detail_by_guid")
    Result getSubsidyDetailByGuid(@RequestBody List<String> guids);

    @PostMapping("/subsidy_record_detail/batch_update_status")
    Result batchUpdateDetailStatus(@RequestBody List<SubsidyActivityDetailRecordDTO> subsidyActivityDetailRecords);

    @PostMapping("/subsidy_record_detail/update_subsidy_recycling")
    Result updateSubsidyRecycling(@RequestBody List<String> guids);

    @PostMapping("/subsidy_record_detail/update_subsidy_detail")
    Result batchUpdateSubsidyDetail(@RequestBody SubsidyActivityDetailRecordDTO subsidyActivityDetailRecordDTO);

    @PostMapping("/subsidy_record/list_page")
    Result subsidyRecordPageList(@RequestBody RequestSubsidyRecordQO requestSubsidyActivityQO);

    @GetMapping("/subsidy_record/get_detail/{subsidyRecordGuid}")
    Result getDetail(@PathVariable("subsidyRecordGuid") String subsidyRecordGuid);

    @GetMapping("/subsidy_record/send_subsidy")
    Result subsidyReissueByRecord(@RequestParam(value = "recordGuid") String recordGuid);

    @PostMapping("/subsidy_record/find_all_soon_overdue_subsidy")
    Result findAllSoonOverdueSubsidy(@RequestBody List<String> detailRecordGuids);

    @PostMapping("/subsidy_record/save_batch_subsidy_record")
    Result saveBatchSubsidyRecord(@RequestBody List<SubsidyActivityRecordDTO> subsidyActivityRecordDTOS);

    @PostMapping("/subsidy_record/batch_update_status")
    Result batchUpdateRecordStatus(@RequestBody List<SubsidyActivityRecordDTO> subsidyActivityRecordDTOS);

    @PostMapping("/subsidy_record/get_subsidy_record_by_guid")
    Result<List<SubsidyActivityRecordDTO>> getSubsidyRecordByGuid(@RequestBody List<String> guids);

    @PostMapping("/subsidy_record/update_subsidy_record")
    Result<Boolean> updateSubsidyRecord(@RequestBody SubsidyActivityRecordDTO subsidyActivityRecordDTO);

    @GetMapping("/type_config/get_business_type")
    Result<List<ApplyTypeVO>> getBusinessType();

    /**
     * 查看会员详情
     *
     * @return 操作结果
     */
    @GetMapping(value = "/hsa-member/get_detail", produces = "application/json;charset=utf-8")
    Result<OperationMemberDetailVO> getOperationMemberDetail(@RequestParam("guid") String guid);

    @ApiOperation("查询会员名称")
    @PostMapping(value = "/hsa-member/findMemberNameByGuid")
    Result<String> findMemberNameByGuid(@RequestParam(value = "memberGuid") String memberGuid);

    @ApiOperation("增加角色类型")
    @PostMapping(value = "/hsa-member/add_role_type")
    Result<Void> addRoleType(@RequestParam(value = "phoneNum") String phoneNum, @RequestParam(value = "roleType") String roleType);

    @ApiOperation("删除角色类型")
    @PostMapping(value = "/hsa-member/delete_role_type")
    Result<Void> deleteRoleType(@RequestParam(value = "phoneNum") String phoneNum, @RequestParam(value = "roleType") String roleType);

    @GetMapping("/member_circle/list/{memberGuid}")
    List<MemberCircleRspDTO> listPartnerMemberCircle(@PathVariable("memberGuid") String memberGuid);

    @PostMapping("/member_circle/list_by_members")
    List<MemberCircleRspDTO> listPartnerMemberCircleByMemberGuids(@RequestBody List<String> memberGuidList);

    @PostMapping("/member_circle/get")
    MemberCircleRspDTO getMemberCircle(@RequestBody MemberCircleReqDTO circleReqDTO);

    @PostMapping("/member_circle/activity_join/{memberGuid}")
    Boolean activityJoin(@PathVariable("memberGuid") String memberGuid);

    @PostMapping("/member_circle/list_non_guids")
    List<PartnerMemberBaseDTO> listNonGuids(@RequestBody List<String> friendGuidList);

    @GetMapping("/member_circle/list_search_member")
    List<PartnerMemberBaseDTO> searchPartnerMember(@RequestParam(value = "searchContent", required = false) String searchContent, @RequestParam("index") int index);

    @PostMapping("/member_circle/list_by_guids")
    List<PartnerMemberBaseDTO> listByGuids(@RequestBody List<String> friendGuidList);

    @GetMapping("/equities_center/current")
    List<MemberGradeEquitiesPartnerVO> currentQuery();

    @PostMapping("/activity/offline/member/relation")
    void offlineActivityMemberRelation(@RequestBody OfflineActivityMemberRelationDTO relationDTO);

    @PostMapping("/activity/offline/member/relation/list")
    List<MemberInfoVO> offlineActivityMemberRelationList(@RequestBody OfflineActivityMemberRelationQO query);

    @PostMapping("/activity/offline/member/relation/page")
    PageResult<MemberInfoVO> offlineActivityMemberRelationPage(@RequestBody OfflineActivityMemberRelationQO query);

    @PostMapping("/activity/offline/member/relation/list/count")
    List<OfflineActivityMemberRelationDTO> offlineActivityMemberRelationListCount(@RequestBody OfflineActivityMemberRelationQO query);

    @PostMapping("/activity/offline/member/relation/list/activityGuids")
    List<String> offlineActivityQueryActivityGuids(@RequestBody OfflineActivityMemberRelationQO query);

    @GetMapping("/activity/offline/member/relation/download_excel_url")
    String offlineActivityDownloadExcelUrl();

    @GetMapping("/activity/offline/member/relation/upload")
    Result<OfflineActivityMemberUploadResultVO> offlineActivityUploadMember(@RequestParam("fileUrl") String fileUrl,
                                                                            @RequestParam("activityGuid") String activityGuid);

    @PostMapping("/activity/offline/member/relation/export")
    Response offlineActivityExportMember(@RequestBody OfflineActivityMemberRelationQO query);

    @GetMapping("/member_circle/list_offline_activity/{memberGuid}")
    List<String> getOfflineActivityByMemberGuid(@PathVariable("memberGuid") String memberGuid);

    @ApiOperation("获取当前用户操作权限")
    @PostMapping("/member_permission/check_account_permission")
    MemberSystemPermissionVO getAccountPermission(@RequestBody CheckRolePermissionQO query);

    @ApiOperation("预定会员信息查询")
    @GetMapping("/hsa-member/query_member_by_guid_list")
    List<PartnerReserveMemberInfoDTO> queryMemberByGuidList(@RequestParam(value = "memberGuidList") List<String> memberGuidList);

    @ApiOperation("就餐会员信息查询")
    @GetMapping("/hsa-member/query_member_by_phone_list")
    List<PartnerReserveMemberInfoDTO> queryMemberByPhoneList(@RequestParam(value = "phoneList") List<String> phoneList);

    /**
     * 增加预定消费记录
     */
    @PostMapping("/consumption/save_reserve")
    void saveReserve(@RequestBody ReserveConsumptionDTO consumptionDTO);

    @PostMapping(value = "/member_info_card_total/save_today_card_amount", produces = "application/json;charset=utf-8")
    Result<Void> saveTodayCardAmount();

    /**
     * 分页查询明细
     *
     * @param qo
     * @return
     */
    @PostMapping(value = "/hsa-member-coupon-package-link/pageDetail")
    Result<PageResult<CouponPackageGiveVO>> pagePackageDetail(@RequestBody CouponPackageGiveQO qo);

    /**
     * 分页查询明细
     *
     * @param qo
     * @return
     */
    @PostMapping(value = "/hsa-member-coupon-package-link/pageDetail")
    Result<PageResult<CouponPackageGiveExportVO>> pagePackageExportDetail(@RequestBody CouponPackageGiveQO qo);

    /**
     * 发送优惠券
     */
    @ApiOperation("发送优惠券")
    @PostMapping("/hsa-member/sendMemberCoupon")
    Result<Boolean> sendMemberCoupon(@RequestBody MemberSendCouponQO memberSendCouponQO);

    /**
     * 发放数量统计
     *
     * @param qo
     * @return
     */
    @PostMapping(value = "/hsa-member-coupon-package-link/countGiveNum")
    Result<CouponPackageGiveCountVO> countPackageGiveNum(@RequestBody CouponPackageGiveQO qo);

    /**
     * 分页查询明细
     *
     * @param qo
     * @return
     */
    @PostMapping(value = "/hsa-member-coupon-link/pageDetail")
    Result<PageResult<CouponGiveVO>> pageCouponDetail(@RequestBody CouponGiveQO qo);

    /**
     * 核销数量统计
     *
     * @param qo
     * @return
     */
    @PostMapping(value = "/member_coupon_use/countUseNum")
    Result<CouponUseCountVO> countCouponUseNum(@RequestBody CouponUseQO qo);

    /**
     * 分页查询明细
     *
     * @param qo
     * @return
     */
    @PostMapping(value = "/member_coupon_use/pageDetail")
    Result<PageResult<CouponUseVO>> pageCouponUseDetail(@RequestBody CouponUseQO qo);

    /**
     * 券：标记使用
     *
     * @param qo 标记参数
     * @return
     */
    @PostMapping(value = "/member_coupon_use/markUse")
    Result<Void> markUse(@RequestBody CouponMarkUseQO qo);

    /**
     * 券作废
     *
     * @param qo 券类型
     * @return
     */
    @PostMapping(value = "/member_coupon_use/invalid")
    Result<Void> invalid(@RequestBody CouponMarkInvalidQO qo);

    /**
     * 分页查询明细
     *
     * @param qo
     * @return
     */
    @PostMapping(value = "/hsa-member-coupon-link/pageDetail")
    Result<PageResult<CouponGiveExportVO>> pageCouponExportDetail(@RequestBody CouponGiveQO qo);

    /**
     * 发放数量统计
     *
     * @param qo
     * @return
     */
    @PostMapping(value = "/hsa-member-coupon-link/countGiveNum")
    Result<CouponGiveCountVO> countCouponGiveNum(@RequestBody CouponGiveQO qo);

    @PostMapping(value = "/hsa-member-coupon-link/countPackageActivity")
    Map<String, CouponPackageCountVO> countPackageActivity(@RequestBody PackageListCountQO qo);

    @PostMapping(value = "/hsa-member-coupon-link/countSellableCouponPackageActivity")
    Map<String, SellableCouponPackageCountVO> countSellableCouponPackageActivity(@RequestBody SellableCouponPackageListCountQO qo);

    @PostMapping(value = "/hsa-member-coupon-link/countCoupon")
    List<ItemNum> countCoupon(@RequestBody CouponListCountQO qo);

    @PostMapping(value = "/hsa-member-coupon-link/countListCoupon")
    Map<String, CouponListCountVO> countListCoupon(@RequestBody CouponListCountQO qo);

    /**
     * 积分抵现
     *
     * @param qo
     * @return
     */
    @PostMapping(value = "/applets/calculate_order_integral_deduct")
    Result<CalculateOrderIntegralDeductVO> calculateOrderIntegralDeduct(@RequestBody CalculateIntegralDeductQO qo);

    /**
     * 计算订单折扣
     *
     * @param qo CalculateMemberPriceCommodityQO
     * @return CalculateMemberPriceCommodityVO
     */
    @ApiOperation("计算订单折扣")
    @PostMapping(value = "/applets/card/calculate_member_price_commodity", produces = "application/json;charset=utf-8")
    Result<List<CalculateMemberPriceCommodityVO>> calculateMemberPriceCommodity(@RequestBody CalculateMemberPriceQO qo);

    /**
     * 会员优惠项列表
     *
     * @param dto 查询参数
     * @return 优惠项
     */
    @PostMapping(value = "/member/discount/list")
    List<SettlementApplyOrderVO> listDiscount(@RequestBody SettlementApplyOrderDTO dto);

    /**
     * 计算优惠
     *
     * @param dto 查询参数
     * @return 优惠项
     */
    @PostMapping(value = "/member/discount/calculate")
    List<SettlementApplyOrderVO> calculateDiscount(@RequestBody SettlementApplyOrderCalculateDTO dto);

    /**
     * 保存公众号openId和unionId
     */
    @PostMapping(value = "/wx_mp/open_id_and_union_id/save", produces = "application/json;charset=utf-8")
    void saveOpenIdAndUnionId(@RequestBody TokenRequestBO request);

    @PostMapping(value = "/wechat_message/send_batch", produces = "application/json;charset=utf-8")
    void wechatMessageSendBatch(@RequestBody MessagesSendbatchQO messagesSendBatchQuery);

    @PostMapping(value = "/wechat_message/send", produces = "application/json;charset=utf-8")
    void wechatMessageSend(@RequestBody MessagesSendQO messagesSendQO);

    @PostMapping(value = "/short_message/send_batch", produces = "application/json;charset=utf-8")
    void shortMessageSendBatch(@RequestBody MessagesSendbatchQO messagesSendBatchQuery);

    @PostMapping(value = "/purchase/order/commodity/member", produces = "application/json;charset=utf-8")
    List<PurchaseApplyCommodityVo> listMemberPurchaseCommodity(@RequestBody PurchaseMemberCommodityQo qo);


    @PostMapping(value = "/purchase/order/commodity/member/reserve", produces = "application/json;charset=utf-8")
    List<PurchaseReserveCommodityVO> listMemberPurchaseCommodity(@RequestBody PurchaseReserveCommodityQO qo);

    @PostMapping(value = "/hsa-base/autoInit", produces = "application/json;charset=utf-8")
    void autoInitSubjectData(@RequestBody InitSubjectDataDTO initSubjectData);

    /**
     * 查看会员标签等级
     *
     * @return 操作结果
     */
    @GetMapping(value = "/hsa-member/get/member/label/grade", produces = "application/json;charset=utf-8")
    MemberLabelGradeVO getMemberLabelAndGradeInfo(@RequestParam("guid") String guid);

    /**
     * 查看会员y以及卡详情
     *
     * @return 操作结果
     */
    @GetMapping(value = "/hsa-member/getOperationMemberCardInfo", produces = "application/json;charset=utf-8")
    OperationMemberCardInfoVO getOperationMemberCardInfo(@RequestParam("guid") String guid);

    @GetMapping(value = "/hsa-member-coupon-link/listByCodeRecord")
    Map<String, Integer> listByCodeRecord(@RequestBody CouponDtlQO couponDtlQO);

    /**
     * 获取会员信息
     *
     * @param dto 请求参数
     * @return 会员信息
     */
    @GetMapping(value = "/hsa-member/getMemberInfo")
    Result<MemberBasicInfoVO> getMemberInfo(@RequestBody MemberQueryDTO dto);

    /**
     * 获取会员信息
     *
     * @param dto 请求参数
     * @return 会员信息
     */
    @PostMapping(value = "/hsa-member/getMemberByOpenid")
    Result<MemberBasicInfoVO> getMemberByOpenid(@RequestBody MemberQueryDTO dto);

    /**
     * 登录会员
     */
    @PostMapping(value = "/ter-card/login_member_card")
    Result<BaseLoginMemberCardVO> loginMemberCard(@RequestBody TerLoginMemberCardQO terLoginMemberCardQO);

    /**
     * 批量获取会员信息
     * @param dto 请求参数
     * @return 会员信息
     */
    @GetMapping(value = "/hsa-member/batch/getMemberInfo")
    Result<List<MemberBasicInfoVO>> batchGetMemberInfo(@RequestBody MemberQueryDTO dto);


    /**
     * 通过会员手机号查询当前运营主体下会员列表
     */
    @PostMapping(value = "/hsa-member/get_operation_member_info_list_by_phone")
    Result<PageResult<MemberInfoVO>> getOperationMemberInfoListByPhone(@RequestBody MemberListQO qeury);

    /**
     * 获取会员账户信息
     *
     * @param dto 请求参数
     * @return 会员信息
     */
    @GetMapping(value = "/hsa-member/memberAccountInfo")
    Result<MemberAccountInfoVO> memberAccountInfo(@RequestBody MemberQueryDTO dto);


    /**
     * 新增会员信息
     *
     * @param dto 请求参数
     * @return 会员信息
     */
    @PostMapping(value = "/hsa-member/addMemberInfo")
    Result<MemberBasicInfoVO> addMemberInfo(@RequestBody MemberAddDTO dto);

    /**
     * 修改会员信息
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/hsa-member/updateMemberInfo")
    Result<Void> updateMemberInfo(@RequestBody MemberUpdateDTO dto);

    /**
     * 查询支付会员卡
     *
     * @param dto 请求参数
     * @return 会员卡列表
     */
    @PostMapping(value = "/member_info_card/list_pay_member_card")
    Result<List<PayMemberCardVO>> listPayMemberCard(@RequestBody MemberCardQueryDTO dto);

    /**
     * 查询我的会员卡
     *
     * @param qo 请求参数
     * @return 会员卡列表
     */
    @PostMapping(value = "/applets/list_mini_program_card")
    Result<List<MiniProgramCardDTO>> listMyMemberCard(@RequestBody ListMiniProgramCardQO qo);

    /**
     * 查询发卡状态
     *
     * @param cardGuid 会员卡guid
     * @return 发卡状态
     */
    @PostMapping(value = "/applets/query_open_card_rule_status")
    Result<Integer> queryOpenCardRuleStatus(@RequestParam("cardGuid") String cardGuid);

    /**
     * 开通会员卡
     *
     * @param dto 请求参数
     * @return 开卡结果
     */
    @PostMapping(value = "/applets/mini_program_open_card")
    Result<AppletOpenCardVO> openCard(@RequestBody MemberCardOpenDTO dto);

    /**
     * 开通会员卡（校验）
     *
     * @param dto 请求参数
     * @return 校验结果
     */
    @PostMapping(value = "/applets/card/openCardPayCheck")
    Result<Void> openCardPayCheck(MemberCardOpenCardPayCheckDTO dto);

    /**
     * 开通会员卡（付费）
     *
     * @param dto 请求参数
     * @return 订单号
     */
    @PostMapping(value = "/applets/card/openCardPay")
    Result<String> openCardPay(@RequestBody MemberCardOpenCardPayDTO dto);

    /**
     * 查询会员卡详情
     *
     * @param dto 请求参数
     * @return 会员卡详情
     */
    @PostMapping(value = "/member_info_card/get_member_card_info")
    Result<AppletMemberCardDetail> getMemberCardInfo(@RequestBody MemberCardInfoQueryDTO dto);

    /**
     * 查询会员卡余额合计
     *
     * @param qo 请求参数
     * @return 余额合计
     */
    @PostMapping(value = "/applets/get_balance_detail_total")
    Result<BalanceDetailTotalVO> getMemberCardBalanceTotal(@RequestBody AppletBalanceDetailQO qo);

    /**
     * 查询会员卡余额记录
     *
     * @param qo 请求参数
     * @return 余额合计
     */
    @PostMapping(value = "/applets/get_applet_balance_record")
    Result<PageResult> getMemberCardBalanceRecord(AppletBalanceDetailQO qo);

    /**
     * 查询会员卡二维码
     *
     * @param dto 请求参数
     * @return 会员卡二维码
     */
    @PostMapping(value = "/member_info_card/get_member_card_qrcode")
    Result<MemberCardQrCodeVO> getQrCode(@RequestBody MemberCardInfoQueryDTO dto);

    /**
     * 修改会员卡密码
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    @PostMapping(value = "/member_info_card/update_member_card_pwd")
    Result<Boolean> updatePwd(@RequestBody MemberCardPwdUpdateDTO dto);

    /**
     * 修改默认会员卡
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    @PostMapping(value = "/member_info_card/update_member_card_default")
    Result<Boolean> updateDefault(@RequestBody MemberCardDefaultUpdateDTO dto);

    /**
     * 绑定实体卡
     *
     * @param bindingPhysicalCardQO 请求参数
     * @return 修改结果
     */
    @PostMapping(value = "/applets/binding_physical_card/by_card_num")
    Result<PhysicalCardResultVO> bindPhysicalCard(@RequestBody BindingPhysicalCardQO bindingPhysicalCardQO);

    /**
     * 会员卡充值页面
     *
     * @param memberInfoCardGuid 会员持卡guid
     * @return 充值页面
     */
    @GetMapping("/applets/card/recharge_page")
    Result<CardWeChatRechargeDataVO> rechargePage(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid,
                                                  @RequestParam(value = "storeGuid", required = false, defaultValue = "0") String storeGuid);

    /**
     * 计算预计到账金额
     *
     * @param preMoneyQO 请求参数
     * @return 查询结果
     */
    @PostMapping(value = "/applets/card/calculate_pre_money")
    Result<RechargeThresholdVO> calculatePreMoney(@RequestBody MemberCalculatePreMoneyQO preMoneyQO);

    /**
     * 会员卡充值
     *
     * @param terMemberCardRechargeQO 请求参数
     * @return 修改结果
     */
    @PostMapping(value = "/applets/card/pay_recharge")
    Result<RechargeRespVO> recharge(@RequestBody TerMemberCardRechargeQO terMemberCardRechargeQO);

    /**
     * 会员卡充值-现金
     *
     * @param terMemberCardRechargeQO 请求参数
     * @return 修改结果
     */
    @PostMapping(value = "/ter-card/recharge")
    Result<RechargeRespVO> cashRecharge(@RequestBody TerMemberCardRechargeQO terMemberCardRechargeQO);

    /**
     * 查询所有可开通得会员卡
     */
    @GetMapping(value = "/member_info_card/list_all_able_ecard_channel")
    Result<List<AbleECardVO>> listAllAbleECardByChannel();

    /**
     * 会员卡密码校验
     *
     * @param dto 请求参数
     * @return 校验结果
     */
    @PostMapping(value = "/member_info_card/member_card_pwd_check")
    Result<Boolean> memberCardPwdCheck(@RequestBody MemberCardPwdCheckDTO dto);

    /**
     * 会员支付记录批量
     *
     * @param dtoList
     * @return
     */
    @PostMapping(value = "/ter-card/member_order_record_list", produces = "application/json;charset=utf-8")
    Result<Boolean> memberCardPayRecordRequest(List<MemberCardPayDTO> dtoList);

    /**
     * 会员卡支付
     *
     * @param dtoList 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/ter-card/member_card_pay_request")
    Result<Boolean> memberCardPay(List<MemberCardPayDTO> dtoList);

    /**
     * 会员卡支付
     *
     * @param payDTO 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/ter-card/member_card_pay")
    Result<ConsumptionRespVO> memberCardPay(RequestMemberCardPayVO payDTO);


    /**
     * 现金支付
     *
     * @param request 订单信息
     * @return 订单信息
     */
    @PostMapping("/ter-card/cash_pay_order")
    Result<ConsumptionRespVO> cashPayOrder(@RequestBody RequestConfirmPayVO request);


    /**
     * 会员卡退款
     *
     * @param payDTO 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/ter-card/member_order_refund")
    Result<ConsumptionRespVO> memberOrderRefund(RequestMemberOrderRefundVO payDTO);

    /**
     * 会员卡支付回调
     *
     * @param dtoList 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/ter-card/member_card_pay_callback")
    Result<Boolean> memberCardPayCallback(List<MemberCardPayCallbackDTO> dtoList);

    /**
     * 会员卡退款
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/ter-card/member_card_refund_request")
    Result<Boolean> memberCardRefund(MemberCardRefundDTO dto);

    /**
     * 通过运营主体获取对应资料项设置的列表信息
     *
     * @param operSubjectGuid 运营主体
     * @return 资料项列表
     */
    @GetMapping("/data_item/list_info")
    Result<List<DataItemSetVO>> listInfo(@RequestParam("operSubjectGuid") String operSubjectGuid);

    /**
     * 校验会员资料项
     */
    @PostMapping("/data_item/check_user_data_item")
    Boolean checkUserDataItem(@RequestBody CheckDataItemDTO dataItemDTO);

    /**
     * 获取会员付费等级信息
     * @param guid 会员guid
     * @param roleType 角色类型
     * @return 会员付费等级信息
     */
    @GetMapping(value = "/member_grade/member_paid_grade_level")
    Result<GradeLevelVO> getMemberPaidGradeLevel(@RequestParam("guid") String guid, @RequestParam(value = "roleType", required = false) String roleType);

    /**
     * 获取会员免费等级信息
     * @param guid 会员guid
     * @param roleType 角色类型
     * @return 会员免费等级信息
     */
    @GetMapping(value = "/member_grade/member_free_grade_level")
    Result<GradeLevelVO> getMemberFreeGradeLevel(@RequestParam("guid") String guid, @RequestParam(value = "roleType", required = false) String roleType);

    /**
     * 会员等级权益预览
     * @param memberGradeGuid 权益业务guid
     * @param memberInfoGuid 用户guid
     * @param sourceType 来源
     * @see com.holderzone.member.common.enums.equities.EquitiesSourceTypeEnum
     * @param effective        若是等级是否查询有效
     * @return 会员等级权益预览
     */
    @GetMapping("/member_grade/get_grade_equities")
    Result<GradePreviewVO> getEquitiesPreviewInfo(@RequestParam("memberGradeGuid") String memberGradeGuid,
                                                  @RequestParam("memberInfoGuid") String memberInfoGuid,
                                                  @RequestParam("sourceType") String sourceType,
                                                  @RequestParam("effective") Integer effective);

    /**
     * 通过equitiesGuid获取权益详情
     */
    @GetMapping("/equities_center/get_equities_info/{equitiesGuid}")
    Result<EquitiesInfoDetailVO> getEquitiesInfoByGuid(@PathVariable("equitiesGuid") String equitiesGuid);

    /**
     * 根据类型获取指定租户的会员列表
     * @param gradeType 等级类型 1:付费会员 0:免费会员
     * @return 会员列表
     */
    @GetMapping("/listMembersByGradeType")
    Result<List<MemberBasicInfoVO>> listMembersByGradeType(@RequestParam("gradeType") Integer gradeType);

    /**
     * 根据查询条件获取会员等级信息
     * @param gradeType 等级类型
     * @param vipGrade 等级
     * @return 会员等级信息
     */
    @GetMapping("/member_grade/info")
    Result<HsaMemberGradeInfoVO> queryMemberGradeInfo(@RequestParam(value = "gradeType") Integer gradeType,
                                                      @RequestParam(value = "vipGrade") Integer vipGrade);

    /**
     * 获取会员当前可用积分
     * @return 可用积分
     */
    @PostMapping("/integral/get/usable/integral")
    Result<Integer> getUsableIntegral(UsableIntegralQO usableIntegralQO);

    /**
     * 商城订单调整积分
     *
     * @param request 调整积分请求参数
     * @return 操作结果
     */
    @PostMapping("/integral/update/member/integral")
    Result<Boolean> updateMemberIntegral(@RequestBody MallOrderIntegralQO request);

    /**
     * 按时间分页查询已领券
     * @param qo 查询参数
     * @return 券列表
     */
    @PostMapping("/applets/coupon/pageMemberCouponByTime")
    Result<List<MemberCouponWxVO>> pageMemberCouponByTime(@RequestBody MemberCouponListQO qo);

    /**
     * 分页查询会员优惠券（按时间排序）
     *
     * @param qo 查询条件
     * @return 分页结果
     */
    @PostMapping("/applets/coupon/pageableMemberCouponByTime")
    Result<PageResult<MemberCouponWxVO>> pageableMemberCouponByTime(@RequestBody MemberCouponQO qo);

    /**
     * 优惠券详情
     * @param memberCouponGuid 优惠券guid
     * @return 优惠券详情
     */
    @GetMapping("/applets/coupon/detail/{memberCouponGuid}")
    Result<MemberCouponWxDetailVO> detail(@PathVariable("memberCouponGuid") String memberCouponGuid);

    /**
     * 批量获取优惠券详情
     * @param memberCouponGuids 会员优惠券GUID列表
     * @return 优惠券详情列表
     */
    @PostMapping("/applets/coupon/batchDetails")
    Result<List<MemberCouponWxDetailVO>> batchDetails(@RequestBody List<String> memberCouponGuids);

    /**
     * 获取二维码
     * @param memberCouponGuid 会员优惠券guid
     * @return 二维码
     */
    @GetMapping("/applets/coupon/getQrcode")
    Result<CouponQrCodeVO> getQrCode(@RequestParam("memberCouponGuid") String memberCouponGuid);

    /**
     * 会员账户详情: 发放数量统计
     * @param qo 查询参数
     * @return 券统计
     */
    @PostMapping("/applets/coupon/countMemberNum")
    Result<MemberCouponWxCountVO> countMemberNum(@RequestBody MemberCouponNumQO qo);

    /**
     * 查询优惠卷适用门店
     *
     * @param dto 查询条件
     * @return 查询结果
     */
    @PostMapping(value = "/applets/get_member_coupon_store_list")
    Result getMemberCouponStoreList(@RequestBody MemberCouponStoreListDTO dto);

    /**
     * 查询会员活动领取次数
     *
     * @param activityGuid 活动GUID
     * @param memberGuid 会员GUID
     * @return 领取次数
     */
    @GetMapping(value = "/hsa-member-coupon-package-link/countMemberActivityReceive")
    Result<Integer> countMemberActivityReceive(@RequestParam("activityGuid") String activityGuid,
                                               @RequestParam("memberGuid") String memberGuid);

    /**
     * 用户主动领券接口
     *
     * @param request 领券请求参数
     * @return 领券结果
     */
    @PostMapping(value = "/hsa-member-coupon-package-link/selfReceiveCouponPackage")
    Result<SelfReceiveCouponPackageVO> selfReceiveCouponPackage(SelfReceiveCouponPackageQO request);

    /**
     * 查询会员画像
     */
    @ApiOperation("查询会员画像")
    @PostMapping("/hsa-member/queryMemberPortrayal")
    Result<MemberPortrayalDetailsDTO> queryMemberPortrayal(@RequestBody MemberQueryDTO memberQueryDTO);

    @ApiOperation("查询会员消费明细")
    @PostMapping("/consumption/get_consumption_detail")
    Result<PageResult<ConsumptionDetailVO>> getConsumptionDetail(@RequestBody ConsumptionDetailQO request);

    /**
     * 根据券码查询优惠券信息
     *
     * @param couponCode 券码
     */
    @GetMapping("/ter-card/getByCouponCode")
    Result<CouponGiveVO> getCouponGiveVOByCouponCode(@RequestParam(value = "couponCode") String couponCode);


    @PostMapping("/frontend/route/build")
    Result<String> buildFrontendRoute(@RequestBody FrontendRouteBuildDTO dto);


    /**
     * 获取用户在企业下的功能权限
     *
     * @return 功能权限
     */
    @GetMapping(value = "/permission/queryIdentificationNames")
    Result<List<String>> queryIdentificationNames();

    /**
     * 根据账号查询所属企业有权限的运营主体
     *
     * @param identificationName 权限标识
     * @return 运营主体
     */
    @GetMapping(value = "/permission/querySubjects")
    Result<List<SubjectVO>> querySubjects(@RequestParam("identificationName") String identificationName);


    /**
     * 查询设置
     */
    @GetMapping("/account_setting/query")
    Result<AccountSettingRespVO> queryAccountSetting();

    /**
     * 获取会员卡余额规则
     */
    @GetMapping("/card_balance_rule/get")
    Result<HsaCardBalanceRuleVO> getBalanceRule();

    /**
     * 查询会员周消费频次
     */
    @PostMapping("/hsa-member/statistics/queryMemberWeekConsumeFrequency")
    Result<MemberWeekConsumeFrequencyStatisticsVO> queryMemberWeekConsumeFrequency();

    /**
     * 查询会员性别统计
     */
    @PostMapping("/hsa-member/statistics/queryMemberSexDistribution")
    Result<MemberSexStatisticsVO> queryMemberSexDistribution();

    /**
     * 查询会员年龄统计
     */
    @PostMapping("/hsa-member/statistics/queryMemberAgeDistribution")
    Result<List<Long>> queryMemberAgeDistribution(@RequestBody MemberAgeDistributionQO memberAgeDistributionQO);

    /**
     * 查询会员近些日增长统计
     */
    @PostMapping("/hsa-member/statistics/queryMemberIncrease")
    Result<List<MemberIncreaseStatisticsVO>> queryMemberIncrease(@RequestBody MemberIncreaseQO memberIncreaseQO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MemberBaseFeign> {

        @Override
        public MemberBaseFeign create(Throwable throwable) {
            MemberBaseFeign memberBaseFeign = new MemberBaseFeign() {


                @Override
                public Result<RechargeSuccessGiftDetailVO> getRechargeSuccessGiftDetail(CardRechargeGiftQO cardRechargeGiftQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getRechargeSuccessGiftDetail", cardRechargeGiftQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<AppletBaseInfoVO> getBaseInfo(AppletBaseInfoQO baseInfoQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getBaseInfo", baseInfoQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String preCheckMemberCardPay(CheckMemberCardPayQO checkMemberCardPayQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "barkOrderDiscountCallback", checkMemberCardPayQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void barkOrderDiscountCallback(BarkOrderDiscountCallbackQO barkOrderDiscountCallbackQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "barkOrderDiscountCallback", barkOrderDiscountCallbackQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void afterOrderDiscountCallback(AfterOrderDiscountCallbackQO afterOrderDiscountCallbackQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "afterOrderDiscountCallback", afterOrderDiscountCallbackQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Boolean> lockedDiscount(SettlementOrderLockDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "lockedDiscount", dto, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Boolean> lockedDiscount(SettlementUnLockedDiscountDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "lockedDiscount", dto, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean redeem(RequestRedeemApplyDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "redeem", dto, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateRelationRule(SettlementSynMarketingDTO discountOptionSynDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateRelationRule", discountOptionSynDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateMemberDiscountRelationRule(SettlementSynMarketingDTO discountOptionSynDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateMemberDiscountRelationRule", discountOptionSynDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<FullReductionFoldActivityRunVO> querySettleCommodityByRun(FullReductionFoldActivityRunQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "querySettleCommodityByRun", query, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<String> saveOrUpdate(MemberEverydayRecordQO recordDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveOrUpdate", recordDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Integer> calculateIntegralCheck(MemberEverydayRecordQO recordDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "calculateIntegralCheck", recordDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<CalculateSignIntegralVO> calculateSignInTaskSevenDayIntegral() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "calculateSignInTaskSevenDayIntegral", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, List<ApplyRecordCommodity>> getLimitSpecialsCommodityRecord(LimitSpecialsCommodityRecordQO limitSpecialsCommodityRecordQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getLimitSpecialsCommodityRecord", limitSpecialsCommodityRecordQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public OrderConsumptionVO queryOrderStatusByOrderNumber(String orderNumber) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryOrderStatusByOrderNumber", orderNumber, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, Long> applyOrderCount(PurchaseApplyOrderQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "applyOrderCount", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PageResult<PurchaseApplyPageVo> pageApply(PurchaseApplyPageQo qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageApply", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PurchaseApplyCountVo applyCount(PurchaseApplyPageQo qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "applyCount", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<IntegralTaskInfoVO> getTaskInfo(AppletGrowthQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getTaskInfo", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult<AppletIntegralDetailVO>> getAppletIntegralDetail(AppletGrowthDetailPageQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAppletIntegralDetail", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<AppletOpenIntegralVO> getIntegralTotalDetail(AppletGrowthQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getIntegralTotalDetail", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult<SyncStoreDTO>> pageSyncStore(SyncStoreQo qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageSyncStore", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void grantFeignCardRights(CardEquitiesQO cardGrantEquitiesDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "grantFeignCardRights", cardGrantEquitiesDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void giveCardRightsJob(GrantMemberCouponPackageEvent giveCardRightsJob) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageSyncgiveCardRightsJobStore", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void sendExpireCardEquities(SendCardEquitiesQO getEquipesPreviewVO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "sendExpireCardEquities", getEquipesPreviewVO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<MemberInfoVO> getOperationMemberInfoList(MemberListQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getOperationMemberInfoList", query, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void sendMemberCouponNotice(List<MemberCouponPackageVO> memberCouponLinks) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "sendMemberCouponNotice", memberCouponLinks, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void sendMemberCouponExpireNotice() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "sendMemberCouponExpireNotice", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void couponAutoExpire() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "couponAutoExpire", null, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result getApplyModule() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getApplyModule", null, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result addMemberInfoLabel(AddMemberLabelCorrelationQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "addMemberInfoLabel", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result updateCorrelationStatus(UpdateLabelCorrelationStatusQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateCorrelationStatus", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void xxlJobCouponPackageGrant() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "xxlJobCouponPackageGrant", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<MemberLabelListVO> listLabelOperGuid(HsaOperSubjectLabelQO operatorLabelQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listLabelOperGuid", operatorLabelQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public GrowthCommodityPageVO queryGradeCommodityNew(GradeCommodityBasePageQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryGradeCommodityNew", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result updateMemberGrowth(RequestMemberGrowthValue request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateMemberGrowth", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void fixedEffectiveGiftAmount() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "fixedEffectiveGiftAmount", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, Integer> getActivityOrderNum(List<String> activityGuidList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getActivityOrderNum", activityGuidList, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PageResult<RechargeGiftAmountRecordVO> queryRechargeGiftAmountRecordPage(QueryGiftAmountRecordPage query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryRechargeGiftAmountRecordPage", query, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<CalculateRechargeGiftRecordVO> calculateRechargeAmountRecord(QueryGiftAmountRecordPage query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "calculateRechargeAmountRecord", query, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public GiftAmountRecordVO exportRechargeGiftAmountRecord(QueryGiftAmountRecordPage query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "exportRechargeGiftAmountRecord", query, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ActivityDetailVO getRechargeGiftActivityBase(ActivityDetailQO activityDetailQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getRechargeGiftActivityBase", activityDetailQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<QueryMemberGradeVO> queryGrowthValueTaskDetail(String roleType, Integer gradeType) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryGrowthValueTaskDetail", roleType, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<HsaMemberGradeInfoVO>> queryMemberGradeInfoList(String roleType, Integer gradeType) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryMemberGradeInfoList", roleType, gradeType, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<HsaMemberGradeInfoVO> getMemberGradeInfoByGuid(String gradeGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getMemberGradeInfoByGuid", gradeGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> addMemberGradePurchaseHistory(MemberGradePurchaseHistoryQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "addMemberGradePurchaseHistory", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<MemberGradeCardVO>> getGradeCardList(GradeCardQueryDTO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getGradeCardList", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> updateGradeCard(GradeCardQueryDTO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateGradeCard", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> addGradeCard(GradeCardQueryDTO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "addGradeCard", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }


                @Override
                public Result<Boolean> saveIntegralDeductDetail(OrderIntegralDeductDetailQO deductDetailVO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveIntegralDeductDetail", deductDetailVO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void releaseIntegralDeductDetail(ReleaseOrderDeductDetailQO releaseOrderDetailVO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "releaseIntegralDeductDetail", releaseOrderDetailVO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PurchaseOrderRecordVO exportPurchaseOrderRecord(PurchaseApplyPageQo qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "exportPurchaseOrderRecord", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void orderGenerateCallbackProcessing(List<OrderGenerateCallbackQO> qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "orderGenerateCallbackProcessing", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void accumulationDiscountRelease(AccumulationReleaseKeyQO accumulationReleaseKeyQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "accumulationDiscountRelease", accumulationReleaseKeyQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<MemberPriceApplyCommodityVO> getMemberPriceApplyCommodity(MemberPriceApplyCommodityQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getMemberPriceApplyCommodity", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<SettlementApplyOrderVO> calculateMemberDiscount(SettlementApplyOrderDTO settlementApplyOrderDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "calculateMemberDiscount", settlementApplyOrderDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<MemberNewGradeDTO> getNewCurrentGradeIconList(List<String> memberGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getNewCurrentGradeIconList", memberGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<StoreBaseInfo>> queryStoreInfo(String keyword) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryStoreInfo", keyword, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<ConsumptionRespVO> payOrder(RequestConfirmPayVO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "payOrder", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void payOrderRightsCallback(TerOrderCallbackQO terOrderCallbackQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "payOrderRightsCallback", terOrderCallbackQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<LabelSiftVO> listLabelSet(List<String> labelGuidList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listLabel", JacksonUtils.writeValueAsString(labelGuidList), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void refreshLabel(List<String> memberGuid, List<String> hsaLabelSettings, int assignLabel, Integer sourceType, Integer triggerType, String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "refreshLabel", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Page<QueryCardInfoPageVO>> getCardInfoPage(QueryCardInfoPageQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCardInfoPage", query, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<CardBaseInfoVO> getCardInfoByGuid(String cardGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCardInfoByGuid", cardGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult> listMemberInfo(MemberListQO memberListQo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listMemberInfo", memberListQo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult> listMemberLabel(MemberLabelListQO memberLabelListQo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listMemberLabel", memberLabelListQo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result saveOrUpdateAutomaticLabel(RequestOperationLabel requestOperationLabelQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveOrUpdateAutomaticLabel", requestOperationLabelQO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Integer> countLabelMember(List<String> labelGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "countLabelMember", labelGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<String> downloadExcelUrl() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "downloadExcelUrl", "null", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<MemberUploadExcelVO> memberUploadExcelUrl(String fileUrl, String cardGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "memberUploadExcelUrl", "fileUrl", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<SubjectPermissionDTO> getAllSubjectPermission() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAllSubjectPermission", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<OperSubjectPermissionTypeDTO> getAllSubjectTypePermission() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAllSubjectTypePermission", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result subsidyActivityPageList(RequestSubsidyActivityQO requestSubsidyActivityQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "subsidyActivityPageList", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<String> subsidyActivityPageListNew(RequestSubsidyActivityQO requestSubsidyActivityQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "subsidyActivityPageListNew", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result saveOrUpdate(MemberSubsidyActivityVO subsidyActivityVO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveOrUpdate", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result getDetailByGuid(String guid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getDetailByGuid", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result deleteByGuid(String guid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "deleteByGuid", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result updateSubsidyActivityStatus(String guid, Integer status) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateSubsidyActivityStatus", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result initSubsidyData() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "initSubsidyData", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result getSubsidyActivityByState(List<Integer> status) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getSubsidyActivityByState", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result getEnableSubsidyActivity() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getEnableSubsidyActivity", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void batchUpdateStatusValue(List<SubsidyActivityDTO> subsidyActivityDTOList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "batchUpdateStatusValue", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result batchUpdateStatus(List<SubsidyActivityDTO> subsidyActivityDTOList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "batchUpdateStatus", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result getSubsidyByGuid(List<String> guids) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getSubsidyByGuid", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result subsidyRecordDetailPageList(RequestSubsidyRecordDetailQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "subsidyRecordDetailPageList", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<SubsidyRecordDetailVO>> subsidyRecordDetailExport(RequestSubsidyRecordDetailQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "subsidyRecordDetailExport", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result subsidyReissueByRecord(SubsidyReissueByRecord request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "subsidyReissueByRecord", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result subsidyRecordStatistical(RequestSubsidyRecordDetailQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "subsidyRecordStatistical", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result getRecyclableSubsidyActivity() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getRecyclableSubsidyActivity", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result saveBatchSubsidyDetail(List<SubsidyActivityDetailRecordDTO> subsidyActivityDetailRecords) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveBatchSubsidyDetail", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result getSubsidyDetailByGuid(List<String> guids) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getSubsidyDetailByGuid", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result batchUpdateDetailStatus(List<SubsidyActivityDetailRecordDTO> subsidyActivityDetailRecords) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "batchUpdateDetailStatus", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result updateSubsidyRecycling(List<String> guids) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateSubsidyRecycling", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result batchUpdateSubsidyDetail(SubsidyActivityDetailRecordDTO subsidyActivityDetailRecordDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "batchUpdateSubsidyDetail", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result subsidyRecordPageList(RequestSubsidyRecordQO requestSubsidyActivityQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "subsidyRecordPageList", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result getDetail(String subsidyRecordGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getDetail", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result subsidyReissueByRecord(String recordGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "subsidyReissueByRecord", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result findAllSoonOverdueSubsidy(List<String> detailRecordGuids) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findAllSoonOverdueSubsidy", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result saveBatchSubsidyRecord(List<SubsidyActivityRecordDTO> subsidyActivityRecordDTOList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveBatchSubsidyRecord", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result batchUpdateRecordStatus(List<SubsidyActivityRecordDTO> subsidyActivityRecordDTOList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "batchUpdateRecordStatus", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<SubsidyActivityRecordDTO>> getSubsidyRecordByGuid(List<String> guids) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getSubsidyRecordByGuid", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Boolean> updateSubsidyRecord(SubsidyActivityRecordDTO subsidyActivityRecordDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateSubsidyRecord", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<ApplyTypeVO>> getBusinessType() {
                    throw new ServerException();
                }

                @Override
                public Result getOperationMemberDetail(String guid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getOperationMemberDetail", guid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<String> findMemberNameByGuid(String memberGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findMemberNameByGuid", memberGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> addRoleType(String phoneNum, String roleType) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "addRoleType", phoneNum + "-" + roleType, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> deleteRoleType(String phoneNum, String roleType) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "deleteRoleType", phoneNum + "-" + roleType, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<MemberCircleRspDTO> listPartnerMemberCircle(String memberGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listPartnerMemberCircle", memberGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberCircleRspDTO getMemberCircle(MemberCircleReqDTO circleReqDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listPartnerMemberCircle", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PartnerMemberBaseDTO> listNonGuids(List<String> friendGuidList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listNonFriend", JacksonUtils.writeValueAsString(friendGuidList),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PartnerMemberBaseDTO> searchPartnerMember(String searchContent, int index) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "searchPartnerMember", searchContent,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PartnerMemberBaseDTO> listByGuids(List<String> friendGuidList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "friendGuidList", JacksonUtils.writeValueAsString(friendGuidList),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<MemberGradeEquitiesPartnerVO> currentQuery() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "currentQuery", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void offlineActivityMemberRelation(OfflineActivityMemberRelationDTO relationDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "offlineActivityMemberRelation", JacksonUtils.writeValueAsString(relationDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<MemberInfoVO> offlineActivityMemberRelationList(OfflineActivityMemberRelationQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "offlineActivityMemberRelationList", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PageResult<MemberInfoVO> offlineActivityMemberRelationPage(OfflineActivityMemberRelationQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "offlineActivityMemberRelationPage", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<OfflineActivityMemberRelationDTO> offlineActivityMemberRelationListCount(OfflineActivityMemberRelationQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "offlineActivityMemberRelationListCount", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<String> offlineActivityQueryActivityGuids(OfflineActivityMemberRelationQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "offlineActivityQueryActivityGuids", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String offlineActivityDownloadExcelUrl() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "offlineActivityDownloadExcelUrl", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<OfflineActivityMemberUploadResultVO> offlineActivityUploadMember(String fileUrl, String activityGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "offlineActivityUploadMember", fileUrl + activityGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Response offlineActivityExportMember(OfflineActivityMemberRelationQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "offlineActivityExportMember", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<String> getOfflineActivityByMemberGuid(String memberGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getOfflineActivityByMemberGuid", memberGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberSystemPermissionVO getAccountPermission(CheckRolePermissionQO query) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAccountPermission", query,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PartnerReserveMemberInfoDTO> queryMemberByGuidList(List<String> memberGuidList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryMemberByGuidList", memberGuidList,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PartnerReserveMemberInfoDTO> queryMemberByPhoneList(List<String> phoneList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryMemberByPhoneList", phoneList,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }


                @Override
                public Boolean activityJoin(String memberGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "activityJoin", memberGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<MemberCircleRspDTO> listPartnerMemberCircleByMemberGuids(List<String> memberGuidList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listPartnerMemberCircleByMemberGuids", memberGuidList,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }


                @Override
                public void saveReserve(ReserveConsumptionDTO consumptionDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveReserve", JacksonUtils.writeValueAsString(consumptionDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> saveTodayCardAmount() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveRecordDateCardAmount", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();

                }


                @Override
                public Result<PageResult<CouponPackageGiveVO>> pagePackageDetail(CouponPackageGiveQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageDetail", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult<CouponPackageGiveExportVO>> pagePackageExportDetail(CouponPackageGiveQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageExportDetail", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Boolean> sendMemberCoupon(MemberSendCouponQO memberSendCouponQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "sendMemberCoupon", JacksonUtils.writeValueAsString(memberSendCouponQO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<CouponPackageGiveCountVO> countPackageGiveNum(CouponPackageGiveQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "countPackageGiveNum", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult<CouponGiveVO>> pageCouponDetail(CouponGiveQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageDetail", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult<CouponGiveExportVO>> pageCouponExportDetail(CouponGiveQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageExportDetail", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<CouponGiveCountVO> countCouponGiveNum(CouponGiveQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "countGiveNum", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<CouponUseCountVO> countCouponUseNum(CouponUseQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "countCouponUseNum", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult<CouponUseVO>> pageCouponUseDetail(CouponUseQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageCouponUseDetail", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, CouponPackageCountVO> countPackageActivity(PackageListCountQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "countPackageActivity", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, SellableCouponPackageCountVO> countSellableCouponPackageActivity(SellableCouponPackageListCountQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "countSellableCouponPackageActivity", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> markUse(CouponMarkUseQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "markUse", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> invalid(CouponMarkInvalidQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "invalid", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemNum> countCoupon(CouponListCountQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "countCoupon", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, CouponListCountVO> countListCoupon(CouponListCountQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "countListCoupon", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<CalculateOrderIntegralDeductVO> calculateOrderIntegralDeduct(CalculateIntegralDeductQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "calculateOrderIntegralDeduct", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<CalculateMemberPriceCommodityVO>> calculateMemberPriceCommodity(CalculateMemberPriceQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "calculateMemberPriceCommodity", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<SettlementApplyOrderVO> listDiscount(SettlementApplyOrderDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listDiscount", JacksonUtils.writeValueAsString(dto),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<SettlementApplyOrderVO> calculateDiscount(SettlementApplyOrderCalculateDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "calculateDiscount", JacksonUtils.writeValueAsString(dto),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void saveOpenIdAndUnionId(TokenRequestBO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveOpenIdAndUnionId", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void wechatMessageSendBatch(MessagesSendbatchQO messagesSendBatchQuery) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "wechatMessageSendBatch", JacksonUtils.writeValueAsString(messagesSendBatchQuery),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void wechatMessageSend(MessagesSendQO messagesSendQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "wechatMessageSend", JacksonUtils.writeValueAsString(messagesSendQO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void shortMessageSendBatch(MessagesSendbatchQO messagesSendBatchQuery) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "shortMessageSendBatch", JacksonUtils.writeValueAsString(messagesSendBatchQuery),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PurchaseApplyCommodityVo> listMemberPurchaseCommodity(PurchaseMemberCommodityQo qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listMemberPurchaseCommodity", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PurchaseReserveCommodityVO> listMemberPurchaseCommodity(PurchaseReserveCommodityQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listMemberPurchaseCommodity", JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void autoInitSubjectData(InitSubjectDataDTO initSubjectData) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "autoInitSubjectData", JacksonUtils.writeValueAsString(initSubjectData),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberLabelGradeVO getMemberLabelAndGradeInfo(String guid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "guid", JacksonUtils.writeValueAsString(guid),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public OperationMemberCardInfoVO getOperationMemberCardInfo(String guid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(guid),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, Integer> listByCodeRecord(CouponDtlQO couponDtlQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(couponDtlQO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<MemberBasicInfoVO> getMemberInfo(MemberQueryDTO queryDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(queryDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<MemberBasicInfoVO> getMemberByOpenid(MemberQueryDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(dto),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<BaseLoginMemberCardVO> loginMemberCard(TerLoginMemberCardQO terLoginMemberCardQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(terLoginMemberCardQO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<MemberBasicInfoVO>> batchGetMemberInfo(MemberQueryDTO queryDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(queryDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult<MemberInfoVO>> getOperationMemberInfoListByPhone(MemberListQO qeury) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(qeury),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<MemberAccountInfoVO> memberAccountInfo(MemberQueryDTO queryDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(queryDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<MemberBasicInfoVO> addMemberInfo(MemberAddDTO addDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(addDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> updateMemberInfo(MemberUpdateDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(dto),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<PayMemberCardVO>> listPayMemberCard(MemberCardQueryDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(dto),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<MiniProgramCardDTO>> listMyMemberCard(ListMiniProgramCardQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Integer> queryOpenCardRuleStatus(String cardGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, cardGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<AppletOpenCardVO> openCard(MemberCardOpenDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(dto),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> openCardPayCheck(MemberCardOpenCardPayCheckDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(dto),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<String> openCardPay(MemberCardOpenCardPayDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(dto),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<AppletMemberCardDetail> getMemberCardInfo(MemberCardInfoQueryDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(dto),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<BalanceDetailTotalVO> getMemberCardBalanceTotal(AppletBalanceDetailQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult> getMemberCardBalanceRecord(AppletBalanceDetailQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(qo),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Boolean> memberCardPwdCheck(MemberCardPwdCheckDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(dto),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Boolean> memberCardPayRecordRequest(List<MemberCardPayDTO> dtoList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(dtoList),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Boolean> memberCardPay(List<MemberCardPayDTO> dtoList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(dtoList),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<ConsumptionRespVO> memberCardPay(RequestMemberCardPayVO payDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(payDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<ConsumptionRespVO> cashPayOrder(RequestConfirmPayVO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<ConsumptionRespVO> memberOrderRefund(RequestMemberOrderRefundVO payDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(payDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Boolean> memberCardPayCallback(List<MemberCardPayCallbackDTO> dtoList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(dtoList),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Boolean> memberCardRefund(MemberCardRefundDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, JacksonUtils.writeValueAsString(dto),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<DataItemSetVO>> listInfo(String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, operSubjectGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Boolean checkUserDataItem(CheckDataItemDTO dataItemDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, dataItemDTO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<GradeLevelVO> getMemberPaidGradeLevel(String memberGuid, String roleType) {
                    log.error(StringConstant.HYSTRIX_PATTERN, memberGuid, roleType,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<GradeLevelVO> getMemberFreeGradeLevel(String memberGuid, String roleType) {
                    log.error(StringConstant.HYSTRIX_PATTERN, memberGuid, roleType,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Boolean> updatePwd(MemberCardPwdUpdateDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, dto,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Boolean> updateDefault(MemberCardDefaultUpdateDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, dto,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PhysicalCardResultVO> bindPhysicalCard(BindingPhysicalCardQO bindingPhysicalCardQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, bindingPhysicalCardQO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<CardWeChatRechargeDataVO> rechargePage(String memberInfoCardGuid, String storeGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, memberInfoCardGuid, storeGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<RechargeThresholdVO> calculatePreMoney(MemberCalculatePreMoneyQO preMoneyQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, preMoneyQO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<RechargeRespVO> recharge(TerMemberCardRechargeQO terMemberCardRechargeQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, terMemberCardRechargeQO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<RechargeRespVO> cashRecharge(TerMemberCardRechargeQO terMemberCardRechargeQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, terMemberCardRechargeQO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<AbleECardVO>> listAllAbleECardByChannel() {
                    log.error(StringConstant.HYSTRIX_PATTERN, Strings.EMPTY,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<MemberCardQrCodeVO> getQrCode(MemberCardInfoQueryDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, dto,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<GradePreviewVO> getEquitiesPreviewInfo(String memberGradeGuid,
                                                                     String memberInfoGuid, String sourceType, Integer effective) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getEquitiesPreviewInfo", memberGradeGuid, memberInfoGuid, sourceType, effective,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<EquitiesInfoDetailVO> getEquitiesInfoByGuid(String guid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getEquitiesInfoByGuid", guid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<MemberBasicInfoVO>> listMembersByGradeType(Integer gradeType) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listMembersByGradeType", gradeType, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<HsaMemberGradeInfoVO> queryMemberGradeInfo(Integer gradeType, Integer vipGrade) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryMemberGradeInfo", gradeType, vipGrade, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Integer> getUsableIntegral(UsableIntegralQO usableIntegralQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryMemberGradeInfo", usableIntegralQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }


                @Override
                public Result<Boolean> updateMemberIntegral(MallOrderIntegralQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateMemberIntegral", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<MemberCouponWxVO>> pageMemberCouponByTime(MemberCouponListQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageMemberCouponByTime", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult<MemberCouponWxVO>> pageableMemberCouponByTime(MemberCouponQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "pageableMemberCouponByTime", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<MemberCouponWxDetailVO> detail(String memberCouponGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "detail", memberCouponGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<MemberCouponWxDetailVO>> batchDetails(List<String> memberCouponGuids) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "batchDetails", memberCouponGuids, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<CouponQrCodeVO> getQrCode(String memberCouponGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getQrCode", memberCouponGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<MemberCouponWxCountVO> countMemberNum(MemberCouponNumQO qo) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "countMemberNum", qo, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result getMemberCouponStoreList(MemberCouponStoreListDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getMemberCouponStoreList", dto, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Integer> countMemberActivityReceive(String activityGuid, String memberGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "countMemberActivityReceive", activityGuid + "-" + memberGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<SelfReceiveCouponPackageVO> selfReceiveCouponPackage(SelfReceiveCouponPackageQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "selfReceiveCouponPackage", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<MemberPortrayalDetailsDTO> queryMemberPortrayal(MemberQueryDTO memberQueryDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryMemberPortrayal", memberQueryDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<PageResult<ConsumptionDetailVO>> getConsumptionDetail(ConsumptionDetailQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getConsumptionDetail", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<CouponGiveVO> getCouponGiveVOByCouponCode(String couponCode) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getCouponGiveVOByCouponCode", couponCode, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<MemberOrderDiscountDTO>> listOrderDiscount(SettlementOrderLockDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listOrderDiscount", dto, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<String> buildFrontendRoute(FrontendRouteBuildDTO dto) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "buildFrontendRoute", JacksonUtils.writeValueAsString(dto),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<String>> queryIdentificationNames() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryIdentificationNames", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<SubjectVO>> querySubjects(String identificationName) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "querySubjects", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<AccountSettingRespVO> queryAccountSetting() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryAccountSetting", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<HsaCardBalanceRuleVO> getBalanceRule() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getBalanceRule", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<MemberWeekConsumeFrequencyStatisticsVO> queryMemberWeekConsumeFrequency() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryMemberWeekConsumeFrequency", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<MemberSexStatisticsVO> queryMemberSexDistribution() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryMemberSexDistribution", "",
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<Long>> queryMemberAgeDistribution(MemberAgeDistributionQO memberAgeDistributionQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryMemberAgeDistribution", JacksonUtils.writeValueAsString(memberAgeDistributionQO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<MemberIncreaseStatisticsVO>> queryMemberIncrease(MemberIncreaseQO memberIncreaseQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryMemberIncrease", JacksonUtils.writeValueAsString(memberIncreaseQO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
            return memberBaseFeign;
        }
    }
}
