package com.holderzone.member.common.module.settlement.apply.vo.detail;

import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyDiscountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * 会员积分
 * <p>
 * 规定了必须返回的字段
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SettlementApplyDiscountDetailOfIntegralVO extends SettlementApplyDiscountDetailVO {


    private static final long serialVersionUID = -3135496341752617040L;
    /**
     * 使用积分
     */
    @ApiModelProperty("使用积分")
    private Integer integralNum;


    /**
     * 周期剩余优惠
     */
    @ApiModelProperty("周期剩余优惠")
    private BigDecimal cycleResidueAmount;

    /**
     * 周期类型  -1:自定义 0：日 1：周 2：月 3：年 4 累计
     *
     * @see com.holderzone.member.common.enums.growth.DataUnitEnum
     */
    private Integer equitiesTimeLimitedType;

    /**
     * 周期优惠限制  0：不限制 1：限制
     *
     * @see com.holderzone.member.common.enums.equities.EquitiesLimitedTypeEnum
     */
    @ApiModelProperty("周期优惠限制 0：不限制 1：限制")
    private Integer periodDiscountLimited;


    /**
     * 使用积分
     */
    @ApiModelProperty("使用积分")
    private Integer usedIntegralNum;

    /**
     * 每多少积分（配置）
     */
    private Integer disIntegralNum;

    /**
     * 抵扣金额（配置）
     */
    private BigDecimal disForNowMoney;
}
