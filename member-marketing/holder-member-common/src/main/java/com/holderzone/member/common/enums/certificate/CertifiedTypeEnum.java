package com.holderzone.member.common.enums.certificate;

import org.springframework.util.StringUtils;

/**
 * 认证类型
 * <AUTHOR>
 */

public enum CertifiedTypeEnum {

    /**
     * 行程票
     */
    TRIP_TICKET(1,"行程票"),

    /**
     * 自定义证件
     */
    CUSTOM_CERTIFICATE(2,"自定义证件"),

    /**
     * 支付宝学生
     */
    ALIPAY_STUDENT(3,"支付宝学生"),
    ;

    private final int code;

    private final String des;

    CertifiedTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode(){
        return this.code;
    }

    public String getDes() {
        return this.des;
    }

    public int getCodeByDes(String des) {
        if (StringUtils.isEmpty(des)) {
            return -1;
        }
        CertifiedTypeEnum[] values = CertifiedTypeEnum.values();
        for (CertifiedTypeEnum value : values) {
            if (value.des.equals(des)) {
                return value.getCode();
            }
        }
        return -1;
    }

    public String getDesByCode(int code) {
        CertifiedTypeEnum[] values = CertifiedTypeEnum.values();
        for (CertifiedTypeEnum value : values) {
            if (value.getCode() == code) {
                return value.getDes();
            }
        }
        return "";
    }
}
