package com.holderzone.member.common.module.base.store.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 * 同步门店
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@Accessors(chain = true)
@ApiModel(value="HsaSyncStore对象", description="同步门店")
public class SyncStoreDTO implements Serializable {

    /**
     * 兼容老接口，返回门店id
     */
    @Min(message = "门店id不能为空",value = 0)
    private String id;

    @NotBlank(message = "运营主体guid不能为空")
    @ApiModelProperty(value = "运营主体guid")
    private String operSubjectGuid;

    @ApiModelProperty(value = "删除标志：0正常(默认) 1 删除")
    private Long deleted = 0L;

    @ApiModelProperty(value = "品牌id")
    private String brandId;

    @ApiModelProperty(value = "品牌")
    private String brandName;

    @ApiModelProperty(value = "门店")
    private String storeGuid;

    @NotBlank(message = "门店名称不能为空")
    @ApiModelProperty(value = "门店")
    private String storeName;

    @NotBlank(message = "门店编号不能为空")
    @ApiModelProperty(value = "门店编号")
    private String storeNumber;


    /**
     * @see com.holderzone.member.common.enums.SystemEnum
     */
    @ApiModelProperty(value = "来源系统")
    private String system;

    /**
     * 门店状态
     * @see com.holderzone.member.common.enums.sale.SaleStatusEnum#des
     */
    private String status;

    @ApiModelProperty(value = "联系人")
    private String contact;

    @ApiModelProperty(value = "联系电话")
    private String phoneNumber;

    @ApiModelProperty(value = "营业地址")
    private String address;

    @ApiModelProperty(value = "logo")
    private String storeLogo;

    @ApiModelProperty(value = "经纬度")
    private String addressPoint;

    @ApiModelProperty(value = "营业时间")
    private String time;

    @ApiModelProperty(value = "营业时间：周一")
    private Boolean isMonday;

    @ApiModelProperty(value = "营业时间：周二")
    private Boolean isTuesday;

    @ApiModelProperty(value = "营业时间：周三一")
    private Boolean isWednesday;

    @ApiModelProperty(value = "营业时间：周四")
    private Boolean isThursday;

    @ApiModelProperty(value = "营业时间：周五")
    private Boolean isFriday;

    @ApiModelProperty(value = "营业时间：周六一")
    private Boolean isSaturday;

    @ApiModelProperty(value = "营业时间：周日")
    private Boolean isSunday;

    private String storeTeamInfoId;
}
