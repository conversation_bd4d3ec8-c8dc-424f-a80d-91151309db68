package com.holderzone.member.common.config.mybatis;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.extension.injector.AbstractLogicMethod;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * @date 2020/05/09 14:46
 * @description RemoveByGuid-sql拼接
 */
public class LogicRemoveByGuid extends AbstractLogicMethod {
    private static final String LOGIC_REMOVE_MAPPER_METHOD = "removeByGuid";
    private static final String LOGIC_REMOVE_MAPPER_SQL = "DELETE FROM %s WHERE %s=#{%s}";
    private static final String LOGIC_REMOVE_GUID_NAME = "guid";

    public LogicRemoveByGuid() {
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        String logicRemovesql = String.format(LOGIC_REMOVE_MAPPER_SQL, tableInfo.getTableName(), LOGIC_REMOVE_GUID_NAME, LOGIC_REMOVE_GUID_NAME);
        SqlSource sqlSource = this.languageDriver.createSqlSource(this.configuration, logicRemovesql, modelClass);
        return this.addUpdateMappedStatement(mapperClass, modelClass, LOGIC_REMOVE_MAPPER_METHOD, sqlSource);
    }
}
