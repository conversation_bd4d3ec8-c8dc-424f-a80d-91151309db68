package com.holderzone.member.common.enums.commodity;

/**
 * <AUTHOR>
 * @date 2023/3/9 下午4:26
 * @description 商品上架状态
 */
public enum StoreStateEnum {

    SELL_OUT(1,"告罄"),

    DOWN(2,"未上架"),

    UP(3,"已上架"),

    EXPIRED(4,"已失效"),
    ;

    private final int code;

    private final String des;

    StoreStateEnum(int code,String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return this.code;
    }

    public String getDes() {
        return this.des;
    }
}
