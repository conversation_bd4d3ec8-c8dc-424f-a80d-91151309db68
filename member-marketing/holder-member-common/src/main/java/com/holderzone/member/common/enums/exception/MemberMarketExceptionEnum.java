package com.holderzone.member.common.enums.exception;

import com.holderzone.member.common.enums.ResponseBase;

/**
 * <AUTHOR>
 * @description 营销中心异常枚举
 * @date 2023-8-29
 */
public enum MemberMarketExceptionEnum implements ResponseBase {

    // 数据操作错误定义

    MEMBER_COUPON_NULL(500001, "优惠券不存在"),

    MEMBER_COUPON_NOT_USE(500002, "优惠券不可使用"),
    ;

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误描述
     */
    private final String des;

    MemberMarketExceptionEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDes() {
        return des;
    }
}
