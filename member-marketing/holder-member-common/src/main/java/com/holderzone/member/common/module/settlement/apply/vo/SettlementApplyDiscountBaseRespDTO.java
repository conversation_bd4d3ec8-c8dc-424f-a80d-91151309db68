package com.holderzone.member.common.module.settlement.apply.vo;

import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 优惠应用结果
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementApplyDiscountBaseRespDTO implements Serializable {


    private static final long serialVersionUID = 1671089069916426611L;
    /**
     * 优惠类型
     *
     * @see SettlementDiscountOptionEnum
     */
    private Integer discountOption;

    /**
     * 优惠guid，必须
     */
    @ApiModelProperty(value = "优惠guid")
    private String discountGuid;

    /**
     * 优惠名称
     */
    @ApiModelProperty(value = "优惠名称")
    private String discountName;
}
