package com.holderzone.member.common.enums.exception;

import com.holderzone.member.common.enums.ResponseBase;

/**
 * <AUTHOR>
 * @description 异常枚举类
 * @date 2021/8/10
 */
public enum ExceptionEnum implements ResponseBase {

    // 数据操作错误定义
    SUCCESS(200, "成功!"),
    BODY_NOT_MATCH(400,"请求的数据格式不符!"),
    SIGNATURE_NOT_MATCH(401,"请求的数字签名不匹配!"),
    NOT_FOUND(404, "未找到该资源!"),
    INTERNAL_SERVER_ERROR(500, "服务器内部错误!"),
    FEIGN_URL_ERROR(5001,"feign地址错误！"),
    SERVER_BUSY(503,"服务器正忙，请稍后再试!"),
    OPERATION_FAILURE(-1,"操作失败"),
    TRANSACTION_FAILURE(-2,"事务异常"),
    ERROR_PARAM(120006,"参数错误"),
    ;

    /** 错误码 */
    private final int code;

    /** 错误描述 */
    private final String des;

    ExceptionEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDes() {
        return des;
    }
}
