package com.holderzone.member.common.qrcode;

import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 二维码生成
 *
 * <AUTHOR>
 * @date 2023/8/28
 **/
@Component
@Slf4j
public class QrCodeSupport {

    @Resource
    private QrcodeServer qrcodeServer;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 二维码刷新时间，60s，防止提前过期+1s
     */
    private static final int EXPIRE_TIME = 61;

    /**
     * 获取二维码字符串
     *
     * @param value  缓存值
     * @param prefix key前缀
     * @return
     */
    public String getQrStr(String value, String prefix) {
        String key = prefix + guidGeneratorUtil.getEightCode(randomUUID());
        QRCode qrCode = qrcodeServer.generate(key, 300, 300);
        redisTemplate.opsForValue().set(key, value, EXPIRE_TIME, TimeUnit.SECONDS);
        String qrStr = qrcodeServer.display(qrCode);
        log.info("二维码key:{},value:{},qrStr:{}", key, value, qrStr);
        return qrStr;
    }

    /**
     * 获取条形码字符串
     *
     * @param value  缓存值
     * @param prefix key前缀
     * @return
     */
    public String getBarcode(String value, String prefix) {
        //卡号加上时间戳 格式 qr_pre加随机数
        String key = prefix + guidGeneratorUtil.getEightCode(randomUUID());
        String barcodeStr = qrcodeServer.generateBarcode(key, 500, 200);
        redisTemplate.opsForValue().set(key, value, EXPIRE_TIME, TimeUnit.SECONDS);
        log.info("条形码key:{},value:{},barcodeStr:{}", key, value, barcodeStr);
        return barcodeStr;
    }

    public String randomUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

}
