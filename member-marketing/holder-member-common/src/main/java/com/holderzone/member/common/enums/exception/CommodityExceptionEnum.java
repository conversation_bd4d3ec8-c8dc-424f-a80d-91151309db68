package com.holderzone.member.common.enums.exception;

import com.holderzone.member.common.enums.ResponseBase;

/**
 * <AUTHOR>
 * @date 2023/3/8 上午10:24
 * @description 商品异常情况枚举
 */
public enum CommodityExceptionEnum implements ResponseBase {

    // 购物车异常枚举
    SET_UNINITIALIZED(1001,"通用设置未进行初始化"),
    CHANGE_TYPE_ERROR(1002,"变动类型错误"),
    COMMODITY_DOWN(1003,"商品已下架，请选购其它商品"),

    CART_COMMODITY_DOWN(1004,"购物车此商品不存在"),

    CART_COMMODITY_NULL(1005,"购物车无商品"),

    CART_STORE_NULL(1006,"购物车无门店"),

    CART_STORE_MULTIPLE(1007,"多个门店商品请分开下单"),

    CART_COMMODITY_ALL_INVALID(1008,"商品已失效，请选购其它商品"),

    CART_COMMODITY_COUNT_LIMIT(1009,"最多可加入100个商品"),
    ;

    /** 错误码 */
    private final int code;

    /** 错误描述 */
    private final String des;

    CommodityExceptionEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDes() {
        return des;
    }
}
