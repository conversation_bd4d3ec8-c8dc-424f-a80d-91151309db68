package com.holderzone.member.common.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.s2b2c.MemberConsumptionDistributeDTO;
import com.holderzone.member.common.dto.s2b2c.ShopQueryPageDTO;
import com.holderzone.member.common.vo.s2b2c.PlatformProductVO;
import com.holderzone.member.common.vo.s2b2c.ShopListVO;
import com.holderzone.member.common.vo.s2b2c.params.PlatformProductParam;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 私域商城feign接口
 * <AUTHOR>
 * @version 1.0, 2025/5/20
 */
@Component
@FeignClient(name = FilterConstant.S2B2C_MALL, fallbackFactory = S2b2cMallFeign.ServiceFallBack.class,url = "${feign.s2b2c}")
public interface S2b2cMallFeign {


    /**
     * 获取店铺商品列表
     * @param platformProductParam 查询参数
     * @return 商品列表
     */
    @PostMapping ("/gruul-mall-goods/api/product/get/platform/all")
    Result<Page<PlatformProductVO>> getPlatformAllProducts(@RequestBody PlatformProductParam platformProductParam);


    /**
     * 获取私域商城店铺列表
     * @param queryPageDTO 查询参数
     * @return 店铺列表
     */
    @PostMapping ("/gruul-mall-shop/api/shop/platform/pageList")
    Result<Page<ShopListVO>> platformShopPageList(@RequestBody ShopQueryPageDTO queryPageDTO);

    /**
     * 订单完成推送订单至私域商城
     * @param distributeDTO 订单数据
     * @return 推送结果
     */
    @PostMapping("/gruul-mall-order/api/order/distribute/complete")
    Result<Void> orderCompleteDistribute(@RequestBody MemberConsumptionDistributeDTO distributeDTO);


    /**
     * 订单退款推送订单至私域商城
     * @param distributeDTO 订单数据
     * @return 推送结果
     */
    @PostMapping("/gruul-mall-order/api/order/distribute/refund")
    Result<Void> orderRefundDistribute(@RequestBody MemberConsumptionDistributeDTO distributeDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<S2b2cMallFeign> {
        @Override
        public S2b2cMallFeign create(Throwable throwable) {
            return new S2b2cMallFeign() {
                @Override
                public Result<Page<PlatformProductVO>> getPlatformAllProducts (PlatformProductParam platformProductParam) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getPlatformAllProducts", platformProductParam, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Page<ShopListVO>> platformShopPageList (@RequestBody ShopQueryPageDTO queryPageDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "platformShopPageList", queryPageDTO,ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> orderCompleteDistribute (@RequestBody MemberConsumptionDistributeDTO distributeDTO) {
                    log.error("推送订单完成数据至私域商城失败: 会员guid:{},订单号{},完整参数{}",
                            distributeDTO.getMemberInfoGuid(),distributeDTO.getOrderNumber(),JacksonUtils.writeValueAsString(distributeDTO));
                    log.error(StringConstant.HYSTRIX_PATTERN, "orderCompleteDistribute", distributeDTO,ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> orderRefundDistribute (@RequestBody MemberConsumptionDistributeDTO distributeDTO) {
                    log.error("推送订单退款数据至私域商城失败: 会员guid:{},订单号{},完整参数{}",
                            distributeDTO.getMemberInfoGuid(),distributeDTO.getOrderNumber(),JacksonUtils.writeValueAsString(distributeDTO));
                    log.error(StringConstant.HYSTRIX_PATTERN, "orderRefundDistribute", distributeDTO,ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
