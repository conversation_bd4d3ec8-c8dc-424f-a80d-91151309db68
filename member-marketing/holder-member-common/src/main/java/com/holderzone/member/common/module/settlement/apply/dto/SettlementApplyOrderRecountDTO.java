package com.holderzone.member.common.module.settlement.apply.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;


/**
 * 优惠重新计算
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementApplyOrderRecountDTO {


    private static final long serialVersionUID = 3380794548408311914L;
    /**
     * 订单入参
     */
    @ApiModelProperty("订单相关入参")
    @NotNull(message = "订单入参必填！")
    private SettlementApplyOrderInfoDTO orderInfo;

}
