package com.holderzone.member.common.enums.certificate;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-04-13 16:29
 */
public enum ApplyCertifiedResultEnum {

    SUCCEED(0, "成功"),

    TRIP_TICKET_ALREADY_EXCHANGE(1, "当天行程票已兑换过礼品，请勿重复兑换"),

    ALREADY_JOIN_THE_ACTIVITY(2, "您已参与过此活动"),

    GAME_OVER(3, "很抱歉，你来晚了哦，活动已结束"),

    CERTIFICATION_ERROR(4, "证件认证失败，请重新提交"),

    CERTIFICATE_ALREADY_EXCHANGE(5, "证件已兑换过礼品，请勿重复兑换"),

    APPLY_INFO_NOT_ACCORD_CONDITION(6, "申请信息不符合活动条件，请重新确认"),

    QUERY_PHONE_UNREGISTERED_ERROR(7, "当前会员未注册，请通过【微信小程序】注册会员再参与活动");

    private int code;

    private String des;

    ApplyCertifiedResultEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public String getDes() {
        return this.des;
    }

    public int getCode() {
        return this.code;
    }


}
