package com.holderzone.member.common.external.impl;

import cn.hutool.core.collection.CollUtil;
import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Maps;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.dto.PageModel;
import com.holderzone.member.common.dto.holder.RoleAndPostIdDTO;
import com.holderzone.member.common.dto.ipaas.AddAndUpdateIPaasDTO;
import com.holderzone.member.common.dto.mall.TeamStoreInfoModel;
import com.holderzone.member.common.dto.mall.TeamStoreInfoPageParamModel;
import com.holderzone.member.common.dto.member.FunctionModelDTO;
import com.holderzone.member.common.dto.member.PermissionModelDTO;
import com.holderzone.member.common.dto.permission.MemberSystemPermissionDTO;
import com.holderzone.member.common.dto.permission.OperationPermissionQO;
import com.holderzone.member.common.dto.permission.RolePermissionMapModel;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.dto.user.OperSubjectInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SaasIdentificationTransferEnum;
import com.holderzone.member.common.enums.SystemPermissionEnum;
import com.holderzone.member.common.enums.member.ConsumptionOrderTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.enums.order.OrderSourceEnum;
import com.holderzone.member.common.enums.order.OrderTypeEnum;
import com.holderzone.member.common.exception.MemberMarketingException;
import com.holderzone.member.common.external.ExternalBaseService;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.SaasStoreFeign;
import com.holderzone.member.common.qo.permission.OperSubjectUserPermissionQO;
import com.holderzone.member.common.util.ServletUtils;
import com.holderzone.member.common.vo.base.BusinessDataModel;
import com.holderzone.member.common.vo.base.OperSubjectInfoVO;
import com.holderzone.member.common.vo.equities.ApplyTypeVO;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.feign.SassMenuVo;
import com.holderzone.member.common.vo.ipass.StoreListVO;
import com.holderzone.member.common.vo.ipass.SubjectListVO;
import com.holderzone.member.common.vo.ipass.SubjectVO;
import com.holderzone.member.common.vo.member.MemberSystemPermissionVO;
import com.holderzone.member.common.vo.permission.HsaOperSubjectPermissionVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description holder
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SaasBaseServiceImpl implements ExternalBaseService {

    private final SaasStoreFeign saasStoreFeign;

    @Value("${feign.store}")
    private String storeHost;

    @Value("${feign.member-center}")
    private String memberCenter;

    @Value("${feign.member-mall-tool}")
    private String memberMallTool;

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Override
    public HeaderUserInfo queryUserInformation(String enterpriseGuid) {
        final FeignModel<HeaderUserInfo> feignModel = saasStoreFeign.queryUserInformation();
        if (feignModel.getCode() != NumberConstant.NUMBER_0) {
            //认证失败等
            throw new MemberMarketingException(feignModel.getCode(), feignModel.getMessage());
        }
        final HeaderUserInfo headerUserInfo = Optional.ofNullable(feignModel.getData()).orElse(new HeaderUserInfo());
        try {
            final HttpServletRequest request = ServletUtils.getRequest();
            if (!Objects.isNull(request)) {
                //从老门店传过来的参数，后续一直带着
                headerUserInfo.setLoginType(request.getHeader(FilterConstant.LOGIN_TYPE));
                headerUserInfo.setTerminalCode(request.getHeader(FilterConstant.TERMINAL_CODE));
                headerUserInfo.setMenuGuid(request.getHeader(FilterConstant.MENU_GUID));
                //企业
                if (!StringUtils.isEmpty(request.getHeader(FilterConstant.ENTERPRISE_GUID))) {
                    headerUserInfo.setEnterpriseGuid(request.getHeader(FilterConstant.ENTERPRISE_GUID));
                }
            }
        } catch (Exception e) {
            log.warn("ServletUtils getRequest error", e);
        }
        return headerUserInfo;
    }

    @Override
    public HeaderUserInfo queryUserInformation(String enterpriseGuid, String token) {
        return queryUserInformation(enterpriseGuid);
    }

    @Override
    public OperSubjectInfoVO getOperatingSubjectInfo() {
        OperSubjectInfoVO subjectInfoVO = new OperSubjectInfoVO();
        List<OperSubjectInfo> subjectInfoList = queryOperatingSubject();
        HeaderUserInfo userInfo = queryUserInformation(null);
        subjectInfoVO.setOperSubjectInfos(subjectInfoList);
        BeanUtils.copyProperties(userInfo, subjectInfoVO);
        return subjectInfoVO;
    }

    @Override
    public List<HsaOperSubjectPermissionVO> queryOperatingSubjectByEnterpriseGuid(String enterpriseGuid) {
        log.info("老门店queryOperatingSubjectByEnterpriseGuid:{}", enterpriseGuid);
        return Collections.emptyList();
    }

    @Override
    public Boolean validateUser(String account, String password) {
        log.info("老门店validateUser:{},{}", account, password);
        return true;
    }

    @Override
    public BusinessDataModel queryBusinessData(String operSubjectGuid) {
        log.info("老门店queryBusinessData:{}", operSubjectGuid);
        return new BusinessDataModel();
    }

    @Override
    public BusinessDataModel queryBusinessData(String operSubjectGuid, String token) {
        log.info("老门店queryBusinessData:{},{}", operSubjectGuid, token);
        return new BusinessDataModel();
    }

    @Override
    public List<OperSubjectInfo> queryOperatingSubject(String enterpriseGuid) {
        log.info("老门店queryOperatingSubject:{}", enterpriseGuid);
        return saasStoreFeign.queryOperatingSubject().getData();
    }

    @Override
    public List<OperSubjectInfo> queryOperatingSubject() {
        return saasStoreFeign.queryOperatingSubject().getData();
    }

    @Override
    public String findBusinessDataAddress(String enterpriseGuid) {
        log.info("老门店findBusinessDataAddress:{}", enterpriseGuid);
        return null;
    }

    @Override
    public List<String> listAllOperationSubjectId() {
        log.info("老门店listAllOperationSubjectId");
        return new ArrayList<>();
    }

    @Override
    public Boolean getHasPermissionName(OperationPermissionQO request, String token) {
        log.info("老门店getHasPermissionName:{}", request);
        return true;
    }

    @Override
    public List<MemberSystemPermissionDTO> listSystemPermission(OperationPermissionQO operationPermissionRequest, String token) {
        log.info("老门店listSystemPermission:{}", operationPermissionRequest);
        //查询门店配置跳转到营销中心/小程序配置的权限
        String pageUrl = memberCenter;
        if (operationPermissionRequest.getIdentifications().contains(SystemPermissionEnum.MEMBER_TOOL.getDes())) {
            // 如果是小程序配置的权限
            pageUrl = memberMallTool;
        }
        final FeignModel<List<SassMenuVo>> sourceByMenu = saasStoreFeign.getPageUrlByMenu(Maps.newHashMap("data", pageUrl));
        if (!CollectionUtils.isEmpty(sourceByMenu.getData())) {
            List<SassMenuVo> sassMenuList = sourceByMenu.getData();
            return permissionConverter(sassMenuList);
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> listIdentificationNames() {
        FeignModel<List<SassMenuVo>> sourceByMenu = saasStoreFeign.getPageUrlByMenu(Maps.newHashMap("data", Strings.EMPTY));
        List<SassMenuVo> sassMenuVos = sourceByMenu.getData();
        log.info("sourceByMenu:{}", sassMenuVos);
        return transferIdentificationNames(sassMenuVos);
    }

    @Override
    public List<SubjectVO> listIdentificationSubjects(String identificationName) {
        FeignModel<List<OperSubjectInfo>> feignModel = saasStoreFeign.queryOperatingSubject();
        if (feignModel == null) {
            return Lists.newArrayList();
        }
        List<OperSubjectInfo> operSubjectInfoList = feignModel.getData();
        if (CollectionUtils.isEmpty(operSubjectInfoList)) {
            return Lists.newArrayList();
        }
        return operSubjectInfoList.stream().map(e -> {
            SubjectVO subjectVO = new SubjectVO();
            subjectVO.setId(e.getOperSubjectGuid());
            subjectVO.setName(e.getMultiMemberName());
            return subjectVO;
        }).collect(Collectors.toList());
    }


    private List<String> transferIdentificationNames(List<SassMenuVo> sassMenuVos) {
        if (CollectionUtils.isEmpty(sassMenuVos)) {
            return Lists.newArrayList();
        }
        List<String> allIdentificationNames = sassMenuVos.stream()
                .map(SassMenuVo::getSourceCode)
                .distinct()
                .collect(Collectors.toList());
        // 兼容老权限 ，转换老权限为新权限
        List<String> originals = sassMenuVos.stream()
                .map(e -> e.getModuleName() + "." + e.getSourceName())
                .distinct()
                .collect(Collectors.toList());
        List<String> lasts = SaasIdentificationTransferEnum.getLastByOriginal(originals);
        if (!CollectionUtils.isEmpty(lasts)) {
            allIdentificationNames.addAll(lasts);
            allIdentificationNames = allIdentificationNames.stream().distinct().collect(Collectors.toList());
        }
        return allIdentificationNames;
    }

    @Override
    public List<String> listUserRoleIds(OperSubjectUserPermissionQO userPermissionQO, String token) {
        log.info("老门店listUserRoleIds:{}", userPermissionQO);
        return Lists.newArrayList();
    }

    @Override
    public RolePermissionMapModel findUserRoleMapPermission(OperationPermissionQO operationPermissionRequest, String token) {
        log.info("老门店findUserRoleMapPermission:{}", operationPermissionRequest);
        return new RolePermissionMapModel();
    }

    /**
     * 获取系统权限列表
     *
     * @param operationPermissionQO 请参参数
     * @return 操作结果
     */
    @Override
    public RolePermissionMapModel findUserRoleMapPermission(OperationPermissionQO operationPermissionQO) {
        log.info("老门店findUserRoleMapPermission:{}", operationPermissionQO);
        return new RolePermissionMapModel();
    }


    @Override
    public String getTemporaryTokenByAccount(String account) {
        log.info("老门店getTemporaryTokenByAccount:{}", account);
        return null;
    }

    /**
     * 发送验证码
     *
     * @param type 0 短信 1 邮箱
     * @param info 数据
     * @return
     */
    @Override
    public int requestVerification(Integer type, String info) {
        log.info("老门店requestVerification:{},{}", type, info);
        return 0;
    }

    /**
     * 查询验证码
     *
     * @param code 验证码
     * @param info 数据
     * @return
     */
    @Override
    public int validateVerification(String code, String info) {
        log.info("老门店validateVerification:{},{}", code, info);
        return 0;
    }

    /**
     * 通过企业查询运营主体
     *
     * @param teamId 企业id
     * @return 运营主体
     */
    @Override
    public List<OperSubjectInfo> queryOperatingSubjectByTeam(String teamId) {
        log.info("老门店queryOperatingSubjectByTeam:{}", teamId);
        return Lists.newArrayList();
    }

    /**
     * 获取系统权限列表
     *
     * @param operationPermissionRequest 请参参数
     * @return 操作结果
     */
    @Override
    public List<MemberSystemPermissionDTO> getSystemPermissionList(OperationPermissionQO operationPermissionRequest) {
        log.info("老门店getSystemPermissionList:{}", operationPermissionRequest);
        return new ArrayList<>();
    }

    /**
     * @description: 查询用户角色
     * @author: li ao
     * @date: 2024/3/19 10:57
     * @param: enterpriseGuid
     * @param: tel
     * @return: com.holderzone.member.common.dto.holder.RoleAndPostIdDTO
     **/
    @Override
    public RoleAndPostIdDTO findUserRoleAndPost(String enterpriseGuid, String tel) {
        log.info("老门店findUserRoleAndPost:{},{}", enterpriseGuid, tel);
        return new RoleAndPostIdDTO();
    }

    /**
     * @description: 查询商店分页信息
     * @author: li ao
     * @date: 2024/3/19 10:57
     * @param: enterpriseGuid
     **/
    @Override
    public PageModel<TeamStoreInfoModel> findStorePageInfo(TeamStoreInfoPageParamModel teamStoreInfoPageParamModel) {
        log.info("老门店findStorePageInfo:{}", teamStoreInfoPageParamModel);
        return new PageModel<>();
    }

    /**
     * 查询用户下权限范围
     *
     * @return
     */
    @Override
    public List<String> queryUserPermission(Long teamId, String account, Integer permissionType, Long typeId, String token) {
        log.info("老门店queryUserPermission:{},{},{},{},{}", teamId, account, permissionType, typeId, token);
        return new ArrayList<>();
    }

    /**
     * holder通过账号获取临时token
     */
    @Override
    public String getTemporaryToken(String phoneNum) {
        log.info("老门店getTemporaryToken:{}", phoneNum);
        return null;
    }

    @Override
    public List<StoreListVO> getStoreByTeam(Long teamInfoId) {
        log.info("老门店getStoreByTeam:{}", teamInfoId);
        return new ArrayList<>();
    }

    @Override
    public StoreListVO getStoreDetail(Long id) {
        log.info("老门店getStoreDetail:{}", id);
        return null;
    }

    @Override
    public void addAndUpdateStore(AddAndUpdateIPaasDTO addAndUpdateIPaas) {
        log.info("老门店addAndUpdateStore:{}", addAndUpdateIPaas);
        // TODO document why this method is empty
    }

    @Override
    public HeaderUserInfo queryUserInfoDetail(String account) {
        log.info("老门店queryUserInfoDetail:{}", account);
        return null;
    }

    @Override
    public MemberSystemPermissionVO getAccountPermission() {
        MemberSystemPermissionVO permissionVO = new MemberSystemPermissionVO();
        //查询门店配置跳转到营销中心的权限
        final FeignModel<List<SassMenuVo>> sourceByMenu = saasStoreFeign.getPageUrlByMenu(Maps.newHashMap("data", memberCenter));
        if (!CollectionUtils.isEmpty(sourceByMenu.getData())) {
            List<SassMenuVo> sassMenuList = sourceByMenu.getData();
            final List<MemberSystemPermissionDTO> permissionDTOList = permissionConverter(sassMenuList);
            permissionVO.setMemberSystemPermissionDTOs(permissionDTOList);
        }
        //运营主体权限
        final List<OperSubjectInfo> operatingSubject = queryOperatingSubject();
        if (!CollectionUtils.isEmpty(operatingSubject)) {
            final List<PermissionModelDTO> operatingSubjectList = operatingSubject.stream().map(o -> new PermissionModelDTO().setId(o.getOperSubjectGuid())
                    .setPermissionName(o.getMultiMemberName())
                    .setIsChecked(BooleanEnum.TRUE.getCode())).collect(Collectors.toList());
            permissionVO.setPermissionModelDTOS(operatingSubjectList);
        }
        return permissionVO;
    }

    @Override
    public List<ApplyTypeVO> getApplyBusiness() {
        log.info("老门店getApplyBusiness");
        return memberBaseFeign.getBusinessType().getData();
    }

    @Override
    public List<ApplyTypeVO> getAllApplyBusiness() {
        log.info("老门店getApplyBusiness");
        return memberBaseFeign.getBusinessType().getData();
    }

    @Override
    public List<ApplyTypeVO> getApplyTerminal() {
        log.info("老门店getApplyTerminal");
        return new ArrayList<>();
    }

    @Override
    public List<ApplyTypeVO> getAllApplyTerminal() {
        log.info("老门店getApplyTerminal");
        return new ArrayList<>();
    }

    @Override
    public String getBusinessName(Integer code) {
        log.info("老门店getBusinessName:{}", code);
        return ConsumptionOrderTypeEnum.getDesByCode(code);
    }

    @Override
    public String getSourceTypeEnum(int code) {
        return SourceTypeEnum.getMsgByCode(code);
    }

    @Override
    public String getOrderTypeEnum(int code) {
        return OrderTypeEnum.getByCode(code);
    }

    @Override
    public String getOrderSourceEnum(int code) {
        return OrderSourceEnum.getByCode(code);
    }

    @Override
    public SubjectListVO findSubjectDetail(Integer id) {
        log.info("老门店findSubjectDetail:{}", id);
        return null;
    }

    /**
     * 当前权限转换为，旧会员系统，应用层权限封装
     * SYSTEM_PERMISSION、APPLICATION_PERMISSION 两个常量为旧会员营销中心权限
     * 如果后面需要更改，那么这两个常量也需要同步修改
     *
     * @param sassMenuList 操作权限信息
     * @return 旧会员营销中心权限信息
     */
    private List<MemberSystemPermissionDTO> permissionConverter(List<SassMenuVo> sassMenuList) {
        if (CollUtil.isEmpty(sassMenuList)) {
            return CollUtil.newArrayList();
        }

        List<MemberSystemPermissionDTO> permissionDTOList = new ArrayList<>();
        List<PermissionModelDTO> permissionModelList = new ArrayList<>();
        String moduleName = "";
        for (SassMenuVo sassMenuVo : sassMenuList) {
            if (!moduleName.equals(sassMenuVo.getModuleName())) {
                moduleName = sassMenuVo.getModuleName();
                MemberSystemPermissionDTO systemPermission = new MemberSystemPermissionDTO();
                //todo 统一规则，如： 随行红包管理->随行红包->列表查询
                systemPermission.setName(moduleName + "管理");
                systemPermission.setIsFormPermissions(BooleanEnum.FALSE.getCode());

                FunctionModelDTO functionModel = new FunctionModelDTO();
                functionModel.setFunctionName(moduleName);
                functionModel.setIsFormPermissions(BooleanEnum.FALSE.getCode());
                permissionModelList = new ArrayList<>();
                functionModel.setPermissionModels(permissionModelList);
                systemPermission.setFunctionModels(Collections.singletonList(functionModel));

                permissionDTOList.add(systemPermission);
            }
            PermissionModelDTO permissionModel = new PermissionModelDTO();
            permissionModel.setPermissionName(sassMenuVo.getSourceName());
            permissionModel.setIsChecked(BooleanEnum.TRUE.getCode());
            permissionModel.setId(sassMenuVo.getSourceGuid());
            permissionModelList.add(permissionModel);
        }
        return permissionDTOList;
    }
}
