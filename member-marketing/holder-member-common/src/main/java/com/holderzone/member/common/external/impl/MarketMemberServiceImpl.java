package com.holderzone.member.common.external.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.activity.MemberBaseActivityDTO;
import com.holderzone.member.common.dto.card.ResponseCardInfoDTO;

import com.holderzone.member.common.dto.certificate.CertifiedCouponDTO;
import com.holderzone.member.common.dto.certificate.SendCertifiedStampsDTO;
import com.holderzone.member.common.dto.coupon.ResponseGiftCouponDTO;
import com.holderzone.member.common.dto.excel.MemberUploadExcelVO;
import com.holderzone.member.common.dto.member.MemberQueryDTO;
import com.holderzone.member.common.dto.member.TableBasicDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.coupon.CouponActivityStateEnum;
import com.holderzone.member.common.enums.coupon.CouponPackageTypeEnum;
import com.holderzone.member.common.enums.gift.GiftTypeEnum;
import com.holderzone.member.common.enums.RegisterChannelEnum;
import com.holderzone.member.common.enums.member.LabelConnectTypeEnum;
import com.holderzone.member.common.enums.member.LabelTypeEnum;
import com.holderzone.member.common.enums.member.MarketConsumptionOrderTypeEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.common.external.ExternalMemberService;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.qo.activity.MemberBaseActivityQO;
import com.holderzone.member.common.qo.activity.RequestLabelBaseQO;
import com.holderzone.member.common.qo.card.QueryCardInfoPageQO;
import com.holderzone.member.common.qo.gift.RequestQueryMemberBaseQO;
import com.holderzone.member.common.qo.gift.RequestQueryVolumeBaseQO;
import com.holderzone.member.common.qo.member.*;

import com.holderzone.member.common.vo.activity.IslandCouponDTO;
import com.holderzone.member.common.vo.base.ResponseOperationLabel;

import com.holderzone.member.common.vo.card.QueryCardInfoPageVO;
import com.holderzone.member.common.vo.coupon.ResponseCouponActivityVO;
import com.holderzone.member.common.vo.grade.QueryMemberGradeVO;
import com.holderzone.member.common.vo.member.MemberBasicInfoVO;
import com.holderzone.member.common.vo.member.MemberInfoVO;
import com.holderzone.member.common.vo.member.MemberLabelListVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.holderzone.member.common.qo.coupon.CouponPageQO;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MarketMemberServiceImpl implements ExternalMemberService {

    private final MemberBaseFeign memberBaseFeign;

    private final MemberMarketingFeign memberMarketingFeign;

    @Override
    public List<TableBasicDTO> queryTable(String storeGuid, List<String> tableGuidList) {
        return Collections.emptyList();
    }

    @Override
    public QueryMemberGradeVO queryGrowthValueTaskDetail(String roleType, Integer gradeType) {
        return memberBaseFeign.queryGrowthValueTaskDetail(roleType, gradeType).getData();
    }

    @Override
    public List<ResponseOperationLabel> getLabelList(RequestLabelBaseQO requestLabelDTO) {
        MemberLabelListQO memberLabelListQO = new MemberLabelListQO();
        memberLabelListQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        memberLabelListQO.setPageSize(NumberConstant.NUMBER_999999);
        memberLabelListQO.setCurrentPage(NumberConstant.NUMBER_1);
        if (StringUtils.isNotBlank(requestLabelDTO.getLabelName())) {
            memberLabelListQO.setLabelName(requestLabelDTO.getLabelName());
        }

        Result<PageResult> result = memberBaseFeign.listMemberLabel(memberLabelListQO);
        if (result == null || result.getData() == null || CollectionUtils.isEmpty(result.getData().getRecords())) {
            return Collections.emptyList();
        }

        List<MemberLabelListVO> labelList = JSON.parseArray(JSON.toJSONString(result.getData().getRecords()), MemberLabelListVO.class);
        return labelList.stream()
                .map(label -> {
                    ResponseOperationLabel responseLabel = new ResponseOperationLabel();
                    responseLabel.setLabelName(label.getLabelName());
                    responseLabel.setGuid(label.getGuid());
                    responseLabel.setLabelType(label.getLabelType());
                    responseLabel.setGmtCreate(label.getGmtCreate());
                    responseLabel.setMemberNum(label.getRelationCount());
                    return responseLabel;
                })
                .sorted(Comparator.comparing(ResponseOperationLabel::getGmtCreate).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public boolean saveAutomaticLabel(RequestLabelSetting req) {
        RequestOperationLabel requestOperationLabel = new RequestOperationLabel();
        requestOperationLabel.setRequestLabelSetting(new RequestLabelSetting()
                .setLabelName(req.getLabelName())
                .setLabelType(LabelTypeEnum.MANUAL.getCode())
                .setConditionSet(1)
                .setRemark(req.getRemark()));
        log.info("保存标签入参:{}", JacksonUtils.writeValueAsString(requestOperationLabel));
        memberBaseFeign.saveOrUpdateAutomaticLabel(requestOperationLabel);
        return true;
    }

    @Override
    public void addMemberInfoLabel(AddMemberLabelCorrelationQO addMemberLabelCorrelationQO) {
        addMemberLabelCorrelationQO.setBatchId(UUID.randomUUID().toString());
        addMemberLabelCorrelationQO.setConnectionType(LabelConnectTypeEnum.AUTOMATION.getCode());
        log.info("标签关联开始：{}", JSON.toJSONString(addMemberLabelCorrelationQO));
        memberBaseFeign.addMemberInfoLabel(addMemberLabelCorrelationQO);
    }

    @Override
    public void removeMemberInfoLabel(RemoveMemberLabelCorrelationQO removeMemberLabelCorrelationQO) {
        UpdateLabelCorrelationStatusQO qo = new UpdateLabelCorrelationStatusQO();
        qo.setIsConnection(0);
        qo.setLabelGuid(removeMemberLabelCorrelationQO.getLabelGuid());
        qo.setMemberGuid(removeMemberLabelCorrelationQO.getMemberInfoGuid().get(0));
        memberBaseFeign.updateCorrelationStatus(qo);
    }

    @Override
    public MemberBasicInfoVO getMemberInfo(MemberQueryDTO queryDTO) {
        return memberBaseFeign.getMemberInfo(queryDTO).getData();
    }

    @Override
    public String getMemberName(MemberQueryDTO queryDTO) {
        Result<MemberBasicInfoVO> memberInfo = memberBaseFeign.getMemberInfo(queryDTO);
        MemberBasicInfoVO data = memberInfo.getData();
        if (Objects.isNull(data)) {
            return null;
        }
        return data.getUserName();
    }

    @Override
    public Page<IslandCouponDTO> queryVolumeList(RequestQueryVolumeBaseQO request) {
        Page<IslandCouponDTO> page = new Page<>();

        CouponPageQO couponPageQO = new CouponPageQO();
        BeanUtils.copyProperties(request, couponPageQO);
        couponPageQO.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        couponPageQO.setPageSize(request.getPageSize());
        couponPageQO.setCurrentPage(request.getCurrentPage());
        couponPageQO.setActivityState(CouponActivityStateEnum.UNDER_WAY.getCode());

        Result<PageResult<ResponseCouponActivityVO>> result = memberMarketingFeign.couponPageQuery(couponPageQO);
        log.info("分页查询优惠券返回结果==============>" + JSON.toJSONString(result));

        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            return page;
        }

        PageResult<ResponseCouponActivityVO> pageResult = result.getData();
        List<ResponseCouponActivityVO> records = pageResult.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            return page;
        }

        List<IslandCouponDTO> islandCouponDTOs = records.stream()
                .map(this::convertToIslandCouponDTO)
                .collect(Collectors.toList());

        page.setData(islandCouponDTOs);
        page.setCurrentPage(pageResult.getCurrent());
        page.setTotalCount(pageResult.getTotal());
        page.setPageSize(pageResult.getSize());

        return page;
    }

    @Override
    public void sendMemberCoupon(SendCertifiedStampsDTO sendCertifiedStampsDTO) {
        List<CertifiedCouponDTO> certifiedCouponDTOS = sendCertifiedStampsDTO.getCertifiedCouponDTOS();
        if (CollectionUtils.isEmpty(certifiedCouponDTOS)) {
            return;
        }
        List<CertifiedCouponDTO> memberCouponList = certifiedCouponDTOS.stream()
                .filter(e -> Objects.equals(GiftTypeEnum.GIFT_MEMBER_COUPON.getCode(), e.getGiftType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(memberCouponList)) {
            return;
        }

        MemberSendCouponQO sendCouponQO = new MemberSendCouponQO();
        // 会员信息
        MemberInfoCouponQO memberInfoCouponQO = new MemberInfoCouponQO();
        MemberQueryDTO memberQueryDTO = new MemberQueryDTO();
        memberQueryDTO.setPhoneNum(sendCertifiedStampsDTO.getPhoneNumber());
        Result<MemberBasicInfoVO> memberInfo = memberBaseFeign.getMemberInfo(memberQueryDTO);
        log.info("memberInfo返回:{}", JacksonUtils.writeValueAsString(memberInfo));
        MemberBasicInfoVO memberBasicInfoVO = memberInfo.getData();
        if (Objects.isNull(memberBasicInfoVO) || StringUtils.isEmpty(memberBasicInfoVO.getGuid())) {
            log.error("会员信息不存在，phoneNum:{}", sendCertifiedStampsDTO.getPhoneNumber());
            return;
        }
        memberInfoCouponQO.setMemberGuid(memberBasicInfoVO.getGuid());
        memberInfoCouponQO.setMemberName(memberBasicInfoVO.getUserName());
        memberInfoCouponQO.setMemberPhone(memberBasicInfoVO.getPhoneNum());
        sendCouponQO.setMemberInfoCouponQOS(Lists.newArrayList(memberInfoCouponQO));

        // 查询优惠券信息
        CouponPageQO couponPageQO = new CouponPageQO();
        couponPageQO.setCurrentPage(1);
        couponPageQO.setPageSize(NumberConstant.NUMBER_9999999);
        couponPageQO.setGuidList(memberCouponList.stream().map(e -> String.valueOf(e.getCouponId())).collect(Collectors.toList()));
        Result<PageResult<ResponseCouponActivityVO>> couponResult = memberMarketingFeign.couponPageQuery(couponPageQO);
        log.info("查询优惠券返回结果==============>" + JSON.toJSONString(couponResult));
        PageResult<ResponseCouponActivityVO> couponPageResult = couponResult.getData();
        if (Objects.isNull(couponPageResult)) {
            log.error("优惠券不存在，入参:{}", couponPageQO);
            return;
        }
        List<ResponseCouponActivityVO> couponPages = couponPageResult.getRecords();
        Map<String, ResponseCouponActivityVO> couponPageMap = couponPages.stream()
                .collect(Collectors.toMap(ResponseCouponActivityVO::getGuid, Function.identity(), (key1, key2) -> key1));

        boolean customizeCouponTime = Objects.nonNull(sendCertifiedStampsDTO.getStartTime()) && Objects.nonNull(sendCertifiedStampsDTO.getEndTime());

        List<ResponseGiftCouponDTO> couponGuidList = Lists.newArrayList();
        for (CertifiedCouponDTO couponDTO : memberCouponList) {
            ResponseCouponActivityVO responseCouponActivityVO = couponPageMap.get(String.valueOf(couponDTO.getCouponId()));
            if (Objects.isNull(responseCouponActivityVO)) {
                log.error("优惠券不存在，couponId:{}", couponDTO.getCouponId());
                continue;
            }
            ResponseGiftCouponDTO responseGiftCouponDTO = new ResponseGiftCouponDTO();
            BeanUtils.copyProperties(responseCouponActivityVO, responseGiftCouponDTO);
            responseGiftCouponDTO.setNum(couponDTO.getCouponNum());

            // 自定义优惠券开始结束时间
            if (customizeCouponTime) {
                responseGiftCouponDTO.setEffectiveType(0);
                responseGiftCouponDTO.setCouponEffectiveStartTime(sendCertifiedStampsDTO.getStartTime().atStartOfDay());
                responseGiftCouponDTO.setCouponEffectiveEndTime(sendCertifiedStampsDTO.getEndTime().atTime(LocalTime.MAX));
            }
            couponGuidList.add(responseGiftCouponDTO);
        }
        if (CollectionUtils.isEmpty(couponGuidList)) {
            log.error("没有可发送的优惠券，memberCouponList:{}", memberCouponList);
            return;
        }
        sendCouponQO.setCouponGuidList(couponGuidList);
        sendCouponQO.setCouponPackageType(CouponPackageTypeEnum.CERTIFIED.getCode());
        sendCouponQO.setCouponPackageCode(sendCertifiedStampsDTO.getActivityNumber());
        // 修改source
        updateRequestSource(sendCertifiedStampsDTO.getMemberSource());
        log.info("sendCouponQO:{}", JacksonUtils.writeValueAsString(sendCouponQO));
        memberBaseFeign.sendMemberCoupon(sendCouponQO);
    }

    private void updateRequestSource(Integer source) {
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        headerUserInfo.setSource(source);
        ThreadLocalCache.put(JacksonUtils.writeValueAsString(headerUserInfo));
    }

    private IslandCouponDTO convertToIslandCouponDTO(ResponseCouponActivityVO vo) {
        if (Objects.isNull(vo)) {
            return null;
        }

        IslandCouponDTO dto = new IslandCouponDTO();
        BeanUtils.copyProperties(vo, dto);
        // 设置特定字段，根据实际业务需求进行映射
        dto.setId(vo.getGuid());
        dto.setName(vo.getCouponName());
        return dto;
    }

    @Override
    public Page<ResponseOperationMemberInfo> getOperationMemberInfoPage(RequestQueryMemberBaseQO requestQueryMemberBaseQO) {
        List<ResponseOperationMemberInfo> responseOperationMemberInfos = new ArrayList<>();
        Page<ResponseOperationMemberInfo> operationMemberInfoPage = new Page<>();
        operationMemberInfoPage.setPageSize(requestQueryMemberBaseQO.getPageSize());
        operationMemberInfoPage.setCurrentPage(requestQueryMemberBaseQO.getCurrentPage());
        MemberListQO memberListQo = new MemberListQO();

        BeanUtils.copyProperties(requestQueryMemberBaseQO, memberListQo);

        if (Objects.nonNull(requestQueryMemberBaseQO.getSex())) {
            List<Integer> sex = Arrays.asList(requestQueryMemberBaseQO.getSex());
            memberListQo.setSex(sex.get(0));
        }

        if (CollUtil.isNotEmpty(requestQueryMemberBaseQO.getMemberGuidList())){
            memberListQo.setMemberGuids(requestQueryMemberBaseQO.getMemberGuidList());
        }

        if (CollUtil.isNotEmpty(requestQueryMemberBaseQO.getMemberInfoGradeGuidList())){
            memberListQo.setMemberGradeGuids(requestQueryMemberBaseQO.getMemberInfoGradeGuidList());
        }

        if (Objects.nonNull(requestQueryMemberBaseQO.getAccountState())) {
            memberListQo.setMemberStatus(Boolean.TRUE.equals(requestQueryMemberBaseQO.getAccountState()) ?
                    BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
        }
        memberListQo.setSourceType(requestQueryMemberBaseQO.getSourceTypeList());
        memberListQo.setKeywords(requestQueryMemberBaseQO.getKeyword());
        log.info("零售会员查询入参:{}", JSON.toJSONString(memberListQo));
        Result<PageResult> pageResultResult = memberBaseFeign.listMemberInfo(memberListQo);
        log.info("零售会员查询返参:{}", JSON.toJSONString(pageResultResult));
        if (Objects.isNull(pageResultResult) || Objects.isNull(pageResultResult.getData())) {
            return operationMemberInfoPage;
        }

        PageResult<MemberInfoVO> pageResult = pageResultResult.getData();

        List<MemberInfoVO> responseOperationMemberInfoList = pageResult.getRecords();

        String memberInfo = JSON.toJSONString(responseOperationMemberInfoList);
        responseOperationMemberInfoList = JSON.parseArray(memberInfo, MemberInfoVO.class);

        for (MemberInfoVO memberInfoVO : responseOperationMemberInfoList) {
            ResponseOperationMemberInfo responseOperationMemberInfo = new ResponseOperationMemberInfo();
            BeanUtils.copyProperties(memberInfoVO, responseOperationMemberInfo);
            responseOperationMemberInfo.setGmtCreate(memberInfoVO.getGmtCreate().toLocalDate());
            responseOperationMemberInfo.setMemberInfoCardName(memberInfoVO.getMemberCard());
            responseOperationMemberInfo.setOperationMemberInfoCardLevelName(memberInfoVO.getMemberLevel());
            responseOperationMemberInfo.setAccountState(BooleanEnum.getCode(memberInfoVO.getAccountState()));
            responseOperationMemberInfo.setGuid(memberInfoVO.getMemberGuid());
            responseOperationMemberInfo.setSourceTypeName(RegisterChannelEnum.getNameByCode(memberInfoVO.getSourceType()));
            responseOperationMemberInfos.add(responseOperationMemberInfo);
        }
        operationMemberInfoPage.setData(responseOperationMemberInfos);
        operationMemberInfoPage.setTotalCount(pageResult.getTotal());

        return operationMemberInfoPage;
    }

    @Override
    public void updateGrowthValue(RequestMemberGrowthValue request) {
        MemberQueryDTO memberQueryDTO = new MemberQueryDTO();
        memberQueryDTO.setPhoneNum(request.getPhoneNum());
        Result<MemberBasicInfoVO> memberInfo = memberBaseFeign.getMemberInfo(memberQueryDTO);
        log.info("memberInfo返回:{}", JacksonUtils.writeValueAsString(memberInfo));
        MemberBasicInfoVO memberBasicInfoVO = memberInfo.getData();
        if (Objects.isNull(memberBasicInfoVO) || StringUtils.isEmpty(memberBasicInfoVO.getGuid())) {
            log.error("会员信息不存在，phoneNum:{}", request.getPhoneNum());
            return;
        }
        request.setMemberInfoGuidList(Lists.newArrayList(memberBasicInfoVO.getGuid()));
        memberBaseFeign.updateMemberGrowth(request);
    }

    @Override
    public MemberUploadExcelVO memberUploadExcelUrl(String fileUrl) {
        Result<MemberUploadExcelVO> memberUploadExcelUrl = memberBaseFeign.memberUploadExcelUrl(fileUrl, StringConstant.EMPTY);
        log.info("导入新会员返参:{}", JSON.toJSONString(memberUploadExcelUrl));
        if (memberUploadExcelUrl.getCode() == -1) {
            throw new MemberBaseException(memberUploadExcelUrl.getCode(), memberUploadExcelUrl.getMessage());
        }
        return memberUploadExcelUrl.getData();
    }

    @Override
    public String downloadExcelUrl() {
        return null;
    }

    @Override
    public MemberBaseActivityDTO memberBaseDataInfo(MemberBaseActivityQO memberBaseActivityQO) {
        return null;
    }

    @Override
    public List<ResponseCardInfoDTO> getAllCardList() {
        List<ResponseCardInfoDTO> responseCardInfoDTOS = Lists.newArrayList();
        Result<com.baomidou.mybatisplus.extension.plugins.pagination.Page<QueryCardInfoPageVO>>
                queryCardInfoPage = memberBaseFeign.getCardInfoPage(new QueryCardInfoPageQO());

        log.info("获取新会员所有卡返参:{}", JSON.toJSONString(queryCardInfoPage));
        if (Objects.isNull(queryCardInfoPage) || Objects.isNull(queryCardInfoPage.getData())) {
            return responseCardInfoDTOS;
        }

        List<QueryCardInfoPageVO> queryCardInfoPageVOS = queryCardInfoPage.getData().getRecords();

        if (CollUtil.isNotEmpty(queryCardInfoPageVOS)) {
            for (QueryCardInfoPageVO queryCardInfoPageVO : queryCardInfoPageVOS) {
                ResponseCardInfoDTO responseCardInfoDTO = new ResponseCardInfoDTO();
                responseCardInfoDTO.setCardGuid(queryCardInfoPageVO.getCardGuid());
                responseCardInfoDTO.setCardName(queryCardInfoPageVO.getCardName());
                responseCardInfoDTOS.add(responseCardInfoDTO);
            }
        }

        return responseCardInfoDTOS;
    }

    @Override
    public List<Integer> getBusinessScene() {
        return new ArrayList<>();
    }

    @Override
    public String getConsumptionOrderType(Integer orderType) {
        return MarketConsumptionOrderTypeEnum.getDesByCode(orderType);
    }
}
