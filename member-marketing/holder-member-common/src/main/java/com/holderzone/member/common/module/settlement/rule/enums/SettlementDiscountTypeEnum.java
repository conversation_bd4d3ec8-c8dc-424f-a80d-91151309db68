package com.holderzone.member.common.module.settlement.rule.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2023-09-02
 * @description 结算台优惠类型
 */
@AllArgsConstructor
@Getter
public enum SettlementDiscountTypeEnum {

    /**
     * 单品级
     */
    SINGLE_ITEM(0, "单品级优惠"),
    /**
     * 订单级
     */
    ORDER(1, "订单级优惠"),
    /**
     * 资产级
     */
    PROPERTY(2, "资产级优惠");

    /**
     * 编码
     */
    private final int code;

    /**
     * 信息
     */
    private final String des;

    /**
     * 根据code获取name
     *
     * @param code code
     * @return 操作结果
     */

    public static String getDesByCode(int code) {
        for (SettlementDiscountTypeEnum anEnum : SettlementDiscountTypeEnum.values()) {
            if (anEnum.getCode() == code) {
                return anEnum.getDes();
            }
        }
        return null;
    }
}
