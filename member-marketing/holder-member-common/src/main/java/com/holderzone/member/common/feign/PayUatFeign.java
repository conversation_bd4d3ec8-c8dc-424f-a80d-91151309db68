package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.qo.wechat.CheckWechatAccountCreditedQO;
import com.holderzone.member.common.vo.feign.PayUatFeignModel;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 聚合支付
 *
 * <AUTHOR>
 * @date 2023/11/13
 **/
@Component
@FeignClient(name = FilterConstant.PAY_UAT, fallbackFactory = PayUatFeign.ServiceFallBack.class, url = "${feign.uat}")
public interface PayUatFeign {

    @PostMapping("/zuul/agg/mchnt/validate")
    @ApiOperation("校验是否匹配")
    PayUatFeignModel<Boolean> checkWechatAccountCredited(@RequestBody CheckWechatAccountCreditedQO enterpriseGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<PayUatFeign> {


        @Override
        public PayUatFeign create(Throwable throwable) {
            return enterpriseGuid -> {
                log.error(StringConstant.HYSTRIX_PATTERN, "checkWechatAccountCredited", enterpriseGuid, ThrowableUtils.asString(throwable));
                throw new ServerException();
            };
        }
    }

}
