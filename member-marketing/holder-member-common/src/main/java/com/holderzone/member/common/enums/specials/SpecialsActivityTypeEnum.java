package com.holderzone.member.common.enums.specials;

import com.holderzone.member.common.vo.activity.QueryFullReductionFoldActivityVO;
import com.holderzone.member.common.vo.specials.QueryLimitSpecialsActivityVO;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * @description: 适用人群类型 0 不限制 1 所有注册会员 2 指定标签会员 3 指定等级会员 4 指定会员
 * @author: rw
 */
public enum SpecialsActivityTypeEnum {

    /**
     * 不限制
     */
    SPECIALS_UNRESTRICTED(0, "不限制"),

    /**
     * 所有注册会员
     */
    SPECIALS_ALL_MEMBER(1, "所有注册会员"),

    /**
     * 指定标签会员
     */
    SPECIALS_LABEL_MEMBER(2, "指定标签会员"),

    /**
     * 指定等级会员
     */
    SPECIALS_GRADE_MEMBER(3, "指定等级会员"),

    /**
     * 指定会员
     */
    SPECIALS_MEMBER(4, "指定会员");

    /**
     * 编号
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

    SpecialsActivityTypeEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public static SpecialsActivityTypeEnum getEnum(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (SpecialsActivityTypeEnum typeEnum : SpecialsActivityTypeEnum.values()) {
            if (typeEnum.code == code) {
                return typeEnum;
            }
        }
        return null;
    }

    public static SpecialsActivityTypeEnum getSpecialsActivityTypeEnum(QueryLimitSpecialsActivityVO activity) {
        SpecialsActivityTypeEnum giftTypeEnum;
        //转化人群
        if (activity.getGroupType()==SpecialsActivityTypeEnum.SPECIALS_UNRESTRICTED.getCode()){
            giftTypeEnum = SpecialsActivityTypeEnum.SPECIALS_UNRESTRICTED;
        } else if (activity.getGroupType()==SpecialsActivityTypeEnum.SPECIALS_ALL_MEMBER.getCode()) {
            giftTypeEnum = SpecialsActivityTypeEnum.SPECIALS_ALL_MEMBER;
        }else {
            if (!StringUtils.isEmpty(activity.getConditionGradeJson())){
                giftTypeEnum = SpecialsActivityTypeEnum.SPECIALS_GRADE_MEMBER;
            } else if (!StringUtils.isEmpty(activity.getConditionLabelJson())) {
                giftTypeEnum = SpecialsActivityTypeEnum.SPECIALS_LABEL_MEMBER;
            }else {
                giftTypeEnum = SpecialsActivityTypeEnum.SPECIALS_MEMBER;
            }
        }
        return giftTypeEnum;
    }

    public static SpecialsActivityTypeEnum getFullActivityTypeEnum(QueryFullReductionFoldActivityVO activity) {
        SpecialsActivityTypeEnum giftTypeEnum;
        //转化人群
        if (activity.getGroupType()==SpecialsActivityTypeEnum.SPECIALS_UNRESTRICTED.getCode()){
            giftTypeEnum = SpecialsActivityTypeEnum.SPECIALS_UNRESTRICTED;
        } else if (activity.getGroupType()==SpecialsActivityTypeEnum.SPECIALS_ALL_MEMBER.getCode()) {
            giftTypeEnum = SpecialsActivityTypeEnum.SPECIALS_ALL_MEMBER;
        }else {
            if (!StringUtils.isEmpty(activity.getConditionGradeJson())){
                giftTypeEnum = SpecialsActivityTypeEnum.SPECIALS_GRADE_MEMBER;
            } else if (!StringUtils.isEmpty(activity.getConditionLabelJson())) {
                giftTypeEnum = SpecialsActivityTypeEnum.SPECIALS_LABEL_MEMBER;
            }else {
                giftTypeEnum = SpecialsActivityTypeEnum.SPECIALS_MEMBER;
            }
        }
        return giftTypeEnum;
    }
}
