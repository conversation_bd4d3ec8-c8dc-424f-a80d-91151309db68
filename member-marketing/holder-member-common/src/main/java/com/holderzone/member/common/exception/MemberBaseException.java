package com.holderzone.member.common.exception;

import com.holderzone.member.common.enums.ResponseBase;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.MemberTerminalExceptionEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 会员基础服务异常
 * @date 2021/8/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberBaseException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    protected int code;
    /**
     * 错误信息
     */
    protected String des;

    public MemberBaseException() {
        super();
    }


    public MemberBaseException(ResponseBase responseBase) {
        super(String.valueOf(responseBase.getCode()));
        this.code = responseBase.getCode();
        this.des = responseBase.getDes();
    }

    public MemberBaseException(ResponseBase responseBase, Throwable cause) {
        super(String.valueOf(responseBase.getCode()), cause);
        this.code = responseBase.getCode();
        this.des = responseBase.getDes();
    }

    public MemberBaseException(String des) {
        super(des);
        this.des = des;
    }

    public MemberBaseException(MemberAccountExceptionEnum exceptionEnums) {
        super(exceptionEnums.getDes());
        this.code = exceptionEnums.getCode();
        this.des = exceptionEnums.getDes();
    }

    public MemberBaseException(MemberTerminalExceptionEnum exceptionEnums) {
        super(exceptionEnums.getDes());
        this.code = exceptionEnums.getCode();
        this.des = exceptionEnums.getDes();
    }


    public MemberBaseException(int code, String des) {
        super(String.valueOf(code));
        this.code = code;
        this.des = des;
    }

    public MemberBaseException(int code, String des, Throwable cause) {
        super(String.valueOf(code), cause);
        this.code = code;
        this.des = des;
    }

}
