package com.holderzone.member.common.enums.malltool;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2023-11-15
 * @description
 */
@Getter
@AllArgsConstructor
public enum MiniProgramEnum {

    WE_CHAT(1, "微信"),
    ALI_PAY(2, "支付宝");
    private final Integer code;
    private final String des;

    public Integer getCode() {
        return code;
    }

    public static MiniProgramEnum getByCode(Integer code) {
        for (MiniProgramEnum programEnum : values()) {
            if (programEnum.getCode().equals(code)) {
                return programEnum;
            }
        }
        return WE_CHAT;
    }

    public static boolean isWechat(Integer code) {
        if (Objects.isNull(code)) {
            return true;
        }
        return WE_CHAT.getCode().equals(code);
    }

    public static boolean isAli(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        return ALI_PAY.getCode().equals(code);
    }
}
