package com.holderzone.member.common.enums.certificate;

import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * @description: 认证条件类型
 * <AUTHOR>
 */
public enum CertificationConditionTypeEnum {
    /**
     * 目的地
     */
    DESTINATION(1,"目的地"),

    /**
     * 始发地
     */
    ORIGIN(2,"始发地"),

    /**
     * 纯数字
     */
    PURE_NUMBERS(3,"纯数字"),

    /**
     * 纯字母
     */
    PURE_LETTERS(4,"纯字母"),

    /**
     * 字母+数字
     */
    LETTER_AND_NUMBER(5,"字母+数字"),

    ERROR_PARAM(-1,"错误参数");

    private final int code;

    private final String des;

    CertificationConditionTypeEnum(int code, String des){
        this.code = code;
        this.des = des;
    }

    public String getDes(){
       return this.des;
    }

    public int getCode(){
        return this.code;
    }

    /**
     * 通过des获取code
     * @param des 描述信息
     * @return code参数
     */
    public int getCodeByDes(String des){
        if (StringUtils.isEmpty(des)) {
            return ERROR_PARAM.getCode();
        }
        CertificationConditionTypeEnum[] conditionTypeEnums = CertificationConditionTypeEnum.values();
        for (CertificationConditionTypeEnum conditionTypeEnum : conditionTypeEnums) {
            if (conditionTypeEnum.getDes().equals(des)) {
                return conditionTypeEnum.getCode();
            }
        }
        return ERROR_PARAM.getCode();
    }

    public String getDesByCode(Integer code){
        if (Objects.isNull(code)) {
            return "";
        }
        CertificationConditionTypeEnum[] conditionTypeEnums = CertificationConditionTypeEnum.values();
        for (CertificationConditionTypeEnum conditionTypeEnum : conditionTypeEnums) {
            if (code.equals(conditionTypeEnum.getCode())) {
                return conditionTypeEnum.getDes();
            }
        }
        return "";
    }
}
