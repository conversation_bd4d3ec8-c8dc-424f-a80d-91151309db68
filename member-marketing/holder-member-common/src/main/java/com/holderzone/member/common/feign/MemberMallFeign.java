package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.base.ResOrderCommodity;
import com.holderzone.member.common.qo.mall.order.MemberSynOrderQO;
import com.holderzone.member.common.vo.commodity.CommodityShareFormatVO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @description: 会员商城远程调用
 */
@FeignClient(name = FilterConstant.MEMBER_MALL, fallbackFactory = MemberMallFeign.ServiceFallBack.class)
public interface MemberMallFeign {


    /**
     * 通过运营主体获取商品分享标签格式
     *
     * @param operSubjectGuid 运营主体
     * @return 商品分享格式
     */
    @GetMapping("/commodity_set/get_share_format")
    Result<CommodityShareFormatVO> getShareFormat(@RequestParam("operSubjectGuid") String operSubjectGuid);

    @ApiOperation("订单商品")
    @GetMapping(value = "/mall_order/find_mall_order_product", produces = "application/json;charset=utf-8")
    List<ResOrderCommodity> findMallBaseOrderProduct(@RequestParam(value = "orderNum") String orderNum);

    /**
     * 初始化运营主体相关数据
     *
     * @param subjectInfoList 运营主体列表
     */
    @PostMapping(value = "/initialize/init_subject_data")
    void initializeSubjectData(@RequestBody List<String> subjectInfoList);

    @ApiOperation("会员信息同步")
    @PostMapping(value = "mall_order/memberSynOrder", produces = "application/json;charset=utf-8")
    void memberSynOrder(@RequestBody MemberSynOrderQO memberSynOrderQO);

    @ApiOperation("商城订单状态更新")
    @GetMapping(value = "/mall_order/updateOrderCondition", produces = "application/json;charset=utf-8")
    void updateOrderCondition();

    /**
     * 订单未支付自动取消
     *
     * @param cancelTime 取消时间
     */
    @PostMapping(value = "/auto/deal/order/cancel", produces = "application/json;charset=utf-8")
    void orderCancel(LocalDateTime cancelTime);

    /**
     * 订单自动收货
     *
     * @param receiveTime 收货时间
     */
    @PostMapping(value = "/auto/deal/order/receive", produces = "application/json;charset=utf-8")
    void orderReceive(LocalDateTime receiveTime);

    /**
     * 订单自动退款
     *
     * @param refundConfirmTime 收货时间
     */
    @PostMapping(value = "/auto/deal/order/refund", produces = "application/json;charset=utf-8")
    void orderRefundConfirm(LocalDateTime refundConfirmTime);


    /**
     * 查询默认模板guid
     */
    @GetMapping(value = "/logistics/template/default")
    String queryDefaultTemplateGuid(@RequestParam("operSubjectGuid") String operSubjectGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MemberMallFeign> {

        @Override
        public MemberMallFeign create(Throwable throwable) {
            return new MemberMallFeign() {

                @Override
                public Result<CommodityShareFormatVO> getShareFormat(String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getShareFormat", operSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ResOrderCommodity> findMallBaseOrderProduct(String orderNum) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "findMallBaseOrderProduct", orderNum, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void initializeSubjectData(List<String> subjectInfoList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "initializeSubjectData", subjectInfoList, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void memberSynOrder(MemberSynOrderQO memberSynOrderQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "memberSynOrder", memberSynOrderQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void updateOrderCondition() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "updateOrderCondition", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void orderCancel(LocalDateTime cancelTime) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "orderCancel", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void orderReceive(LocalDateTime receiveTime) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "orderReceive", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void orderRefundConfirm(LocalDateTime refundConfirmTime) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "orderRefundConfirm", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String queryDefaultTemplateGuid(String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryDefaultTemplateGuid", operSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
