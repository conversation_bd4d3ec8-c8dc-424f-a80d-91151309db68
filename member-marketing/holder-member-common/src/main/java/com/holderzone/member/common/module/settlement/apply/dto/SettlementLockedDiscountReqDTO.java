package com.holderzone.member.common.module.settlement.apply.dto;

import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 已选优惠
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementLockedDiscountReqDTO implements Serializable {


    private static final long serialVersionUID = 3173715868370886269L;
    /**
     * 优惠类型
     *
     * @see SettlementDiscountOptionEnum#code
     */
    @ApiModelProperty(value = "优惠类型")
    @Min(value = 1, message = "优惠类型必传！")
    private Integer discountOption;

    /**
     * 优惠类型guid，大类型guid
     * eg：优惠券 1guid、优惠券 2guid..
     */
    @ApiModelProperty(value = "优惠类型guid")
    private String discountGuid;

    /**
     * 优惠类型具体项的id
     */
    @ApiModelProperty(value = "优惠id")
    private String discountOptionId;

    /**
     * 优惠力度（锁定必传）： eg 多少积分，多少折
     */
    @ApiModelProperty(value = "优惠力度")
    private String discountDynamic;

    /**
     * 优惠金额： 锁定必传
     */
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    private String storeGuid;

    private String storeName;

    /**
     * 使用次数 兑换券必传
     */
    private Integer usedTimes;

    /**
     * 共享互斥关系 0互斥 1共享
     */
    private Integer shareRelation;
}
