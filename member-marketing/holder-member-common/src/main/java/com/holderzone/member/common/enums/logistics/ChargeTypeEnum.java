package com.holderzone.member.common.enums.logistics;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum ChargeTypeEnum {

    UNKNOWN(0, "未知"),
    UNIFICATION(1, "统一运费"),
    FREE_SHIPPING(2, "商家包邮"),
    NUMBER(3, "按件数"),
    WEIGHT(4, "按重量"),
    ;

    private int code;

    private String des;


    public static ChargeTypeEnum getEnumByCode(int code) {
        for (ChargeTypeEnum chargeTypeEnum : ChargeTypeEnum.values()) {
            if (chargeTypeEnum.getCode() == code) {
                return chargeTypeEnum;
            }
        }
        return UNKNOWN;
    }
}
