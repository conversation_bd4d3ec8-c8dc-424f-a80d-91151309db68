package com.holderzone.member.common.enums.permission;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @description: 拥有区分运营主体类型
 */

public enum SubjectPermissionTypeEnum {

    /**
     * 空值
     */
    NULL_PERMISSION(2,"null"),

    /**
     * 全部
     */
    ALL_PERMISSION(1,"all"),

    /**
     * 部分
     */
    PART_PERMISSION(0,"part");

    /**
     * code
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    SubjectPermissionTypeEnum(int code, String des){
        this.code = code;
        this.des = des;
    }

    public String getDesByCode(int code){
        SubjectPermissionTypeEnum[] subjectPermissionTypeEnums = SubjectPermissionTypeEnum.values();
        for (SubjectPermissionTypeEnum subjectPermissionTypeEnum : subjectPermissionTypeEnums) {
            if(subjectPermissionTypeEnum.getCode() == code){
                return subjectPermissionTypeEnum.getDes();
            }
        }
        return "";
    }

    public int getCodeByDes(String message){
        if(StringUtils.isEmpty(message)){
            return -1;
        }
        SubjectPermissionTypeEnum[] subjectPermissionTypeEnums = SubjectPermissionTypeEnum.values();
        for (SubjectPermissionTypeEnum subjectPermissionTypeEnum : subjectPermissionTypeEnums) {
            if(subjectPermissionTypeEnum.getDes().equals(message)){
                return subjectPermissionTypeEnum.getCode();
            }
        }
        return -1;
    }

    public String getDes(){
        return des;
    }

    public int getCode(){
        return code;
    }
}
