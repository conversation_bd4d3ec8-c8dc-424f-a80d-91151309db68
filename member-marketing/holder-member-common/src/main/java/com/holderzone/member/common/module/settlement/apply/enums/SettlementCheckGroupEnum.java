package com.holderzone.member.common.module.settlement.apply.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 选中分组
 *
 * <AUTHOR>
 * @date 2023/10/25
 * @since 1.8
 */
@AllArgsConstructor
@Getter
public enum SettlementCheckGroupEnum {

    /**
     * 选中
     */
    CHECK(-1),

    /**
     * 未选中：不可叠加
     */
    DIS_APPEND(0),

    /**
     * 未选中：叠加，后面会改成具体的option
     */
    APPEND(1);

    /**
     * 编码
     */
    private final int code;
}
