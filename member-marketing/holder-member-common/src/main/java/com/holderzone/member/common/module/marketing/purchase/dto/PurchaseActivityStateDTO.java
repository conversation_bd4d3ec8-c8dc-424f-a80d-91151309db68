package com.holderzone.member.common.module.marketing.purchase.dto;

import com.holderzone.member.common.module.marketing.purchase.enums.PurchaseStateEnum;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;

/**
 * 限购活动状态操作
 *
 * <AUTHOR>
 * @date 2023/11/27
 **/
@Data
public class PurchaseActivityStateDTO {

    /**
     * 活动guid
     */
    @NotEmpty(message = "活动guid不能为空!")
    private String guid;

    /**
     * 活动状态: 2 发布/开启 ，5暂停
     *
     * @see PurchaseStateEnum
     */
    @Min(0)
    private Integer activityState;

    /**
     * 只能开启/发布 暂停
     */
    public void validate() {
        if (activityState != PurchaseStateEnum.PUBLISHED.getCode()
                && activityState != PurchaseStateEnum.STOP.getCode()) {
            throw new IllegalArgumentException("该状态不能更新!");
        }
    }
}
