package com.holderzone.member.common.enums.commodity;

import com.holderzone.member.common.enums.coupon.CouponShowStateEnum;

import java.util.Objects;

/**
 * 零售商品类型
 */
public enum MarketGoodsTypeEnum {

    ADD_CAR_GOOD_TYPE(2,"无码加购"),

    CODELESS_ADD_CAR(3,"快速结账"),

    FAST_PAY(1,"销售中心"),

    SALE_GOODS (11,"扫码加购"),

    REPAST_GOODS (20,"餐饮云商品"),
    ;

    private final int code;

    private final String des;

    MarketGoodsTypeEnum(int code,String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return this.code;
    }

    public String getDes() {
        return this.des;
    }

    public static MarketGoodsTypeEnum getByCode(Integer code) {

        if (Objects.isNull(code)) {
            return null;
        }
        for (MarketGoodsTypeEnum type : MarketGoodsTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

    /**
     * 通过code 获取描述信息
     *
     * @param code 参数值
     * @return 信息描述
     */
    public static String getDesByCode(int code) {
        MarketGoodsTypeEnum[] stateEnums = MarketGoodsTypeEnum.values();
        for (MarketGoodsTypeEnum typeEnum : stateEnums) {
            if (typeEnum.getCode() == code) {
                return typeEnum.getDes();
            }
        }
        return "";
    }
}
