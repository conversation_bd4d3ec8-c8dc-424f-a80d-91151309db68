package com.holderzone.member.common.module.settlement.apply.dto;

import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyDiscountDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 下单锁定优惠
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@Builder
public class SettlementLockedDiscountDTO implements Serializable {

    private static final long serialVersionUID = 3020625655107093187L;

    /**
     * 会员guid
     */
    @ApiModelProperty("会员guid")
    private String memberInfoGuid;

    /**
     * 当前订单号 : 计算时必传
     * orderNum
     */
    @ApiModelProperty("当前订单号")
    private String orderNumber;

    /**
     * 门店名称
     * todo 无？？
     */
    private String storeName;

    /**
     * 门店guid
     */
    private String storeGuid;
    
    /**
     * 主体
     */
    @ApiModelProperty("operSubjectGuid")
    private String operSubjectGuid;

    /**
     * 商品合计
     * 金额计算: 商品a单价 x 数量 + 商品b单价 x 数量 ....
     */
    @ApiModelProperty("商品合计金额")
    private BigDecimal commodityTotalAmount;

    /**
     * 订单优惠金额
     */
    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmount;

    /**
     * 选中类型详情
     */
    private Map<Integer, List<SettlementApplyDiscountDetailVO>> optionCheckDetailMap;
}
