package com.holderzone.member.common.enums.exception;

import com.holderzone.member.common.enums.ResponseBase;

/**
 * author: pantao
 */
public enum MemberLabelExceptionEnum  implements ResponseBase {

    ERROR_NOT_LABEL(120001, "标签不存在"),
    ERROR_BAD_LABEL(120005, "请传入正确的标签名"),

    DUPLICATE_LABEL(120004, "标签名称重复"),

    ERROR_DATA_REQUEST(120003, "请求数据错误"),

    ERROR_REFRESH_LABEL(120002, "30分钟内只能刷新一次"),

    ERROR_PARAM(120006,"参数错误"),

    ;

    /** 错误码 */
    private final int code;

    /** 错误描述 */
    private final String des;

    MemberLabelExceptionEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getDes() {
        return des;
    }
}
