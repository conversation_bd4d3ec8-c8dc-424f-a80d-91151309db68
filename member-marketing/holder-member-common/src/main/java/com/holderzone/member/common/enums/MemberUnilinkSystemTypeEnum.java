package com.holderzone.member.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 会员通-系统类型
 *
 * <AUTHOR>
 * @date 2025/3/28
 */
@Getter
@RequiredArgsConstructor
public enum MemberUnilinkSystemTypeEnum {
    REPAST("餐饮云", "直连餐饮云系统，实现会员/资产/消费数据/营销统一管理，有效降低运营成本。"),

    RETAIL("零售云", "直连零售云系统，实现会员/资产/消费数据/营销统一管理，有效降低运营成本。"),

    MALL("私域商城", "直连私域商城系统，实现会员/资产/消费数据/营销统一管理，有效降低运营成本。");

    /**
     * 系统备注
     */
    private final String systemRemark;

    /**
     * 系统描述
     */
    private final String systemDescription;
}
