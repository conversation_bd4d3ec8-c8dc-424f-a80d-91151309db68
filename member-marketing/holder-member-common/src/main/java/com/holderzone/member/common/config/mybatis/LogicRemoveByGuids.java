package com.holderzone.member.common.config.mybatis;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlScriptUtils;
import com.baomidou.mybatisplus.extension.injector.AbstractLogicMethod;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * @date 2020/05/09 14:46
 * @description RemoveByGuids-sql拼接
 */
public class LogicRemoveByGuids extends AbstractLogicMethod {

    private static final String MAPPER_SQL = "<script>\nDELETE FROM %s WHERE %s in (%s)</script>";
    private static final String MAPPER_METHOD = "removeByGuids";
    private static final String GUID_NAME = "guid";

    public LogicRemoveByGuids() {
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        String sql = String.format(MAPPER_SQL, tableInfo.getTableName(), GUID_NAME,
                SqlScriptUtils.convertForeach("#{guid}", "guids", (String) null, "guid", ","));
        //删除sql
        SqlSource sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, modelClass);
        return this.addUpdateMappedStatement(mapperClass, modelClass, MAPPER_METHOD, sqlSource);
    }
}
