package com.holderzone.member.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;
/**
 * <AUTHOR> ao
 **/
@Data
@Component
@ConfigurationProperties(prefix = "ipaas")
public class IPaasSecretConfig {

    /**
     * 密钥key
     **/
    private String consumerKey;
    /**
     * 密钥value
     **/
    private String consumerSecret;

    /**
     * 服务地址
     **/
    private String url;


}
