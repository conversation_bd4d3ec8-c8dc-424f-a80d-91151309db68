package com.holderzone.member.common.external.impl;

import com.beust.jcommander.internal.Lists;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.PageModel;
import com.holderzone.member.common.dto.holder.RoleAndPostIdDTO;
import com.holderzone.member.common.dto.ipaas.AddAndUpdateIPaasDTO;
import com.holderzone.member.common.dto.mall.TeamStoreInfoModel;
import com.holderzone.member.common.dto.mall.TeamStoreInfoPageParamModel;
import com.holderzone.member.common.dto.permission.MemberSystemPermissionDTO;
import com.holderzone.member.common.dto.permission.OperationPermissionQO;
import com.holderzone.member.common.dto.permission.RolePermissionMapModel;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.dto.user.OperSubjectInfo;
import com.holderzone.member.common.enums.SystemPermissionEnum;
import com.holderzone.member.common.enums.member.ConsumptionOrderTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.enums.order.OrderSourceEnum;
import com.holderzone.member.common.enums.order.OrderTypeEnum;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.external.ExternalBaseService;
import com.holderzone.member.common.feign.HolderFeign;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.qo.permission.OperSubjectUserPermissionQO;
import com.holderzone.member.common.util.verify.ObjectUtil;
import com.holderzone.member.common.vo.base.BusinessDataModel;
import com.holderzone.member.common.vo.base.OperSubjectInfoVO;
import com.holderzone.member.common.vo.equities.ApplyTypeVO;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.ipass.StoreListVO;
import com.holderzone.member.common.vo.ipass.SubjectListVO;
import com.holderzone.member.common.vo.ipass.SubjectVO;
import com.holderzone.member.common.vo.member.MemberSystemPermissionVO;
import com.holderzone.member.common.vo.permission.HsaOperSubjectPermissionVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description holder
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HolderBaseServiceImpl implements ExternalBaseService {

    private final HolderFeign holderFeign;

    @Value("${feign.store}")
    private String storeHost;

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Override
    public HeaderUserInfo queryUserInformation(String enterpriseGuid) {
        if (enterpriseGuid != null) {
            return holderFeign.queryUserInformation(enterpriseGuid).getData();
        }
        return holderFeign.queryUserInformation().getData();
    }

    @Override
    public HeaderUserInfo queryUserInformation(String enterpriseGuid, String token) {
        try {
            if (enterpriseGuid != null) {
                return Optional.ofNullable(holderFeign.queryUserInformation(enterpriseGuid, token).getData())
                        .orElse(new HeaderUserInfo());
            }
            return Optional.ofNullable(holderFeign.queryUserInformation().getData())
                    .orElse(new HeaderUserInfo());
        } catch (Exception e) {
            log.warn("queryUserInformation error", e);
        }
        return new HeaderUserInfo();
    }

    @Override
    public OperSubjectInfoVO getOperatingSubjectInfo() {
        OperSubjectInfoVO operSubjectInfoVO = new OperSubjectInfoVO();
        List<OperSubjectInfo> operSubjectInfoList = holderFeign.queryOperatingSubject(ThreadLocalCache.getEnterpriseGuid()).getDataList();

        HeaderUserInfo userInfo = queryUserInformation(ThreadLocalCache.getEnterpriseGuid());

        BeanUtils.copyProperties(userInfo, operSubjectInfoVO);

        operSubjectInfoVO.setOperSubjectInfos(operSubjectInfoList);
        return operSubjectInfoVO;
    }

    @Override
    public List<HsaOperSubjectPermissionVO> queryOperatingSubjectByEnterpriseGuid(String enterpriseGuid) {
        return null;
    }

    @Override
    public Boolean validateUser(String account, String password) {
        FeignModel<Boolean> model = holderFeign.validateUser(account, password);
        if (model == null || model.getData() == null || !model.getData()) {
            return false;
        }
        return true;
    }

    @Override
    public BusinessDataModel queryBusinessData(String operSubjectGuid) {
        return holderFeign.queryBusinessData(operSubjectGuid).getData();
    }

    @Override
    public BusinessDataModel queryBusinessData(String operSubjectGuid, String token) {
        return holderFeign.queryBusinessData(operSubjectGuid, token).getData();
    }

    @Override
    public List<OperSubjectInfo> queryOperatingSubject(String enterpriseGuid) {
        return holderFeign.queryOperatingSubject(enterpriseGuid).getDataList();
    }

    @Override
    public List<OperSubjectInfo> queryOperatingSubject() {
        return Lists.newArrayList();
    }

    @Override
    public String findBusinessDataAddress(String enterpriseGuid) {
        String address = holderFeign.findBusinessDataAddress(enterpriseGuid).getData();
        return Optional.ofNullable(address).orElse(storeHost);
    }

    @Override
    public List<String> listAllOperationSubjectId() {
        return holderFeign.listAllOperationSubjectId().getDataList();
    }

    @Override
    public Boolean getHasPermissionName(OperationPermissionQO request, String token) {
        return holderFeign.getHasPermissionName(request, token).getData();
    }

    @Override
    public List<MemberSystemPermissionDTO> listSystemPermission(OperationPermissionQO operationPermissionRequest, String token) {
        return holderFeign.getSystemPermissionList(operationPermissionRequest, token).getDataList();
    }

    @Override
    public List<String> listIdentificationNames() {
        return Collections.emptyList();
    }

    @Override
    public List<SubjectVO> listIdentificationSubjects(String identificationName) {
        return Collections.emptyList();
    }

    @Override
    public List<String> listUserRoleIds(OperSubjectUserPermissionQO userPermissionQO, String token) {
        return holderFeign.listUserRoleIds(token, userPermissionQO.getTeamId(), userPermissionQO.getUserId(), userPermissionQO.getIsRole()).getDataList();
    }

    @Override
    public RolePermissionMapModel findUserRoleMapPermission(OperationPermissionQO operationPermissionRequest, String token) {
        return holderFeign.findUserRoleMapPermission(operationPermissionRequest, token);
    }

    /**
     * 获取系统权限列表
     *
     * @param operationPermissionQO 请参参数
     * @return 操作结果
     */
    @Override
    public RolePermissionMapModel findUserRoleMapPermission(OperationPermissionQO operationPermissionQO) {
        return holderFeign.findUserRoleMapPermission(operationPermissionQO);
    }


    @Override
    public String getTemporaryTokenByAccount(String account) {
        return holderFeign.getTemporaryToken(account).getData();
    }

    /**
     * 发送验证码
     * @param type 0 短信 1 邮箱
     * @param info 数据
     * @return
     */
    @Override
    public int requestVerification(Integer type, String info) {
        return holderFeign.requestVerification(type, info).getReturnCode();
    }

    /**
     * 查询验证码
     * @param code 验证码
     * @param info 数据
     * @return
     */
    @Override
    public int validateVerification(String code, String info) {
        return holderFeign.validateVerification(code, info).getReturnCode();
    }

    /**
     * 通过企业查询运营主体
     *
     * @param teamId 企业id
     * @return 运营主体
     */
    @Override
    public List<OperSubjectInfo> queryOperatingSubjectByTeam(String teamId) {
        return holderFeign.queryOperatingSubjectByTeam(teamId).getDataList();
    }

    /**
     * 获取系统权限列表
     *
     * @param operationPermissionRequest 请参参数
     * @return 操作结果
     */
    @Override
    public List<MemberSystemPermissionDTO> getSystemPermissionList(OperationPermissionQO operationPermissionRequest) {
        return holderFeign.getSystemPermissionList(operationPermissionRequest).getDataList();
    }

    /**
     * @description: 查询用户角色
     * @author: li ao
     * @date: 2024/3/19 10:57
     * @param: enterpriseGuid
     * @param: tel
     * @return: com.holderzone.member.common.dto.holder.RoleAndPostIdDTO
     **/
    @Override
    public RoleAndPostIdDTO findUserRoleAndPost(String enterpriseGuid, String tel) {
        FeignModel<RoleAndPostIdDTO> userRoleAndPost = holderFeign.findUserRoleAndPost(enterpriseGuid, tel);
        if (Objects.isNull(userRoleAndPost)) {
            return null;
        }
        return holderFeign.findUserRoleAndPost(enterpriseGuid, tel).getData();
    }

    /**
     * @description: 查询商店分页信息
     * @author: li ao
     * @date: 2024/3/19 10:57
     * @param: enterpriseGuid
     **/
    @Override
    public PageModel<TeamStoreInfoModel> findStorePageInfo(TeamStoreInfoPageParamModel teamStoreInfoPageParamModel) {
        return holderFeign.findStorePageInfo(new TeamStoreInfoPageParamModel(ObjectUtil.objToLong(teamStoreInfoPageParamModel.getTeamId()),
                "", 0, 9999999)).getData();
    }

    /**
     * 查询用户下权限范围
     *
     * @return
     */
    @Override
    public List<String> queryUserPermission(Long teamId, String account, Integer permissionType, Long typeId, String token) {
        return holderFeign.queryUserPermission(teamId, account, permissionType, typeId, token).getDataList();
    }

    /**
     * holder通过账号获取临时token
     */
    @Override
    public String getTemporaryToken(String phoneNum) {
        FeignModel<String> feignModel = holderFeign.getTemporaryToken(phoneNum);
        if (feignModel != null && feignModel.getCode() == 0) {
            return feignModel.getData();
        }
        return "";
    }

    @Override
    public List<StoreListVO> getStoreByTeam(Long teamInfoId) {
        return null;
    }

    @Override
    public StoreListVO getStoreDetail(Long id) {
        return null;
    }

    @Override
    public void addAndUpdateStore(AddAndUpdateIPaasDTO addAndUpdateIPaas) {

    }

    @Override
    public HeaderUserInfo queryUserInfoDetail(String account) {
        return null;
    }

    @Override
    public MemberSystemPermissionVO getAccountPermission() {
        return memberMarketingFeign.getHolderAccountPermission(SystemPermissionEnum.MARKETING_PERMISSION.getDes());
    }

    @Override
    public List<ApplyTypeVO> getApplyBusiness() {
        log.info("Holder getApplyBusiness");
        return memberBaseFeign.getBusinessType().getData();
    }

    @Override
    public List<ApplyTypeVO> getAllApplyBusiness() {
        log.info("Holder getApplyBusiness");
        return memberBaseFeign.getBusinessType().getData();
    }

    @Override
    public List<ApplyTypeVO> getApplyTerminal() {
        log.info("Holder getApplyTerminal");
        return new ArrayList<>();
    }

    @Override
    public List<ApplyTypeVO> getAllApplyTerminal() {
        log.info("Holder getApplyTerminal");
        return new ArrayList<>();
    }

    @Override
    public String getBusinessName(Integer code) {
        log.info("Holder getBusinessName:{}", code);
        return ConsumptionOrderTypeEnum.getDesByCode(code);
    }

    @Override
    public String getSourceTypeEnum(int code) {
        return SourceTypeEnum.getMsgByCode(code);
    }

    @Override
    public String getOrderTypeEnum(int code) {
        return OrderTypeEnum.getByCode(code);
    }

    @Override
    public String getOrderSourceEnum(int code) {
        return OrderSourceEnum.getByCode(code);
    }

    @Override
    public SubjectListVO findSubjectDetail(Integer id) {
        return null;
    }
}
