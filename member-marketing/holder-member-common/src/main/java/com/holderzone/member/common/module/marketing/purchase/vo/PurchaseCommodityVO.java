package com.holderzone.member.common.module.marketing.purchase.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 限购适用商品
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PurchaseCommodityVO implements Serializable {

    private static final long serialVersionUID = -6120754654026258074L;
    /**
     * 限购活动guid
     */
    @ApiModelProperty(value = "活动id")
    private String activityId;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * 商品编码
     */
    private String commodityCode;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 单品1 固定2 可选3
     *
     * @see com.holderzone.member.common.enums.growth.CommodityComboTypeEnum
     */
    private Integer comboType;

    /**
     * 商品id
     */
    private String commodityId;


    /**
     * 商品售价
     */
    private String commodityPrice;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 限购数量
     */
    private Integer limitNumber;


    /**
     * 此商品是否存在
     */
    private Integer isExist;
}
