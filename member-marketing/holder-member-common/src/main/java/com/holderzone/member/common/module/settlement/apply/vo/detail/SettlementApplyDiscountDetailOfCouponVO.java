package com.holderzone.member.common.module.settlement.apply.vo.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.member.common.enums.coupon.CouponDisableMsgEnum;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyDiscountDetailVO;
import com.holderzone.member.common.vo.coupon.ResponseCouponCommodityVO;
import com.holderzone.member.common.vo.coupon.ResponseCouponStoreVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 会员已领券
 * <p>
 * 规定了必须返回的字段
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SettlementApplyDiscountDetailOfCouponVO extends SettlementApplyDiscountDetailVO {


    private static final long serialVersionUID = 9188168034523854082L;

    @ApiModelProperty("优惠券单笔限制张数")
    private Integer couponLimitNum = 1;

    /**
     * 优惠券有效开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime couponEffectiveStartTime;
    /**
     * 优惠券有效结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime couponEffectiveEndTime;
    /**
     * 不可用 提示
     *
     * @see CouponDisableMsgEnum
     */
    @ApiModelProperty("不可用 提示")
    private String disableMsg;

    /**
     * 优惠券类型
     *
     * @see com.holderzone.member.common.enums.coupon.CouponTypeEnum
     */
    @ApiModelProperty("优惠券类型")
    private Integer couponType;

    /**
     * 使用门槛
     * 0 无门槛
     * 1 订单满多少可用
     */
    @ApiModelProperty("使用门槛")
    private Integer thresholdType;

    /**
     * 满足金额
     */
    @ApiModelProperty("满足金额")
    private BigDecimal thresholdAmount;

    /**
     * 券金额
     */
    @ApiModelProperty("券金额")
    private BigDecimal couponAmount;

    /**
     * 当前可用最大金额：所有可用商品的总额
     */
    @ApiModelProperty("最大可用金额")
    private BigDecimal maxUseAmount;


    /**
     * 0：全部商品适用 1：部分商品适用 2 不适用商品
     * 用于商品优惠分摊
     *
     * @see com.holderzone.member.common.enums.ApplyCommodityEnum
     */
    private Integer applyCommodity;

    /**
     * 商品集合
     *
     * @see ResponseCouponCommodityVO
     */
    private String applyCommodityJson;

    /**
     * 是否适用于所有门店(1:全部门店；0:部分门店(外关联表))
     */
    private Integer applicableAllStore;

    /**
     * 适用门店集合
     * @see ResponseCouponStoreVO
     */
    private String applicableAllStoreJson;

    /**
     * 优惠金额上限
     * null/0 表示不限制
     */
    private BigDecimal discountAmountLimit;


    /**
     * 已使用次数+已占用
     */
    private Integer usedTimes;

    /**
     * 剩余可兑换次数
     */
    private Integer exchangeTimes;

    /**
     * 兑换次数限制
     */
    private Integer exchangeLimit;

    /**
     * 适用门店集合
     */
    private List<String> applyStoreGuidList;

    /**
     * 可兑换次数
     */
    private Integer limitExchangeTimes;

    /**
     * 单笔订单限用数量
     * 单笔订单最多使用{singleOrderUsedLimit}张此优惠券
     * 当前优惠券限用数量需 ≤ 结算规则限制数量
     */
    private Integer singleOrderUsedLimit;

}
