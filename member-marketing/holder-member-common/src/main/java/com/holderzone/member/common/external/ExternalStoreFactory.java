package com.holderzone.member.common.external;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.member.common.enums.PlatformEnum;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024-03-06
 * @description
 */
@Component
public class ExternalStoreFactory {

    private final ExternalStoreService canteenStoreServiceImpl;

    private final ExternalStoreService saasStoreServiceImpl;

    private final ExternalStoreService marketStoreServiceImpl;

    private final ExternalStoreService mallStoreServiceImpl;

    public ExternalStoreFactory(@Qualifier("canteenStoreServiceImpl") ExternalStoreService canteenStoreServiceImpl,
                                @Qualifier("saasStoreServiceImpl") ExternalStoreService saasStoreServiceImpl,
                                @Qualifier("marketStoreServiceImpl") ExternalStoreService marketStoreServiceImpl,
                                @Qualifier("mallStoreServiceImpl") ExternalStoreService mallStoreServiceImpl) {
        this.canteenStoreServiceImpl = canteenStoreServiceImpl;
        this.saasStoreServiceImpl = saasStoreServiceImpl;
        this.marketStoreServiceImpl = marketStoreServiceImpl;
        this.mallStoreServiceImpl = mallStoreServiceImpl;
    }

    public ExternalStoreService build(PlatformEnum platformEnum) {
        switch (platformEnum) {
            case HOLDER_CANTEEN:
                return canteenStoreServiceImpl;
            case SAAS:
                return saasStoreServiceImpl;
            case PASS_RETAIL:
                return marketStoreServiceImpl;
            case MALL:
                return mallStoreServiceImpl;
            default:
                throw new BusinessException("平台有误");
        }
    }
}
