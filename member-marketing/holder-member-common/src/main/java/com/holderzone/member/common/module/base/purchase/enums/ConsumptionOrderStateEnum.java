package com.holderzone.member.common.module.base.purchase.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 消费订单状态
 *
 * <AUTHOR>
 * @date 2023/12/01
 **/
@Getter
@AllArgsConstructor
public enum ConsumptionOrderStateEnum {


    /**
     * 创建：待支付
     */
    CREATE(0, "待支付"),

    /**
     * 已支付
     */
    PAY(1, "已支付"),

    /**
     * 订单退款
     */
    REFUND(2, "已退款");

    private final int code;

    private final String des;

    public static ConsumptionOrderStateEnum get(Integer code) {
        for (ConsumptionOrderStateEnum anEnum : ConsumptionOrderStateEnum.values()) {
            if (Objects.equals(anEnum.code, code)) {
                return anEnum;
            }
        }
        return null;
    }
}
