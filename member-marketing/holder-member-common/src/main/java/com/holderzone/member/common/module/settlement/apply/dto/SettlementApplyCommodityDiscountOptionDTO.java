package com.holderzone.member.common.module.settlement.apply.dto;

import com.holderzone.member.common.module.settlement.rule.enums.SettlementDiscountOptionEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单优惠平摊到商品
 * todo 订单优惠分摊到每个商品上
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementApplyCommodityDiscountOptionDTO implements Serializable {

    private static final long serialVersionUID = -2782609235995021152L;
    /**
     * 优惠项
     *
     * @see SettlementDiscountOptionEnum
     */
    private Integer discountOption;

    /**
     * 多个数量,优惠总额金额
     */
    private BigDecimal discountFee;

}
