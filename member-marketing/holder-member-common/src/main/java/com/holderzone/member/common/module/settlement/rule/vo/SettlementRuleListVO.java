package com.holderzone.member.common.module.settlement.rule.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.member.common.module.settlement.rule.dto.SettlementRuleStoreDTO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 结算规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
public class SettlementRuleListVO implements Serializable {


    private static final long serialVersionUID = 1591838060113188126L;

    private String guid;
    /**
     * 名称
     */
    private String name;

    /**
     * 核销顺序及规则：0自动 1手动
     */
    private Integer useType;

    /**
     * 业务：0全部 1部分
     */
    private Integer applyBusiness;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 业务数组
     */
    private List<String> applyBusinessList;

    /**
     * 门店：0全部 1部分
     */
    private Integer applicableAllStore;

    /**
     * 应用门店
     */
    private List<SettlementRuleStoreDTO> applicableStore;

    /**
     * 是否默认
     */
    private Integer isDefault;
}
