package com.holderzone.member.common.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.ali.AliAuthorizerInfoDTO;
import com.holderzone.member.common.dto.base.PaySettingDTO;
import com.holderzone.member.common.dto.holder.RoleAndPostIdDTO;
import com.holderzone.member.common.dto.member.PermissionModelDTO;
import com.holderzone.member.common.qo.cloud.OperSubjectCloudQO;
import com.holderzone.member.common.qo.wechat.WeChatConfigInfoQO;
import com.holderzone.member.common.vo.ali.AliAuthorizerInfoVO;
import com.holderzone.member.common.vo.ali.HsaAliAppletInfoVO;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudVO;
import com.holderzone.member.common.vo.tool.MessagesConfigVO;
import com.holderzone.member.common.vo.tool.SendMessagesConfigVO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSystemConfigVO;
import com.holderzone.member.common.vo.unilink.MemberUnilinkSystemVO;
import com.holderzone.member.common.vo.wechat.PaySettingVO;
import com.holderzone.member.common.vo.wechat.WeChatConfigInfoVO;
import com.holderzone.member.common.vo.wx.WechatAuthorizerInfoVO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * <AUTHOR>
 * @description: 会员商城远程调用
 */
@FeignClient(name = FilterConstant.MEMBER_MALL_TOOL, fallbackFactory = MemberMallToolFeign.ServiceFallBack.class)
public interface MemberMallToolFeign {

    @ApiOperation("保存小程序配置")
    @PostMapping("/wechat/config/save")
    Result<Boolean> saveOrUpdateWeChatConfig(@RequestBody WeChatConfigInfoQO request);


    @ApiOperation("初始化消息集合")
    @PostMapping("/msg/initMessagesBySubjectGuid")
    void initMessagesBySubjectGuid(@RequestBody List<String> operSubjectGuidList);

    @GetMapping("/cloud/query_oper")
    OperSubjectCloudVO queryByOperSubiectGuid(@RequestParam(value = "operSubjectGuid") String operSubiectGuid);

    @GetMapping("/cloud/query_oper/by_multi_oper")
    OperSubjectCloudVO queryByMultiOperSubjectGuid(@RequestParam(value = "multiOperSubjectGuid") String multiOperSubjectGuid);

    @ApiOperation("名称查询模板")
    @GetMapping("/msg/getMessagesConfigByName")
    SendMessagesConfigVO getMessagesConfigByName(@RequestParam(value = "operSubjectGuid") String operSubjectGuid, @RequestParam(value = "name") String name);

    @ApiOperation("获取消息集合")
    @PostMapping("/msg/getMessagesConfig")
    Result<List<MessagesConfigVO>> getMessagesConfig();

    @ApiOperation("获取当前运营主体授权的第三方平台信息")
    @GetMapping("/wx_open/get_authorizer_access_token")
    WechatAuthorizerInfoVO getAuthorizerAccessToken(@RequestParam(value = "operSubjectGuid") String operSubjectGuid);

    @ApiOperation("根据支付宝小程序appId获取授权信息")
    @GetMapping("/alipay_open/getAliAppletInfo/by_auth_appId")
    AliAuthorizerInfoVO getAliAppletInfoByAuthAppId(@RequestParam(value = "authAppId") String authAppId);

    @ApiOperation("根据运营主体guid获取授权信息")
    @GetMapping("/alipay_open/getAliAppletInfo/by_oper_subject_guid")
    AliAuthorizerInfoDTO getAliAppletInfoByOperSubjectGuid(@RequestParam(value = "operSubjectGuid") String operSubjectGuid);

    @ApiOperation("根据运营主体获取支付宝授权信息")
    @GetMapping("/alipay_open/getAppAuthInfo")
    Result<AliAuthorizerInfoVO> getAliAppAuthInfo();

    @ApiOperation("根据小程序appId获取授权信息")
    @GetMapping("/ali_applet_info/get_info")
    HsaAliAppletInfoVO getAliAppletInfoByOper(@RequestParam(value = "operSubjectGuid") String operSubjectGuid);

    @ApiOperation("根据appId获取授权信息")
    @GetMapping("/ali_applet_info/get_appId")
    AliAuthorizerInfoVO getByAuthAppId(@RequestParam(value = "authAppId") String authAppId);

    @ApiOperation("获取小程序配置")
    @GetMapping("/wechat/config/get")
    Result<WeChatConfigInfoVO> queryByOperSubjectGuid();

    @ApiOperation("获取小程序支付配置")
    @PostMapping("/wechat/config/get/pay/setting")
    PaySettingVO getPaySetting(@RequestBody PaySettingDTO paySettingDTO);

    @ApiOperation("获取小程序配置")
    @PostMapping("/wechat/config/get/list")
    Result<List<WeChatConfigInfoVO>> queryByOperSubjectGuidList(@RequestBody List<String> operSubjectGuidList);

    @ApiOperation("权限处理")
    @PostMapping("/permission/toOperSubjectPermission")
    List<PermissionModelDTO> toOperSubjectPermission(@RequestBody RoleAndPostIdDTO roleAndPostIdDTO);

    @ApiOperation("根据运营主体查询授权系统")
    @GetMapping("/member_unilink_system/listByOperSubject")
    Result<List<MemberUnilinkSystemVO>> listUnilinkSystemByOperSubject(@RequestParam("operSubjectGuid") String operSubjectGuid);

    @ApiOperation("查询系统配置")
    @GetMapping("/member_unilink_system_config/listConfig")
    Result<MemberUnilinkSystemConfigVO> listConfig();

    @ApiOperation("餐饮云授权")
    @PostMapping("/cloud/auth")
    Result<Void> cloudAuth(@RequestBody OperSubjectCloudQO operSubjectCloudQO);

    @ApiOperation("餐饮云取消授权")
    @PostMapping("/cloud/cancel_auth")
    Result<Void> cloudCancelAuth(@RequestBody OperSubjectCloudQO operSubjectCloudQO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MemberMallToolFeign> {

        @Override
        public MemberMallToolFeign create(Throwable throwable) {
            return new MemberMallToolFeign() {

                @Override
                public Result<Boolean> saveOrUpdateWeChatConfig(WeChatConfigInfoQO request) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "saveOrUpdateWeChatConfig", request, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void initMessagesBySubjectGuid(List<String> operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "initMessagesConfig", operSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public OperSubjectCloudVO queryByOperSubiectGuid(String operSubiectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryByOperSubiectGuid", operSubiectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public OperSubjectCloudVO queryByMultiOperSubjectGuid(String multiOperSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryByMultiOperSubjectGuid", multiOperSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public SendMessagesConfigVO getMessagesConfigByName(String operSubjectGuid, String name) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getMessagesConfigByName", operSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<MessagesConfigVO>> getMessagesConfig() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getMessagesConfig", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public WechatAuthorizerInfoVO getAuthorizerAccessToken(String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAuthorizerAccessToken", operSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public AliAuthorizerInfoVO getAliAppletInfoByAuthAppId(String authAppId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAliAppletInfoByAuthAppId", authAppId, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public AliAuthorizerInfoDTO getAliAppletInfoByOperSubjectGuid(String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAliAppletInfoByOperSubjectGuid", operSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<AliAuthorizerInfoVO> getAliAppAuthInfo() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAliAppAuthInfo", "", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public HsaAliAppletInfoVO getAliAppletInfoByOper(String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getAliAppletInfoByOper", operSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public AliAuthorizerInfoVO getByAuthAppId(String authAppId) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getByAuthAppId", authAppId, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<WeChatConfigInfoVO> queryByOperSubjectGuid() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryByOperSubjectGuid", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PaySettingVO getPaySetting(PaySettingDTO paySettingDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "getPaySetting", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<WeChatConfigInfoVO>> queryByOperSubjectGuidList(List<String> operSubjectGuidList) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "queryByOperSubjectGuidList", operSubjectGuidList, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PermissionModelDTO> toOperSubjectPermission(RoleAndPostIdDTO roleAndPostIdDTO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "toOperSubjectPermission", roleAndPostIdDTO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<List<MemberUnilinkSystemVO>> listUnilinkSystemByOperSubject(String operSubjectGuid) {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listUnilinkSystemByOperSubject", operSubjectGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<MemberUnilinkSystemConfigVO> listConfig() {
                    log.error(StringConstant.HYSTRIX_PATTERN, "listConfig", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> cloudAuth(OperSubjectCloudQO operSubjectCloudQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, operSubjectCloudQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Result<Void> cloudCancelAuth(OperSubjectCloudQO operSubjectCloudQO) {
                    log.error(StringConstant.HYSTRIX_PATTERN, operSubjectCloudQO, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }

}
