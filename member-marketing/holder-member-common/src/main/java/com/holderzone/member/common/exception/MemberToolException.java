package com.holderzone.member.common.exception;

import com.holderzone.member.common.enums.ResponseBase;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.MemberTerminalExceptionEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @date 2022/12/30 上午11:58
 * @description 商城装修服务异常
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberToolException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    protected int code;
    /**
     * 错误信息
     */
    protected String des;

    public MemberToolException() {
        super();
    }


    public MemberToolException(ResponseBase responseBase) {
        super(String.valueOf(responseBase.getCode()));
        this.code = responseBase.getCode();
        this.des = responseBase.getDes();
    }

    public MemberToolException(ResponseBase responseBase, Throwable cause) {
        super(String.valueOf(responseBase.getCode()), cause);
        this.code = responseBase.getCode();
        this.des = responseBase.getDes();
    }

    public MemberToolException(String des) {
        super(des);
        this.des = des;
    }

    public MemberToolException(MemberAccountExceptionEnum exceptionEnums) {
        super(exceptionEnums.getDes());
        this.code = exceptionEnums.getCode();
        this.des = exceptionEnums.getDes();
    }

    public MemberToolException(MemberTerminalExceptionEnum exceptionEnums) {
        super(exceptionEnums.getDes());
        this.code = exceptionEnums.getCode();
        this.des = exceptionEnums.getDes();
    }


    public MemberToolException(int code, String des) {
        super(String.valueOf(code));
        this.code = code;
        this.des = des;
    }

    public MemberToolException(int code, String des, Throwable cause) {
        super(String.valueOf(code), cause);
        this.code = code;
        this.des = des;
    }

}
