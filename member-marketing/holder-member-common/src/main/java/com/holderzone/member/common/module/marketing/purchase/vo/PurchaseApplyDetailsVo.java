package com.holderzone.member.common.module.marketing.purchase.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 限量抢购活动应用详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-04
 */
@Data
@Accessors(chain = true)
public class PurchaseApplyDetailsVo implements Serializable {


    private static final long serialVersionUID = 2122518132631427365L;
    /**
     * 活动ID
     */
    private Long purchaseId;

    /**
     * guid
     */
    @ApiModelProperty(value = "guid")
    private String guid;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String purchaseName;

    /**
     * 活动guid
     */
    @ApiModelProperty(value = "活动guid")
    private String purchaseGuid;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    /**
     * 订单来源
     */
    @ApiModelProperty(value = "订单来源")
    private Integer orderSource;

    /**
     * 下单用户
     */
    @ApiModelProperty(value = "下单用户")
    private String userName;

    /**
     * 用户手机号
     */
    @ApiModelProperty(value = "用户手机号")
    private String phone;

    /**
     * 来源门店
     */
    @ApiModelProperty(value = "来源门店")
    private String storeName;

    /**
     * 优惠金额
     */
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal orderDiscountAmount;

    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实付金额")
    private BigDecimal orderPaidAmount;

    /**
     * 订单时间
     */
    @ApiModelProperty(value = "订单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNumber;
}
