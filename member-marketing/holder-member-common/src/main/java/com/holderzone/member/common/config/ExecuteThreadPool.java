package com.holderzone.member.common.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @Version 1.0
 * @description 异步工具
 */
@Configuration
public class ExecuteThreadPool {

    @Bean(name = "memberBaseThreadExecutor")
    public Executor executor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(20),
                new ThreadFactoryBuilder().setNameFormat("memberBaseThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "operationLogThreadExecutor")
    public Executor operationLogThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(20),
                new ThreadFactoryBuilder().setNameFormat("operationLogThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "marketingThreadExecutor")
    public Executor marketingThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 50,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("marketingThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "marketingUniteActivityQueryThreadExecutor")
    public Executor marketingUniteActivityQueryThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 50,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("uniteActivityQueryThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "marketingCertifiedThreadExecutor")
    public Executor marketingCertifiedThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 50,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("certifiedThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "marketingNthActivityThreadExecutor")
    public Executor marketingNthActivityThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 50,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("nthActivityThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "memberMallThreadExecutor")
    public Executor memberMallThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 50,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("memberMallThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "memberMallToolThreadExecutor")
    public Executor memberMallToolThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 50,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("memberMallToolThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "memberSettlementThreadExecutor")
    public Executor memberSettlementThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(20),
                new ThreadFactoryBuilder().setNameFormat("memberSettlementThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "merchantHomeQueryThreadExecutor")
    public ExecutorService merchantHomeQueryThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("merchantHomeQueryThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "memberPartnerThreadExecutor")
    public ExecutorService memberPartnerThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("memberPartnerThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "memberPartnerRoomThreadExecutor")
    public ExecutorService memberPartnerRoomThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("memberPartnerRoomThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "memberWxMpThreadExecutor")
    public ExecutorService memberWxMpThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("memberWxMpThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "memberWxMsgThreadExecutor")
    public ExecutorService memberWxMsgThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("memberWxMsgThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "aliPayPassTemplateAddThreadExecutor")
    public ExecutorService aliPayPassTemplateAddThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("aliPayPassTemplateAddThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "aliPayPassInstanceAddThreadExecutor")
    public ExecutorService aliPayPassInstanceAddThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("aliPayPassInstanceAddThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "queryWxCpUserThreadExecutor")
    public ExecutorService queryWxCpUserThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("queryWxCpUserThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "removeWxCpQrCodeThreadExecutor")
    public ExecutorService removeWxCpQrCodeThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("removeWxCpQrCodeThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "addWxCpUserContactThreadExecutor")
    public ExecutorService addWxCpUserContactThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("addWxCpUserContactThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "wxCpUserContactHandlerThreadExecutor")
    public ExecutorService wxCpUserContactHandlerThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("wxCpUserContactHandlerThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "wxCpCallbackHandlerThreadExecutor")
    public ExecutorService wxCpCallbackHandlerThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("wxCpCallbackHandlerThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "shortMessageThreadExecutor")
    public ExecutorService shortMessageThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("shortMessageThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "queryMemberStatisticsThreadExecutor")
    public ExecutorService queryMemberStatisticsThreadExecutor() {
        return new ThreadPoolExecutorMdcWrapper(5, 20,
                2L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("queryMemberStatisticsThreadExecutor-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
