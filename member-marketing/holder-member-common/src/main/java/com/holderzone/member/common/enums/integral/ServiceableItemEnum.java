package com.holderzone.member.common.enums.integral;

import java.util.Objects;

/**
 * 适用项目
 *  10 订单商品金额 20 桌台费  30 配送费 40 运费 100 其他费用
 * <AUTHOR>
 */

public enum ServiceableItemEnum {

    /**
     * 订单商品金额
     */
    ITEM_ORDER_AMOUNT(10,"订单商品金额"),

    /**
     * 桌台费
     */
    ITEM_TABLE_AMOUNT(20,"桌台费"),

    /**
     * 配送费
     */
    ITEM_PHYSICAL_AMOUNT(30,"配送费"),

    /**
     * 运费
     */
    ITEM_FREIGHT_AMOUNT(40,"运费"),

    /**
     * 其他费用
     */
    ITEM_OTHER_AMOUNT(100,"其他费用"),
    ;
    private final int code;

    private final String des;

    ServiceableItemEnum(int code, String des){
        this.code = code;
        this.des = des;
    }

    public int getCode(){
        return code;
    }

    public String getDes(){
        return des;
    }

    public int getCodeByDes(String des){
        if (Objects.isNull(des)) {
            return ITEM_OTHER_AMOUNT.getCode();
        }
        ServiceableItemEnum[] wayEnums = ServiceableItemEnum.values();
        for (ServiceableItemEnum wayEnum : wayEnums) {
            if (wayEnum.getDes().equals(des)) {
                return wayEnum.getCode();
            }
        }
        return ITEM_OTHER_AMOUNT.getCode();
    }


}
