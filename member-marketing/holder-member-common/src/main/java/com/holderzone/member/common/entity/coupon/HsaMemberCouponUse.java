package com.holderzone.member.common.entity.coupon;

import com.holderzone.member.common.base.HsaBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 优惠券使用记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "HsaMemberCouponUse对象", description = "优惠券使用记录")
public class HsaMemberCouponUse extends HsaBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 运营主体GUID
     */
    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    /**
     * 优惠券guid
     */
    @ApiModelProperty(value = "优惠券guid")
    private String memberCouponLinkGuid;

    /**
     * 核销时间：下单时
     */
    @ApiModelProperty(value = "锁定时间")
    private LocalDateTime lockTime;

    /**
     * 核销时间：支付后
     */
    @ApiModelProperty(value = "支付时间")
    private LocalDateTime payTime;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    /**
     * 核销时间
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 核销来源
     * （核销渠道：小程序、一体机、管理后台）
     *
     * @see com.holderzone.member.common.enums.member.SourceTypeEnum
     */
    private Integer source;

    /**
     * 抵扣优惠金额
     * （优惠券抵扣金额）
     */
    private BigDecimal discountAmount;

    /**
     * 订单实付金额
     * （带动消费金额）
     */
    private BigDecimal orderPaidAmount;

    /**
     * 操作人
     */
    private String operatorAccountName;


    /**
     * 发放券码
     */
    private String code;


    /**
     * 优惠券编码
     */
    private String couponCode;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 会员名称
     */
    private String userName;

    /**
     * memberPhone
     */
    private String memberPhone;

    /**
     * 券包编码
     */
    private String couponPackageCode;

    /**
     * 劵包类型对应的：活动名称
     * activityName
     */
    private String couponPackageName;

    private String memberGuid;


    public LocalDateTime getUseTime() {
        return payTime == null ? lockTime : payTime;
    }
}
