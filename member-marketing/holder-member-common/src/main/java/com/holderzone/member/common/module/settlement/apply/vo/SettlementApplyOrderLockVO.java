package com.holderzone.member.common.module.settlement.apply.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * 优惠应用结果
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementApplyOrderLockVO implements Serializable {


    private static final long serialVersionUID = 4866181558120642819L;

    /**
     * 是否锁定成功
     */
    private boolean locked;

    /**
     * 异常结果
     */
    private String errMsg;

    /**
     * 优惠列表
     */
    private List<SettlementApplyOrderVO> orderVos;
}
