package com.holderzone.member.common.config.mybatis;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.extension.injector.LogicSqlInjector;

import java.util.List;

/**
 * @date 2020/05/09 14:44
 * @description 自定义sql注入
 */
public class HolderSqlInjector extends LogicSqlInjector {

    @Override
    public List<AbstractMethod> getMethodList() {
        List<AbstractMethod> methodList = super.getMethodList();
        methodList.add(new LogicQueryByGuid());
        methodList.add(new LogicRemoveByGuids());
        methodList.add(new LogicRemoveByGuid());
        methodList.add(new LogicQueryByGuids());
        methodList.add(new LogicUpdateByGuid());
        return methodList;
    }
}
