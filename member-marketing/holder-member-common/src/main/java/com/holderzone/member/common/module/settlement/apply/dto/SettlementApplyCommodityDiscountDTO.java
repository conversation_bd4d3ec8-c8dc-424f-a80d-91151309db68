package com.holderzone.member.common.module.settlement.apply.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 订单商品优惠
 * todo 订单优惠分摊到每个商品上
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SettlementApplyCommodityDiscountDTO implements Serializable {


    private static final long serialVersionUID = -8980293122973929935L;
    /**
     * 商品id
     */
    @ApiModelProperty("商品id")
    private String commodityId;

    /**
     * 商品编码
     */
    @ApiModelProperty("商品编码")
    private String commodityCode;

    /**
     * 商品数量
     */
    @ApiModelProperty("商品数量")
    private BigDecimal commodityNum;
    /**
     * 商品单价
     */
    @ApiModelProperty("商品单价")
    private BigDecimal commodityPrice;

    /**
     * 商品名称
     * todo 需要缓存，防止过程中 变更
     */
    @ApiModelProperty("商品名称")
    private String commodityName;

    /**
     * 商品优惠明细
     */
    @ApiModelProperty("商品优惠明细")
    private List<SettlementApplyCommodityDiscountOptionDTO> commodityDiscountList;
}
