package com.holderzone.member.common.module.base.purchase.dto;

import com.holderzone.member.common.module.base.purchase.enums.ConsumptionOrderStateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 消费订单取消
 *
 * <AUTHOR>
 * @date 2023/12/4
 * @since 1.8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PurchaseOrderStateDto implements Serializable {

    private static final long serialVersionUID = -3264854561075935132L;
    
    /**
     * 运营主体guid
     */
    @ApiModelProperty(value = "运营主体guid")
    private String operSubjectGuid;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号 ")
    private String orderNumber;

    /**
     * 订单状态：0下单 1支付 2退款
     * @see ConsumptionOrderStateEnum
     */
    @ApiModelProperty(value = "订单状态：0下单 1支付 2退款")
    private Integer orderState;

    /**
     * 下单时为空
     * 支付时通过orderNumber关联 {@link com.holderzone.member.base.entity.member.HsaMemberConsumption#guid}
     */
    @ApiModelProperty(value = "消费订单guid")
    private String consumptionOrderGuid;
}
