package com.holderzone.member.common.config;

import com.holderzone.member.common.util.ExceptionDuplicateFilter;
import com.holderzone.member.common.util.ExceptionLogUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 异常日志配置类
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Configuration
public class ExceptionLogConfig {

    /**
     * 异常去重过滤器
     */
    @Bean
    @ConditionalOnMissingBean
    public ExceptionDuplicateFilter exceptionDuplicateFilter() {
        return new ExceptionDuplicateFilter();
    }

    /**
     * 异常日志工具
     */
    @Bean
    @ConditionalOnMissingBean
    public ExceptionLogUtil exceptionLogUtil(ExceptionDuplicateFilter duplicateFilter) {
        return new ExceptionLogUtil();
    }
}
