package com.holderzone.member.common.enums.permission;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @description: 权限来源类型
 */

public enum SourcePermissionTypeEnum {

    /**
     * 会员
     */
    MEMBER_SOURCE(1,"member"),

    /**
     * 营销
     */
    MARKETING_SOURCE(2,"marketing");

    /**
     * code
     */
    private final int code;

    /**
     * 描述
     */
    private final String des;

    SourcePermissionTypeEnum(int code, String des){
        this.code = code;
        this.des = des;
    }

    public String getDesByCode(int code){
        SourcePermissionTypeEnum[] subjectPermissionTypeEnums = SourcePermissionTypeEnum.values();
        for (SourcePermissionTypeEnum subjectPermissionTypeEnum : subjectPermissionTypeEnums) {
            if(subjectPermissionTypeEnum.getCode() == code){
                return subjectPermissionTypeEnum.getDes();
            }
        }
        return "";
    }

    public int getCodeByDes(String message){
        if(StringUtils.isEmpty(message)){
            return -1;
        }
        SourcePermissionTypeEnum[] subjectPermissionTypeEnums = SourcePermissionTypeEnum.values();
        for (SourcePermissionTypeEnum subjectPermissionTypeEnum : subjectPermissionTypeEnums) {
            if(subjectPermissionTypeEnum.getDes().equals(message)){
                return subjectPermissionTypeEnum.getCode();
            }
        }
        return -1;
    }

    public String getDes(){
        return des;
    }

    public int getCode(){
        return code;
    }
}
