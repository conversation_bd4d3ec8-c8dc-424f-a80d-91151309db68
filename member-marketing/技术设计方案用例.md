---

# P — Problem（问题与目标）

**现状痛点**

* APNs/FCM 天生“不可靠/不保证必达”，设备离线、Token 失效、厂商通道抖动都会导致**推送不可达/失败**。
* 业务上**部分关键信息只靠 Push** 传达，导致**移动端状态与服务端状态不一致**（错过消息、状态不同步）。
* 上线后缺少“**是否最终一致**”的可观测指标和补偿机制。

**目标（业务 + 技术）**

* 关键消息 **最终一致（Eventual Consistency）**，**消息不丢**、**可重放**、**可追溯**。
* 端到端 **TTC（Time-to-Consistency）p95 ≤ 5s**（前台）/ ≤ 60s（后台/弱网）；**漏达率 ≤ 10⁻⁴**。
* 不改变既有 Push 通道，**在其之上增加“增量同步 + 对账”闭环**。
* SDK 低侵入接入，兼容 iOS/Android 的前后台限制。

**非目标**

* 不保证“严格实时”（毫秒级），而是明确“**最终一致 + 有界延迟**”。
* 不将 Push 作为唯一传输通道；**Push 只作唤醒与提示**。

---

# S — Strategy（核心策略与最小闭环）

## 总体思路（最小闭环）

> **服务端“可持久化外发箱（Outbox）” + 客户端“增量同步引擎（Delta Sync）” + “幂等确认（Ack）” + “周期对账（Reconcile）”**
> Push 仅触发唤醒/提示，同步永远可以脱离 Push 自主完成。

### 架构组件

1. **Outbox & Ledger（服务端外发箱/台账）**

   * 每条待下发的信息写入 **Message Ledger**（持久化，带 `message_id`、`user_id`、`device_id`、`seq`、`payload_hash`、`ttl`、`state`）。
   * **Per-user sequence** 或 **Lamport/Version Vector** 保障有序与重放边界。
2. **Fanout 推送器（APNs/FCM 多通道）**

   * 将消息送入 **Push Attempt** 表（记录尝试、返回码、耗时）。
   * 失败不影响一致性；仅影响**TTC**。
3. **Client Delta Sync 引擎（SDK）**

   * **拉取接口**：`GET /sync?since=<cursor>&limit=...` 返回自上次游标以来的增量消息（含删除/撤回事件）。
   * **幂等确认**：`POST /ack` / `POST /batch-ack`（含 `message_id`、`device_id`、`status`）。
   * **去重机制**：以 `message_id` + `device_id` 幂等；本地维护 **Applied Set** / **last_cursor**。
4. **Reconcile 对账器（服务端/客户端双向）**

   * **服务端巡检任务**：找出“预计应达但未确认”的记录，触发补发/通知。
   * **客户端心跳/冷启动**：前台/网络变更/周期触发 **强制增量拉取**。
   * **死信箱（DLQ）**：多次失败的消息进入 DLQ，人工/自动补偿。
5. **In-App Inbox（应用内消息中心）**

   * 作为**业务真相源**在端侧落地，展示所有服务器确认过的消息，支持补拉/重试。
   * 用户即使禁用系统通知，也能在 Inbox 获取完整记录。
6. **Token & 通道治理**

   * **Token 续期/失效校验**、厂商通道故障 **Circuit Breaker**、指数退避重试。
7. **可观测性**

   * **TTC（生成→端确认）**、**Push 成功率**、**漏达率**、**重复率**、**对账修复率**、**消息积压水位**。

### 数据模型（示例）

* `message_ledger`：`message_id (PK) | user_id | device_id? | seq | type | payload | payload_hash | ttl | state | created_at`
* `push_attempt`：`id | message_id | channel | status | code | latency_ms | retried | created_at`
* `ack_event`：`id | message_id | device_id | ack_status(delivered|seen|failed) | ts`
* `device_cursor`：`user_id | device_id | last_seq`
* `dead_letter`：`message_id | reason | last_attempt_at | next_action`

### 关键接口（示例）

* `GET /sync?since=<seq>&limit=<N>` → `[ {message_id, seq, payload, ttl, op(add|update|delete)} ]`
* `POST /ack` → `{message_id, device_id, ack_status}`
* `POST /receipts`（批量）→ `[{message_id, status}]`
* `GET /inbox?cursor=...`（用于 UI 列表）
* （可选）`POST /pull-hint`（服务端主动提示客户端发起拉取）

### 客户端触发同步策略（iOS/Android 兼容）

* **前台可见**：App 启动/切回前台/网络切换 → 立即 `sync`。
* **后台保活**：

  * iOS：`content-available` 静默推送触发短时间后台 `fetch`，配合 **BGTaskScheduler**（不保证频率）；
  * Android：**WorkManager** 周期任务 + 网络/充电条件。
* **显式入口**：通知点击、Inbox 下拉刷新、错误页“重新同步”。

> **结论**：即使所有 Push 都失败，**用户只要在有效期内打开 App 或心跳触发，就能补回全部增量**。

---

# R — Risk（风险与保障）

| 风险           | 说明                | 保障/缓解                                          |
| ------------ | ----------------- | ---------------------------------------------- |
| **消息乱序/重复**  | 多通道、重试导致顺序错乱、重复达端 | `seq` 严格单调 + 端侧以 `message_id` 去重；端渲染按 `seq` 重排 |
| **端侧长时间离线**  | 游标落后太多/TTL 过期     | 端侧全量/分段补拉策略；超 TTL 进入“历史归档”并提示用户                |
| **iOS 后台限制** | 静默推送触发机会有限        | 把 Push 当“锦上添花”；依赖前台/心跳/手动刷新兜底                  |
| **外部通道故障**   | APNs/FCM/厂商通道抖动   | 通道健康探测 + 熔断/退避；必要时**短信/邮件**用于“关键级别消息”          |
| **一致性冲突**    | 客端本地状态与服务端冲突      | 端侧状态**只读展示**，以服务端为权威；需要写入时用 **乐观锁/版本号**        |
| **存储成本/积压**  | 海量消息保留            | 分级 TTL：UI 必需/审计必需/统计必需 分档；冷热分层、压缩、归档           |
| **安全/隐私**    | 敏感数据在消息体中         | Payload 最小化 + 端拉取详情；传输加密；服务端审计日志               |
| **误删/撤回**    | 用户端已读后撤回          | 撤回作为新事件下发；端侧以操作日志重放，保证“所见即所得”的可追溯              |

---

# V — Validation（验证与上线策略）

## 验证矩阵

* **功能**：

  * 单元/集成：Ledger 写入/去重/乱序重排/撤回重放。
  * 端到端：Push 失败 → 前台打开后 N 秒内消息齐全。
* **性能**：

  * 压测：`sync` 接口在 1k–5k QPS 下 **p95 ≤ 100ms**（不含网络）；
  * 队列与 Ledger 的积压/扫描在 1e6 级消息下保持可用。
* **故障演练**：

  * 断开 APNs/FCM、注入 50% Push 失败；
  * 客户端断网 24h，恢复后观察 **TTC** 与缺失率。
  * 服务端局部节点故障，观测对账修复率。
* **安全/合规**：

  * Payload 不含敏感明文；接口鉴权/签名校验；审计轨迹抽查。
* **观测与 SLO**：

  * **SLO1**：消息 **最终达端确认率 ≥ 99.99%/月**
  * **SLO2**：TTC p95（前台）≤ 5s，p99 ≤ 15s
  * **SLO3**：重复显示率 ≤ 0.1%
  * **SLO4**：对账修复率 ≥ 99.9%（24h 内）

## 上线与灰度

1. **双写期**：旧 Push 流程不变，同时写 Ledger 与发 Hint。
2. **小流量灰度**：开启 SDK 的 `sync`，观察 **TTC/漏达率**。
3. **全量放开**：将“关键事件”只通过 Ledger+Sync 达成（Push 仅提示）。
4. **回滚预案**：关闭 `sync` Feature Flag，即刻回退到纯 Push（功能不丢）。

---
