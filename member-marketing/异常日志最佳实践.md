# 异常日志最佳实践指南

## 🎯 目标

防止重复打印堆栈报错信息，建立统一的异常日志记录规范。

## 🛠️ 核心组件

### 1. ExceptionDuplicateFilter（异常去重过滤器）

**功能**：基于异常指纹识别重复异常，在时间窗口内避免重复记录完整堆栈。

**核心特性**：
- 异常指纹生成：基于异常类型、消息和关键堆栈信息
- 时间窗口控制：默认5分钟内相同异常只记录一次完整堆栈
- 自动清理：定期清理过期的异常记录
- 统计功能：记录异常重复次数

### 2. ExceptionLogUtil（统一异常日志工具）

**功能**：提供统一的异常日志记录接口，自动应用去重策略。

**核心方法**：
- `logException()`: 通用异常记录
- `logBusinessException()`: 业务异常记录（通常不需要堆栈）
- `logSystemException()`: 系统异常记录（总是记录堆栈）
- `logFeignException()`: Feign调用异常记录
- `logDatabaseException()`: 数据库操作异常记录

## 📝 使用方法

### 1. 在GlobalExceptionHandler中使用

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    @Autowired
    private ExceptionLogUtil exceptionLogUtil;
    
    @ExceptionHandler(value = MemberBaseException.class)
    public Result<Void> memberBaseExceptionHandler(MemberBaseException e) {
        // 业务异常，通常不需要完整堆栈
        exceptionLogUtil.logBusinessException(logger, "发生业务异常", e);
        return Result.error(e.getCode(), e.getDes());
    }
    
    @ExceptionHandler(value = Exception.class)
    public Result<Void> handleException(Exception e) {
        // 系统异常，需要完整堆栈但应用去重策略
        exceptionLogUtil.logSystemException(logger, "系统异常", e);
        return Result.error("系统异常，请联系管理员!");
    }
}
```

### 2. 在Service层中使用

```java
@Service
@Slf4j
public class PayServiceImpl {
    
    @Autowired
    private ExceptionLogUtil exceptionLogUtil;
    
    public void processPayment(PaymentRequest request) {
        try {
            // 业务逻辑
            doPayment(request);
        } catch (DatabaseException e) {
            // 数据库异常
            exceptionLogUtil.logDatabaseException(log, "插入", "payment_order", e);
            throw new PaymentException("支付处理失败", e);
        } catch (FeignException e) {
            // Feign调用异常
            exceptionLogUtil.logFeignException(log, "payment-service", "createOrder", e);
            throw new PaymentException("调用支付服务失败", e);
        } catch (Exception e) {
            // 其他系统异常
            exceptionLogUtil.logSystemException(log, "支付处理异常", e);
            throw new PaymentException("支付处理失败", e);
        }
    }
}
```

### 3. 在异步任务中使用

```java
@Component
@Slf4j
public class AsyncTaskProcessor {
    
    @Autowired
    private ExceptionLogUtil exceptionLogUtil;
    
    @Async
    public void processAsyncTask(TaskData data) {
        try {
            // 异步任务逻辑
            doAsyncProcess(data);
        } catch (Exception e) {
            // 异步任务异常，使用去重机制避免大量重复日志
            exceptionLogUtil.logException(log, "异步任务处理失败", e);
        }
    }
}
```

## 🔧 配置说明

### 1. 时间窗口配置

```java
// 默认5分钟时间窗口
boolean shouldLog = duplicateFilter.shouldLogFullStack(exception);

// 自定义时间窗口（10分钟）
boolean shouldLog = duplicateFilter.shouldLogFullStack(exception, 10 * 60 * 1000L);
```

### 2. 异常指纹生成规则

异常指纹基于以下信息生成：
- 异常类型（Exception.class.getName()）
- 异常消息（标准化处理，去除动态内容）
- 关键堆栈信息（前3层非系统调用栈）

### 3. 自动清理配置

- 清理频率：每分钟执行一次
- 过期时间：10分钟未出现的异常记录将被清理
- 缓存大小：自动控制，避免内存泄漏

## 📊 日志输出示例

### 首次异常（完整堆栈）
```
2024-12-19 10:30:15.123 ERROR [http-nio-8080-exec-1] [trace-123] c.h.m.b.s.PayServiceImpl.processPayment:45 数据库操作异常 [操作: 插入, 表: payment_order]
java.sql.SQLException: Connection timeout
    at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
    at com.holderzone.member.base.service.PayServiceImpl.processPayment(PayServiceImpl.java:42)
    ...
```

### 重复异常（简化记录）
```
2024-12-19 10:32:20.456 ERROR [http-nio-8080-exec-2] [trace-456] c.h.m.b.s.PayServiceImpl.processPayment:45 数据库操作异常 [操作: 插入, 表: payment_order] [异常重复出现，已忽略堆栈信息，累计次数: 3] - SQLException: Connection timeout
```

## ⚠️ 注意事项

### 1. 不要混用日志记录方式

❌ **错误做法**：
```java
try {
    // 业务逻辑
} catch (Exception e) {
    log.error("异常信息：{}", e.getMessage());
    e.printStackTrace(); // 不要使用printStackTrace()
    exceptionLogUtil.logException(log, "处理异常", e); // 不要重复记录
}
```

✅ **正确做法**：
```java
try {
    // 业务逻辑
} catch (Exception e) {
    exceptionLogUtil.logException(log, "处理异常", e);
    // 或者根据异常类型选择合适的方法
}
```

### 2. 业务异常vs系统异常

- **业务异常**：用户操作导致的预期异常，通常不需要完整堆栈
- **系统异常**：程序错误、环境问题等，需要完整堆栈进行排查

### 3. 异常传播

在catch块中重新抛出异常时，避免重复记录：

```java
try {
    // 调用下层服务
    lowerService.process();
} catch (LowerServiceException e) {
    // 下层已经记录了异常，这里只做业务处理
    throw new UpperServiceException("上层服务处理失败", e);
}
```

## 🚀 迁移指南

### 1. 现有代码迁移步骤

1. **添加依赖**：确保项目包含异常日志工具类
2. **替换异常处理**：逐步替换现有的异常日志记录
3. **测试验证**：验证异常去重功能正常工作
4. **监控观察**：观察日志量减少效果

### 2. 分阶段实施

- **第一阶段**：GlobalExceptionHandler统一改造
- **第二阶段**：Service层关键异常处理改造
- **第三阶段**：全面推广到所有异常处理点

### 3. 效果评估

- 监控生产环境日志量变化
- 统计异常去重效果
- 评估问题排查效率提升

## 📈 预期效果

- **日志量减少**：重复异常日志减少80%以上
- **存储成本降低**：日志存储成本显著下降
- **排查效率提升**：关键异常信息更加突出
- **系统性能改善**：减少日志I/O开销
