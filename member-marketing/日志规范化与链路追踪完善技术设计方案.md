---

# P — Problem（问题与目标）

**现状痛点**

* **日志不规范问题**：
  * Controller层大量使用INFO级别记录请求参数，生产环境日志量过大，影响性能和存储
  * 异常处理中同时使用`log.error()`和`e.printStackTrace()`，重复记录异常信息，日志冗余
  * 缺少关键业务日志，无法追踪业务操作和排查问题
  * 日志记录了用户关键信息导致信息泄露
  * 防止重复打印堆栈报错信息：目前无对应措施

* **链路追踪缺失问题**：
  * 部分业务无链路追踪
  * Interceptor中MDC设置不一致导致链路追踪断链
  * Feign调用链路传递部分场景丢失
  * 异步任务链路丢失，无法关联到原始请求

**目标（业务 + 技术）**

* **日志规范化**：统一日志格式、级别使用规范，**生产环境日志量减少60%**，**敏感信息零泄露**
* **链路完整性**：实现**端到端链路追踪覆盖率 ≥ 95%**，**异步任务链路关联率 ≥ 90%**
* **问题排查效率**：关键业务操作**可追溯率 100%**，**故障定位时间缩短70%**
* **系统可观测性**：建立完善的日志监控和告警机制，**异常感知时间 ≤ 1分钟**

**非目标**

* 不改变现有业务逻辑，**仅优化日志记录和链路追踪机制**
* 不引入重量级APM工具，**基于现有技术栈进行增强**

---

# S — Strategy（核心策略与最小闭环）

## 总体思路（最小闭环）

> **统一日志配置模板 + 分级日志策略 + 完整链路传递 + 敏感信息脱敏 + 异常去重机制**
> 在不影响业务的前提下，建立标准化的日志和链路追踪体系。

### 架构组件

1. **统一日志配置中心**
   * 制定标准的`log4j2-template.xml`配置模板
   * 按环境（dev/test/prod）分级配置日志级别
   * 统一日志格式：`[时间] [级别] [线程] [TraceId] [类名.方法:行号] 消息`

2. **分级日志策略**
   * **DEBUG**：开发环境详细调试信息
   * **INFO**：关键业务节点、状态变更
   * **WARN**：潜在问题、降级操作
   * **ERROR**：异常错误、系统故障

3. **链路追踪增强器**
   * **TraceId生成器**：统一雪花算法生成TraceId
   * **MDC传递器**：确保所有Interceptor统一设置MDC
   * **异步任务装饰器**：异步执行时传递TraceId上下文
   * **Feign链路传递器**：增强FeignInterceptor确保链路不断

4. **敏感信息脱敏器**
   * **字段级脱敏**：手机号、身份证、银行卡等敏感字段自动脱敏
   * **JSON脱敏**：请求参数中敏感信息自动替换为`***`
   * **白名单机制**：可配置的敏感字段白名单

5. **异常去重机制**
   * **异常指纹**：基于异常类型+堆栈信息生成唯一指纹
   * **时间窗口去重**：相同异常在5分钟内只记录一次
   * **计数统计**：记录重复异常的发生次数

6. **业务日志增强**
   * **关键节点埋点**：订单创建、支付、退款等关键业务节点
   * **状态变更记录**：重要实体状态变更的前后对比
   * **用户行为轨迹**：用户关键操作的完整链路

### 数据模型（示例）

* `log_config`：`service_name | env | log_level | pattern | appenders`
* `trace_context`：`trace_id | span_id | parent_span_id | service_name | method_name | start_time | end_time`
* `sensitive_fields`：`field_name | field_type | mask_rule | is_active`
* `exception_fingerprint`：`fingerprint | exception_class | stack_hash | first_occurrence | count | last_occurrence`
* `business_log`：`trace_id | business_type | operation | before_state | after_state | user_id | timestamp`

### 关键接口（示例）

* **日志配置接口**：`GET /log/config/{service}` → 获取服务日志配置
* **链路查询接口**：`GET /trace/{traceId}` → 查询完整调用链路
* **敏感字段配置**：`POST /sensitive/fields` → 配置敏感字段规则
* **异常统计接口**：`GET /exception/stats` → 异常发生统计
* **业务日志查询**：`GET /business/log?traceId=xxx` → 查询业务操作日志

### 实施策略

1. **第一阶段：日志规范化**
   * 统一所有服务的日志配置
   * 调整生产环境日志级别为WARN
   * 实现敏感信息脱敏

2. **第二阶段：链路追踪完善**
   * 统一TraceId生成和传递机制
   * 增强Feign调用链路传递
   * 实现异步任务链路关联

3. **第三阶段：监控告警**
   * 建立日志监控大盘
   * 配置异常告警规则
   * 实现业务指标监控

---

# R — Risk（风险与保障）

| 风险 | 说明 | 保障/缓解 |
|------|------|----------|
| **性能影响** | 日志脱敏、链路传递可能影响性能 | 异步处理敏感信息脱敏；链路传递使用轻量级ThreadLocal |
| **配置变更风险** | 统一修改日志配置可能影响现有功能 | 分批次灰度发布；保留原配置备份；提供快速回滚机制 |
| **链路传递失败** | 异步任务或特殊场景下链路可能丢失 | 实现链路传递失败的兜底机制；记录链路丢失统计 |
| **存储成本增加** | 业务日志增加可能导致存储成本上升 | 实施日志分级存储；设置合理的日志保留期；压缩历史日志 |
| **脱敏规则误判** | 敏感信息脱敏可能影响问题排查 | 提供脱敏规则白名单；保留原始日志的安全访问通道 |
| **监控告警风暴** | 异常去重机制失效可能导致告警风暴 | 实现告警频率限制；提供告警静默机制；分级告警策略 |

---

# V — Validation（验证与上线策略）

## 验证矩阵

* **功能验证**：
  * 单元测试：日志脱敏规则、TraceId生成、异常去重机制
  * 集成测试：端到端链路追踪、Feign调用链路传递
  * 压力测试：高并发下日志性能、链路传递稳定性

* **性能验证**：
  * 日志脱敏性能：单次脱敏耗时 ≤ 1ms
  * 链路传递开销：TraceId传递耗时 ≤ 0.1ms
  * 存储优化效果：生产环境日志量减少 ≥ 60%

* **安全验证**：
  * 敏感信息脱敏覆盖率 ≥ 99%
  * 脱敏规则准确率 ≥ 95%
  * 原始敏感信息泄露率 = 0

* **可观测性验证**：
  * 链路追踪覆盖率 ≥ 95%
  * 异步任务链路关联率 ≥ 90%
  * 异常去重准确率 ≥ 98%

## 上线与灰度策略

1. **准备阶段（1周）**：
   * 制定统一日志配置模板
   * 开发敏感信息脱敏组件
   * 准备链路追踪增强组件

2. **灰度阶段（2周）**：
   * 选择1-2个非核心服务进行试点
   * 验证日志配置和链路追踪效果
   * 收集性能和功能反馈

3. **全量推广（3周）**：
   * 分批次升级所有服务配置
   * 逐步开启敏感信息脱敏
   * 完善监控告警规则

4. **监控优化（持续）**：
   * 持续监控日志量和性能指标
   * 优化脱敏规则和链路传递
   * 完善异常处理和告警机制

## SLO指标

* **SLO1**：生产环境日志量减少 ≥ 60%/月
* **SLO2**：链路追踪覆盖率 ≥ 95%
* **SLO3**：敏感信息脱敏覆盖率 ≥ 99%
* **SLO4**：异常去重准确率 ≥ 98%
* **SLO5**：故障定位时间缩短 ≥ 70%

## 回滚预案

* **配置回滚**：保留原始日志配置，支持一键回滚
* **功能开关**：提供Feature Flag控制脱敏和链路追踪功能
* **监控告警**：实时监控系统性能，异常时自动降级
* **应急处理**：建立应急响应机制，快速处理配置问题

---
